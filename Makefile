all: infrastructure components

folder_name := NA

.PHONY: make_blueprint
make_blueprint:
	echo "Building blueprint..."
	cp -r $(BLUEPRINT_SOURCE_DIR) ./dist
	$(eval folder_name :=  $(notdir $(basename $(BLUEPRINT_SOURCE_DIR))))
	cd ./dist && zip x_$(folder_name).zip $(folder_name)

.PHONY: infrastructure
infrastructure:
	$(MAKE) make_blueprint BLUEPRINT_SOURCE_DIR=src/blueprint/infrastructure/infra_4_poweredge_k8s_ceph
	$(MAKE) make_blueprint BLUEPRINT_SOURCE_DIR=src/blueprint/infrastructure/kubernates
	$(MAKE) make_blueprint BLUEPRINT_SOURCE_DIR=src/blueprint/infrastructure/ubuntu_os
	$(MAKE) make_blueprint BLUEPRINT_SOURCE_DIR=src/blueprint/infrastructure/poweredge
	$(MAKE) make_blueprint BLUEPRINT_SOURCE_DIR=src/blueprint/infrastructure/rocky_linux_os
	$(MAKE) make_blueprint BLUEPRINT_SOURCE_DIR=src/blueprint/infrastructure/slurm
	$(MAKE) make_blueprint BLUEPRINT_SOURCE_DIR=src/blueprint/infrastructure/ceph

.PHONY: components
components:
	$(MAKE) make_blueprint BLUEPRINT_SOURCE_DIR=src/blueprint/components/dashboard
	$(MAKE) make_blueprint BLUEPRINT_SOURCE_DIR=src/blueprint/components/gpu_device_plugin
	$(MAKE) make_blueprint BLUEPRINT_SOURCE_DIR=src/blueprint/components/gpu_driver
	$(MAKE) make_blueprint BLUEPRINT_SOURCE_DIR=src/blueprint/components/gpu_telemetry
	$(MAKE) make_blueprint BLUEPRINT_SOURCE_DIR=src/blueprint/components/istio_service_mesh
	$(MAKE) make_blueprint BLUEPRINT_SOURCE_DIR=src/blueprint/components/kernel_bios_postconfig
	$(MAKE) make_blueprint BLUEPRINT_SOURCE_DIR=src/blueprint/components/kubeflow
	$(MAKE) make_blueprint BLUEPRINT_SOURCE_DIR=src/blueprint/components/ldap
	$(MAKE) make_blueprint BLUEPRINT_SOURCE_DIR=src/blueprint/components/networking_postconfig
	$(MAKE) make_blueprint BLUEPRINT_SOURCE_DIR=src/blueprint/components/nfs_storage_class
	$(MAKE) make_blueprint BLUEPRINT_SOURCE_DIR=src/blueprint/components/pytorch
	$(MAKE) make_blueprint BLUEPRINT_SOURCE_DIR=src/blueprint/components/roce_device_plugin
	$(MAKE) make_blueprint BLUEPRINT_SOURCE_DIR=src/blueprint/components/switch_out_of_band
	$(MAKE) make_blueprint BLUEPRINT_SOURCE_DIR=src/blueprint/components/switch_storage
	$(MAKE) make_blueprint BLUEPRINT_SOURCE_DIR=src/blueprint/components/vllm


.PHONY: application
application:
	$(MAKE) make_blueprint BLUEPRINT_SOURCE_DIR=src/blueprint/application
clean:
	rm -rf ./dist/*