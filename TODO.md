# TODO for POC Phase 3

## Environment
- Env1 - LS40
- Env2 - H100

## Sample Use Case
Precondition - all the components are deployed

Use Case 1: User upload file 
1. User call API to Upload a file
1. Nginux auth the API with keycloak
1. Save the file to object storage
1. save a record in postgres to recorder the file
1. time scheduler job in functional as service to read the postgres db, find the file to process, and embedding the file to vector db


Use Case 2: RAG
1. User invoke Llamindex API with <PERSON>th to talk with RAG
1. Nginux auth the API with keycloak
1. LLamaIndex process the request, LlamaIndex will talk to vector db to get the  top ranked information, then process with Llama3.x to get the response

Use Case3: Monitoring
1. User can monitor the Grafana to get the information, also integrate the comopnents information with otel to promethus

## Setup Stack w/o DSPO on H100
- [x] Ubuntu
- [x] K8s
- [x] Nvidia Driver
- [x] Ceph
- [x] Keycloak
- [x] Gateway - Nginx
- [x] grafara, promethus
- [x] postgres
- [x] VectorDB - qdrant now
- [x] Rabbit MQ
- [x] KNative
- [x] vllm w/ llama3.x
- [x] minio as S3 provider
- [ ] stack - configure gateway with keycloak for auth
- [ ] stack - configure postgres db using storege-class rook-cephfs
- [ ] stack - configure postgres to send matrix to promethus
- [ ] stack - configure vector db to use storageclass rook-cephfs
- [ ] stack - configure vector db to send matrix to promethus
- [ ] sample - yaml configure to create db schema in postgres to store upload file information  
- [ ] sample - faas code for serve file processing with KNative with llamaindex embedding
- [ ] sample - faas configuration to schedule the task
- [ ] sample - code for serve RAG API with llamaindex
- [ ] stack - configure for promethus to monitor services



## Setup Stack w DSPO 
- [ ] DSPO Environment Setup - Using VM environment?
- [ ] OOB Onboarding - Use Existed OOB onboarding BP
- [ ] Base BP - Ubuntu OS Provision - modify 0.5 code to have ISO generated and installed
- [ ] Base BP - Kubernator Setup - modify 0.5 code to create kubernator
- [ ] Base BP - Nvidia Driver - Using Nvidia Operator to install the GPU Driver
- [ ] Base BP - Ceph Installation - with helm installation plugin
- [ ] Base BP - functional as service - Knative 
- [ ] Base BP - Keycloak
- [ ] Base BP - postGreSql
- [ ] Base BP - Elastic
- [ ] Base BP - Nginx
- [ ] Base BP - Promethus, otel, Grafana
- [ ] Base BP - goharbor
- [ ] Base BP - LLM Model with vllm - llama3.x
- [ ] Base BP - Llamaindex for Embedding Model
- [ ] Base BP - Hashicorp Vault
- [ ] Top Level BP for all the BP above
- [ ] Llamaindex Application Sample

- [ ] Base BP - Powerscale driver - replace ceph
- [ ] Base BP - JBOD + RAID - replace ceph
- [ ] PowerProtect





## Reference
- DSPO download - [isgedge.artifactory.cec.lab.emc.com/ui/native/isgedge-generic-local/artifacts/hzp/dev/bundles/eo/v0.7.0.0/](https://isgedge.artifactory.cec.lab.emc.com/ui/native/isgedge-generic-local/artifacts/hzp/dev/bundles/eo/v0.7.0.0/)
- DSP Install Guide - [https://confluence.cec.lab.emc.com/display/ISGPDE/DSP-MS+Install+Bundle](https://confluence.cec.lab.emc.com/display/ISGPDE/DSP-MS+Install+Bundle)
- OOB discovery Patch - [https://confluence.gtie.dell.com/pages/viewpage.action?pageId=1555545980](https://confluence.gtie.dell.com/pages/viewpage.action?pageId=1555545980)
- 0.5 Base BP for Ubuntu - [https://eos2git.cec.lab.emc.com/ISG-Edge/hzp-eo-solutions/tree/pub/DSP-MS0.5/DSP](https://eos2git.cec.lab.emc.com/ISG-Edge/hzp-eo-solutions/tree/pub/DSP-MS0.5/DSP)
