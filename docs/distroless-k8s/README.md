# Distroless and Air Gapped K8s

## Software Artifacts
software components that need to be carried across the air gap in order for this cluster to be stood up:

* Docker (to host an internal container image registry)
* Containerd
* libcgroup
* socat
* conntrack-tools
* CNI plugins
* crictl
* kubeadm
* kubelet
* kubectl
* kubelet.service systemd file
* kubeadm configuration file
* Docker registry container image
* Kubernetes component container images
* CNI network plugin container images (Flannel)
* CNI network plugin manifests
* CNI tooling container images


## **Prepare the Node for K8s**
Run the following steps as the superuser (`root`)
### config Hostname
```

```
### Write to `/etc/sysctl.d/99-k8s-cri.conf`:
```
cat > /etc/sysctl.d/99-k8s-cri.conf << EOF
net.bridge.bridge-nf-call-iptables=1
net.ipv4.ip_forward=1
net.bridge.bridge-nf-call-ip6tables=1
EOF
```
### Write to `/etc/modules-load.d/k8s.conf` (enable `overlay` and `nbr_netfilter`):
```
echo -e overlay\\nbr_netfilter > /etc/modules-load.d/k8s.conf
```
Install iptables:
Fedora:
```
dnf -y install iptables-legacy
```
Ubuntu:
```
apt install -y iptables
```
### Set iptables to use legacy mode (not `nft` iptables), since k8s does node support `nftables`:
```
update-alternatives --set iptables /usr/sbin/iptables-legacy
```
After execute the command above, we can see the iptabels has swtiched to `legacy` mode
```
root@node01:/home/<USER>
iptables v1.8.7 (nf_tables)
root@node01:/home/<USER>/usr/sbin/iptables-legacy
update-alternatives: using /usr/sbin/iptables-legacy to provide /usr/sbin/iptables (iptables) in manual mode
root@node01:/home/<USER>
iptables v1.8.7 (legacy)
```
### Turn off swap:
Fedora:
```
touch /etc/systemd/zram-generator.conf
systemctl mask systemd-zram-setup@.service
sed -i '/ swap / s/^\(.*\)$/#\1/g' /etc/fstab
```
Ubuntu:
```
```

### Disable firewall
Fedora
```
systemctl disable --now firewalld
```
Ubuntu
```
ufw disable
```
### Disable `systemd-resolved`
```
systemctl disable --now systemd-resolved
```
### Configure DNS defaults for NetworkManager:
Fedora
```
sed -i '/\[main\]/a dns=default' /etc/NetworkManager/NetworkManager.conf
```
Ubuntu does not require this step

### Blank the system-level DNS resolver configuration:
```
unlink /etc/resolv.conf || true
touch /etc/resolv.conf
```
Add our nameserver to `/etc/resolv.conf`
```
root@node01:/home/<USER>/etc/resolv.conf
nameserver ***********
```
### Disable SELinux
Fedora only
```
setenforce 0
```
### Make sure all changes survive a reboot
```
reboot
```

## **Download all the artifacts**
### Set environment variables for ARCH to use:
```
export ARCH=x86_64
export K8s_ARCH=amd64
```
### Set environment variables for software versions to use:
```
export CNI_PLUGINS_VERSION="v1.5.1"
export CRICTL_VERSION="v1.30.0"
export KUBE_RELEASE="v1.30.4"
export RELEASE_VERSION="v0.17.1"
```
### Download Packages
On VDI
```
mkdir download && cd download
curl -L -O "https://github.com/containernetworking/plugins/releases/download/${CNI_PLUGINS_VERSION}/cni-plugins-linux-${K8s_ARCH}-${CNI_PLUGINS_VERSION}.tgz"
curl -L -O "https://github.com/kubernetes-sigs/cri-tools/releases/download/${CRICTL_VERSION}/crictl-${CRICTL_VERSION}-linux-${K8s_ARCH}.tar.gz"ls 
curl -L --remote-name-all https://dl.k8s.io/release/${KUBE_RELEASE}/bin/linux/${K8s_ARCH}/kubeadm
curl -L --remote-name-all https://dl.k8s.io/release/${KUBE_RELEASE}/bin/linux/${K8s_ARCH}/kubelet
curl -L --remote-name-all https://dl.k8s.io/release/${KUBE_RELEASE}/bin/linux/${K8s_ARCH}/kubectl
lscurl -L -O "https://raw.githubusercontent.com/kubernetes/release/${RELEASE_VERSION}/cmd/krel/templates/latest/deb/kubelet/lib/systemd/system/kubelet.service"
curl -L -O "https://raw.githubusercontent.com/kubernetes/release/master/cmd/krel/templates/latest/kubeadm/10-kubeadm.conf"
curl -L -O "https://dl.k8s.io/release/${KUBE_RELEASE}/bin/linux/${K8s_ARCH}/kubectl"
curl -LO "https://raw.githubusercontent.com/flannel-io/flannel/master/Documentation/kube-flannel.yml"
```
Ubuntu Packages
```
curl -L -O http://archive.ubuntu.com/ubuntu/pool/main/c/conntrack-tools/conntrack_1.4.6-2build2_amd64.deb
curl -L -O http://archive.ubuntu.com/ubuntu/pool/universe/libc/libcgroup/libcgroup1_2.0-2_amd64.deb
curl -L -O http://archive.ubuntu.com/ubuntu/pool/main/s/socat/socat_1.7.4.1-3ubuntu4_amd64.deb
curl -L -O http://archive.ubuntu.com/ubuntu/pool/main/c/containerd/containerd_1.5.9-0ubuntu3_amd64.deb
curl -l -O http://archive.ubuntu.com/ubuntu/pool/main/r/runc/runc_1.1.0-0ubuntu1_amd64.deb
```
Fedora Packages
```
curl -O "https://dl.fedoraproject.org/pub/fedora/linux/releases/37/Everything/${ARCH}/os/Packages/s/socat-1.7.4.2-3.fc37.${ARCH}.rpm"
curl -O "https://dl.fedoraproject.org/pub/fedora/linux/releases/37/Everything/${ARCH}/os/Packages/l/libcgroup-3.0-1.fc37.${ARCH}.rpm"
curl -O "https://dl.fedoraproject.org/pub/fedora/linux/releases/37/Everything/${ARCH}/os/Packages/c/conntrack-tools-1.4.6-4.fc37.${ARCH}.rpm"
```
### Download all of the necessary container images:
check the images for the release:
```
./kubeadm config images list
```
for example:
```
mystic@mystic-vm:~/kubeadm/download$ ./kubeadm config images list
registry.k8s.io/kube-apiserver:v1.30.4
registry.k8s.io/kube-controller-manager:v1.30.4
registry.k8s.io/kube-scheduler:v1.30.4
registry.k8s.io/kube-proxy:v1.30.4
registry.k8s.io/coredns/coredns:v1.11.1
registry.k8s.io/pause:3.9
registry.k8s.io/etcd:3.5.12-0
```

```
images=(
    "registry.k8s.io/kube-apiserver:${KUBE_RELEASE}"
    "registry.k8s.io/kube-controller-manager:${KUBE_RELEASE}"
    "registry.k8s.io/kube-scheduler:${KUBE_RELEASE}"
    "registry.k8s.io/kube-proxy:${KUBE_RELEASE}"
    "registry.k8s.io/pause:3.9"
    "registry.k8s.io/etcd:3.5.12-0"
    "registry.k8s.io/coredns/coredns:v1.11.1"
    "flannel/flannel:v0.25.5"
    "flannel/flannel-cni-plugin:v1.5.1-flannel1"
)

for image in "${images[@]}"; do
    # Pull the image from the registry
    docker pull "$image"

    # Save the image to a tar file on the local disk
    image_name=$(echo "$image" | sed 's|/|_|g' | sed 's/:/_/g')
    docker save -o "${image_name}.tar" "$image"

done
```
Copy files to remote host:
```
scp <FILE> <AIRGAP_VM_USER>@<AIRGAP_VM_IP>:~/tmp/
```

## **Put the artifacts in place**
Everything that is needed in order to bootstrap a Kubernetes cluster now exists on the air-gapped VM
```
export ARCH=x86_64
export K8s_ARCH=amd64

export CNI_PLUGINS_VERSION="v1.5.1"
export CRICTL_VERSION="v1.30.0"
export KUBE_RELEASE="v1.30.4"
export RELEASE_VERSION="v0.17.1"

cd ~/tmp/
```
Fedora:
```
dnf -y install ./*.rpm
```

Ubuntu:
```
dpkg -i ./*.deb
```
install the CNI plugins and `crictl`:
```
mkdir -p /opt/cni/bin
tar -C /opt/cni/bin -xz -f "cni-plugins-linux-${K8s_ARCH}-v1.5.1.tgz"
tar -C /usr/local/bin -xz -f "crictl-v1.30.0-linux-${K8s_ARCH}.tar.gz"
```
Make kubeadm, kubelet and kubectl executable
```
chmod +x kubeadm kubelet kubectl
mv kubeadm kubelet kubectl /usr/local/bin
```
Define an override for the systemd kubelet service file
```
mkdir -p /etc/systemd/system/kubelet.service.d
sed "s:/usr/bin:/usr/local/bin:g" 10-kubeadm.conf > /etc/systemd/system/kubelet.service.d/10-kubeadm.conf
```
enable CRI plugin:
```
mkdir -p /etc/containerd
echo 'disabled_plugins = ["cri"]

#root = "/var/lib/containerd"
#state = "/run/containerd"
#subreaper = true
#oom_score = 0

#[grpc]
#  address = "/run/containerd/containerd.sock"
#  uid = 0
#  gid = 0

#[debug]
#  address = "/run/containerd/debug.sock"
#  uid = 0
#  gid = 0
#  level = "info"
' >  /etc/containerd/config.toml

sed -i 's/^disabled_plugins = \["cri"\]/#&/' /etc/containerd/config.toml
```
Move kubelet service:
```
cp kubelet.service /lib/systemd/system/
```
Start, and enable, containerd and the kubelet:
```
systemctl enable --now containerd
systemctl enable --now kubelet
```
Load container images for Kubernetes components, via ctr:
```
for file in ./*.tar; do
    ctr -n k8s.io images import $file
done
```
### Spin up the Kubernetes cluster
heck if a cluster is already running and tear it down if it is:
```
if systemctl is-active --quiet kubelet; then

    # Reset the Kubernetes cluster

    echo "A Kubernetes cluster is already running. Resetting the cluster..."

    kubeadm reset -f

fi
```

Create a cluster configuration file and initialize the cluster:
```
echo "---

apiVersion: kubeadm.k8s.io/v1beta3
kind: ClusterConfiguration
clusterName: kubernetes
kubernetesVersion: v1.30.4
networking:
    dnsDomain: cluster.local
    podSubnet: **********/16 # --pod-network-cidr
    serviceSubnet: *********/12
---
apiVersion: kubeadm.k8s.io/v1beta3
kind: InitConfiguration
localAPIEndpoint:
    advertiseAddress: *********** # Update to the IP address of the air gap VM
    bindPort: 6443
nodeRegistration:
    criSocket: unix:///run/containerd/containerd.sock # or rely on autodetection
    name: airgap # this must match the hostname of the air gap VM
# Since this is a single node cluster, this taint has to be commented out,
# otherwise the coredns pods will not come up.
# taints:
# - effect: NoSchedule
# key: node-role.kubernetes.io/master" > kubeadm_cluster.yaml
kubeadm init --config kubeadm_config.yaml
```
Set `$KUBECONFIG` and use `kubectl` to wait until the API server is healthy:
```
export KUBECONFIG=/etc/kubernetes/admin.conf

until kubectl get nodes; do
    echo -e "\nWaiting for API server to respond..." 1>&2
    sleep 5

done
```
when the cluste is up and running, you could see the output below:
```
[bootstrap-token] Using token: fl8w3o.3jdeju5unxzs6w5u
[bootstrap-token] Configuring bootstrap tokens, cluster-info ConfigMap, RBAC Roles
[bootstrap-token] Configured RBAC rules to allow Node Bootstrap tokens to get nodes
[bootstrap-token] Configured RBAC rules to allow Node Bootstrap tokens to post CSRs in order for nodes to get long term certificate credentials
[bootstrap-token] Configured RBAC rules to allow the csrapprover controller automatically approve CSRs from a Node Bootstrap Token
[bootstrap-token] Configured RBAC rules to allow certificate rotation for all node client certificates in the cluster
[bootstrap-token] Creating the "cluster-info" ConfigMap in the "kube-public" namespace
[kubelet-finalize] Updating "/etc/kubernetes/kubelet.conf" to point to a rotatable kubelet client certificate and key
[addons] Applied essential addon: CoreDNS
[addons] Applied essential addon: kube-proxy

Your Kubernetes control-plane has initialized successfully!

To start using your cluster, you need to run the following as a regular user:

  mkdir -p $HOME/.kube
  sudo cp -i /etc/kubernetes/admin.conf $HOME/.kube/config
  sudo chown $(id -u):$(id -g) $HOME/.kube/config

Alternatively, if you are the root user, you can run:

  export KUBECONFIG=/etc/kubernetes/admin.conf

You should now deploy a pod network to the cluster.
Run "kubectl apply -f [podnetwork].yaml" with one of the options listed at:
  https://kubernetes.io/docs/concepts/cluster-administration/addons/

Then you can join any number of worker nodes by running the following on each as root:

kubeadm join ***********:6443 --token fl8w3o.3jdeju5unxzs6w5u \
        --discovery-token-ca-cert-hash sha256:9776f7f411bc4cf391b009d306dbf3858a215d2c851ddf3273496b95025f3b66
```