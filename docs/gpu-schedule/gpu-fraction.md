# MIG Fragmentation and GPU Underutilization on H100

NVIDIA's Multi-Instance GPU (MIG) technology allows partitioning a physical GPU into multiple smaller, isolated instances. This is great for sharing GPU resources among different workloads. However, with the H100's 7 groups of SM (Streaming Multiprocessor) partitioning scheme, fragmentation can lead to significant underutilization.

Below is the graph of a `Streaming Multiprocessor`:

![Streaming Multiprocessor](./assets/sm.png)

The core issue is that you can't arbitrarily combine MIG instances. If you allocate a 3/7 fraction of the SMs first, you cannot then allocate a 4/7 fraction on the same GPU. You'd be forced to allocate another 3/7, leaving a 1/7 fraction unused. 
Conversely, allocating a 4/7 instance first allows you to then allocate another 3/7 (if resources allow), making better use of the GPU.

## Scenario 1: Allocating 3/7 First, Leading to Fragmentation:
```
+-----------------+
| H100 GPU (7 SMs)|
+-----------------+
| S S S | S S S S |  <-- Initial State
+-----------------+

Allocate 3/7:
+-------+---------+
| [3/7] | S S S S |  <-- Instance 1 (3/7) allocated
| X X X |         |
+-------+---------+

Attempt to allocate 4/7 (FAILS):
+-------+---------+
| [3/7] | S S S S |  <-- Cannot allocate 4/7 here
| X X X |         |
+-------+---------+

Forced to allocate another 3/7 (WASTE 1/7):
+-------+-------+
| [3/7] | [3/7] |  <-- Instance 1 & Instance 2 (3/7 + 3/7 = 6/7)
| X X X | Y Y Y |
+-------+-------+
|       |   S   |  <-- 1/7 SM UNUSED
+-------+-------+
```
In this case, one SM is left unused because we couldn't fit a 4/7 instance after allocating a 3/7 instance.

## Scenario 2: Allocating 4/7 First, Better Utilization:
```
+-----------------+
| H100 GPU (7 SMs) |
+-----------------+
| S S S S | S S S |  <-- Initial State
+---------+-------+

Allocate 4/7:
+---------+-------+
| [4/7]   | S S S |  <-- Instance 1 (4/7) allocated
| X X X X |       |
+---------+-------+

Allocate another 3/7 SUCCESS
+---------+-------+
| [4/7]   | [3/7] |  <-- Instance 1 & Instance 2 (4/7 + 3/7 = 7/7)
| X X X X | Y Y Y |
+---------+-------+
```
If we have another H100 or carefully manage memory oversubscription, we use all SMs effectively.


## `mig-parted` issue in K8s

`mig-parted` is a command-line tool designed to simplify the management of MIG partitions on NVIDIA GPUs. It allows system administrators to define and apply predefined MIG configurations across multiple GPUs or even across an entire cluster. Instead of manually creating and configuring each MIG instance using nvidia-smi, administrators can define configurations in YAML files and then use mig-parted to apply these configurations to the desired GPUs.

The implementation of `mig-parted` reveals that applying a new MIG configuration triggers a reset of the existing MIG configuration. Consequently, resetting the graphics card's MIG requires halting all processes running on each MIG instance, resulting in service disruption.

However, if we do not rely on the existing mig-parted but directly use NVIDIA's APIs to reconfigure MIG, we can avoid resetting the MIG configuration. **This allows us to achieve functionalities similar to Run:ai's dynamic MIG.**

> This requires the graphics card architecture to be Hopper (H100) and later. For graphics cards before the Hopper architecture, such as A100, resetting the entire graphics card is required when reconfiguring MIG.

From the process, it can be seen that mig-parted has **significant room for improvement**. `ClearMigConfig` destroys all graphic instances (GIs). If all GIs are destroyed, then reusable GIs should no longer exist. Here, the delta between the new and old configurations should be analyzed to more smartly apply new MIG instances. I suspect this is to maintain compatibility with Ampere architecture (A100) GPUs.

Below is the flowchart of the `SetMigConfig` process:
```mermaid
flowchart TD
    A[SetMigConfig] --> B{Initialize NVML}
    B -->|Success| C[Get Device Handle by Index]
    B -->|Failure| Z[Return Error: Initializing NVML]
    C -->|Success| D[Get Device Memory Info]
    C -->|Failure| Y[Return Error: Getting Device Handle]
    D -->|Success| E[Assert MIG Enabled]
    D -->|Failure| X[Return Error: Getting Device Memory]
    E -->|Success| F[Iterate Permutations Until Success]
    E -->|Failure| W[Return Error: Asserting MIG Enabled]
    
    F --> G{Get Existing MigConfig}
    G -->|Success| H{Is MigConfig Empty?}
    G -->|Failure| V[Return Error: Getting Existing MigConfig]
    
    H -->|Yes| M[Break]
    H -->|No| I{Clear Attempts < Max Attempts}
    
    I -->|Yes| J[Clear MigConfig]
    I -->|No| N[Return Error: Exceeded Max Attempts to Clear MigConfig]
    
    J -->|Success| G
    J -->|Failure| U[Return Error: Clearing MigConfig]
    
    M --> K[For Each MigProfile in Config]
    
    K --> L[Get GPU Instance Profile Info]
    L -->|Success| O{Reuse GPU Instance?}
    L -->|Failure| T[Return Error: Getting GPU Instance Profile Info]
    
    O -->|Yes| P[Reuse Existing GPU Instance]
    O -->|No| Q[Create New GPU Instance]
    
    P --> R[Get Compute Instance Profile Info]
    Q --> R
    
    R -->|Success| S[Create Compute Instance]
    R -->|Failure| S1[Return Error: Getting Compute Instance Profile Info]
    
    S -->|Success| T1[Validate MIG Profile]
    S -->|Failure| S2[Return Error: Creating Compute Instance]
    
    T1 -->|Valid| K
    T1 -->|Invalid| O
    
    K -->|All MigProfiles Processed| L1[Return nil]
    
    F -->|Failure| L2[Clear MigConfig if Error]
    L2 -->|Success| M1[Log Error Clearing MIG Config]
    L2 -->|Failure| N1[Return Error: Multiple Config Orderings Failed]
    
    N1 --> R1[Return Error: Attempting Multiple Config Orderings]
```
