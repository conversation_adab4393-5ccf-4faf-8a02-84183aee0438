# K8s Scheduling
## Challenges
Kubernetes scheduling can encounter specific challenges when dealing with batch jobs. These challenges mainly arise from the inherent characteristics of batch jobs:

* Large and concentrated resource demands: Batch jobs often require significant computing resources (CPU, memory, GPUs, etc.) that must be available concurrently during execution.

* Long execution times: Batch jobs typically have extended execution durations, ranging from minutes to days.

* High fault tolerance requirements: Due to their lengthy execution, batch jobs are more vulnerable to disruptions like node failures, necessitating greater fault tolerance.
* Job dependencies: Some batch jobs have interdependencies, requiring execution in a specific sequence.

Considering these characteristics, K8s scheduling may encounter the following problems in batch jobs:

### 1. Resource Fragmentation and Scheduling Failures:

* Problem: Batch jobs' substantial resource requirements can lead to scheduling failures if the cluster lacks contiguous and sufficient resources, even if the total cluster capacity is adequate. For instance, if a cluster has two nodes, each with one free GPU, but a job needs two GPUs, the job cannot be scheduled.

    **Scenario 1: Fragmented Resources** 
    ```
    +-----------------+     +-----------------+
    |    Node A       |     |    Node B       |
    +---------+-------+     +---------+-------+
    | Pod 1   | Res: 1|     | Pod 2   | Res: 1|
    +---------+-------+     +---------+-------+
    | Available: 1    |     | Available: 1    |
    | Total: 2        |     | Total: 2        |
    +-----------------+     +-----------------+

    Job requires 2 Resources
    ```
    In this scenario, even though there are 2 available resources in total (2 on each node), a job requiring 2 contiguous resources cannot be scheduled.

    **Scenario 2: Contiguous Resources**
    ```
    +-----------------+     +-----------------+
    |    Node A       |     |    Node B       |
    +---------+-------+     +---------+-------+
    | Pod 1   | Res: 1|     |         |       |
    +---------+-------+     +---------+-------+
    | Available: 2    |     | Available: 3    |
    +-----------------+     +-----------------+

    Job requires 2 Resources
    ```
    Here, Node B and Node A has enough contiguous resources to accommodate the job. But it will be schedul to Node B.

* **Root Cause**: K8s's default scheduling policy, `LeastRequestedPriority`, prioritizes scheduling Pods to nodes with the _lowest_ resource utilization, potentially causing resource fragmentation.

    > `LeastRequestedPriority` favors nodes with fewer requested resources. It calculates the percentage of memory and CPU requested by pods scheduled on the node, and prioritizes nodes that have the highest available/remaining capacity.

* **Solutions**:
    - **Binpacking**: Using the `Binpack` scheduling policy prioritizes scheduling Pods to nodes with higher resource utilization, mitigating fragmentation.

### 2. Node Failures During Job Execution:

* Problem: Long-running batch jobs are susceptible to node failures, leading to job interruption and potential data loss.

    ```
    +-----------------+     +-----------------+
    |    Node A       |     |    Node B       |
    +---------+-------+     +---------+-------+
    | Job Pod | Res: 3|     |         |       |
    +---------+-------+     +---------+-------+
    | Available: 0    |     | Available: 3    |
    +-----------------+     +-----------------+
    ^
    | Node A fails
    ```
    If Node A fails during the job's execution, the job will be interrupted.

* Root Cause: K8s typically reschedules Pods after node failures. However, for batch jobs, rescheduling can lead to significant redundant computation and wasted time.
* Solutions:
    - **Job Controller**: The Job controller ensures that batch jobs complete at least once. It recreates failed Pods to achieve this guarantee.

    - **Checkpoints/Restart Mechanisms (Application-Level)**: For very long jobs, implementing application-level checkpointing and restart mechanisms can be crucial to recover from failures without restarting from the beginning.
### 3. Job Dependencies:

* Problem: Batch workflows often involve dependencies between jobs, requiring sequential execution.
    ```
    Job A --> Job B --> Job C
    ```
    Job B cannot start until Job A completes, and Job C cannot start until Job B completes.


* Root Cause: The default K8s scheduler doesn't inherently handle job dependencies.
* Solutions:
    * **Workflow Engines** (e.g., Kubeflow, Flyte): These tools provide robust dependency management, allowing you to define complex workflows with dependencies, conditional execution, and retries.

### 4. Coscheduling (Gang Scheduling):

* Problem: Some batch jobs require multiple Pods to be scheduled together on different nodes to function correctly.
    ```
    +-----------------+     +-----------------+     +-----------------+
    |    Node A       |     |    Node B       |     |    Node C       |
    +---------+-------+     +---------+-------+     +---------+-------+
    | Pod 1   | Res: 1|     | Pod 2   | Res: 1|     | Pod 3   | Res: 1|
    +---------+-------+     +---------+-------+     +---------+-------+
    ```
    All three Pods need to be scheduled simultaneously for the job to function correctly. If only Pod 1 and Pod 2 are scheduled, the job is stuck.

* Root Cause: The default scheduler doesn't guarantee simultaneous scheduling of multiple Pods.

* Solutions:

    - PodGroups (Kubernetes 1.21+): PodGroups provide a mechanism to schedule a group of Pods as a unit. If all Pods in the group cannot be scheduled, none of them are scheduled. This helps prevent situations where only part of a distributed job is running.

    - Volcano (CNCF Project): Volcano is a batch scheduling system built on Kubernetes. It offers advanced features like gang scheduling, queue management, and fair-share scheduling, specifically designed for high-performance computing (HPC) and batch workloads.

### 5. Scheduling Bottleneck with Many Pods
The kube-scheduler works in a loop: it queues pending Pods, filters suitable nodes based on predicates (e.g., resource availability), scores the filtered nodes based on priorities (e.g., least resource usage), and then binds the Pod to the highest-scoring node.

When dealing with many Pods simultaneously, the filtering and scoring stages become bottlenecks. The scheduler has to evaluate each Pod against every node in the cluster. This linear scaling creates significant overhead, especially in large clusters or during sudden bursts of Pod creation (e.g., autoscaling, batch jobs).

**Illustration:**

Let's imagine a scenario with three nodes and a sudden influx of Pods needing scheduling: suddenly, 1000 new Pods need to be scheduled. The scheduler has to perform the following (simplified) steps for each of those 1000 Pods:

* Filtering: Check if Node A has enough resources. Check if Node B has enough resources. Check if Node C has enough resources... (This process repeats for all 1000 Pods).
* Scoring: If a node passes the filter, calculate its score based on various priorities. This happens for every potential node for each Pod.

This leads to O(N*M) complexity where N is the number of Pods and M is the number of Nodes.

```
+-----------------+     +-----------------+     +-----------------+
|    Node A       |     |    Node B       |     |    Node C       | ... (Many More Nodes)
+-----------------+     +-----------------+     +-----------------+
      ^                     ^                     ^
      |                     |                     |
      |                     |                     |
+-----------------------------------------------------+
|      Scheduler (Processing 1000 Pods)               |
|      (Filtering & Scoring for each Pod against      |
|       every Node becomes a significant load)        |
+-----------------------------------------------------+
```
The arrows indicate the scheduler's interaction with each node for every Pod. The sheer number of these interactions creates the bottleneck.

Consequences:

* Increased scheduling latency: Pods take longer to start.
* Resource contention: The scheduler itself consumes more CPU and memory.
* Cluster instability: In extreme cases, the scheduler can become overloaded, impacting the overall cluster stability.

Solutions:
* Scheduler Plugins (e.g., Volcano): Use specialized schedulers for large-scale batch workloads.
* Node scoring threshold: the kube-scheduler can stop looking for feasible nodes once it has found enough of them.

