# Local vLLM Deployment
## Overview
Using the GPU of a local laptop and the WSL environment, we can deploy VLLM on a local Kubernetes cluster to explore potential issues in deploying the VLLM tech stack.

This document outlines the major steps required to deploy VLLM in a WSL environment and uses VLLM to deploy a large language model to verify the functionality of the tech stack.

By following the steps in this document, you can deploy an inference server based on vLLM inside a k8s cluster in WSL.

## prerequisite
* A Windows Machine with GPU
* WSL2 with Ubuntu 22.04

## Installing Drivers
1. Installing `nvidia-cuda-toolkit` 

    Run commands below to install the `nvidia-cuda-toolkit`
    ```
    $ sudo apt-get update
    $ install nvidia-cuda-toolkit
    ```
    After installation, you can check the infomation of the toolkit using the command below:
    ```
    $ nvcc -V
    ```
    Below is the example output:
    ```
    mystic@W112JHRCK3:~$ nvcc -V
    nvcc: NVIDIA (R) Cuda compiler driver
    Copyright (c) 2005-2022 NVIDIA Corporation
    Built on Wed_Sep_21_10:33:58_PDT_2022
    Cuda compilation tools, release 11.8, V11.8.89
    Build cuda_11.8.r11.8/compiler.31833905_0
    ```
2. Installing CUDA Toolkit
    Run commands below to install the CUDA Toolkit
    ```
    $ wget https://developer.download.nvidia.com/compute/cuda/repos/wsl-ubuntu/x86_64/cuda-wsl-ubuntu.pin
    $ sudo mv cuda-wsl-ubuntu.pin /etc/apt/preferences.d/cuda-repository-pin-600
    $ wget https://developer.download.nvidia.com/compute/cuda/12.6.0/local_installers/cuda-repo-wsl-ubuntu-12-6-local_12.6.0-1_amd64.deb
    $ sudo dpkg -i cuda-repo-wsl-ubuntu-12-6-local_12.6.0-1_amd64.deb
    $ sudo cp /var/cuda-repo-wsl-ubuntu-12-6-local/cuda-*-keyring.gpg /usr/share/keyrings/
    $ sudo apt-get update
    $ sudo apt-get -y install cuda-toolkit-12-6
    ```
3. Installing the NVIDIA Container Toolkit

    * Configure the production repository:
        ```
        $ curl -fsSL https://nvidia.github.io/libnvidia-container/gpgkey | sudo gpg --dearmor -o /usr/share/keyrings/nvidia-container-toolkit-keyring.gpg \
        && curl -s -L https://nvidia.github.io/libnvidia-container/stable/deb/nvidia-container-toolkit.list | \
            sed 's#deb https://#deb [signed-by=/usr/share/keyrings/nvidia-container-toolkit-keyring.gpg] https://#g' | \
            sudo tee /etc/apt/sources.list.d/nvidia-container-toolkit.list
        ```
        configure the repository to use experimental packages:
        ```
        $ sed -i -e '/experimental/ s/^#//g' /etc/apt/sources.list.d/nvidia-container-toolkit.list
        ```
    * Update the packages list from the repository:
        ```
        $ sudo apt-get update
        ```
    * Install the NVIDIA Container Toolkit packages:
        ```
        $ sudo apt-get install -y nvidia-container-toolkit
        ```
        After installation, you can check the infomation of the toolkit using the command below:
        ```
        $ nvidia-container-cli -V
        ```
        Below is the example output:
        ```
        mystic@W112JHRCK3:~$ nvidia-container-cli -V
        cli-version: 1.16.1
        lib-version: 1.16.1
        build date: 2024-07-23T14:57+00:00
        build revision: 4c2494f16573b585788a42e9c7bee76ecd48c73d
        build compiler: x86_64-linux-gnu-gcc-7 7.5.0
        build platform: x86_64
        build flags: -D_GNU_SOURCE -D_FORTIFY_SOURCE=2 -DNDEBUG -std=gnu11 -O2 -g -fdata-sections -ffunction-sections -fplan9-extensions -fstack-protector -fno-strict-aliasing -fvisibility=hidden -Wall -Wextra -Wcast-align -Wpointer-arith -Wmissing-prototypes -Wnonnull -Wwrite-strings -Wlogical-op -Wformat=2 -Wmissing-format-attribute -Winit-self -Wshadow -Wstrict-prototypes -Wunreachable-code -Wconversion -Wsign-conversion -Wno-unknown-warning-option -Wno-format-extra-args -Wno-gnu-alignof-expression -Wl,-zrelro -Wl,-znow -Wl,-zdefs -Wl,--gc-sections
        ```
4. Configuring Docker
    * Configure the container runtime by using the `nvidia-ctk` command:
        ```
        $ sudo nvidia-ctk runtime configure --runtime=docker
        ```
        The `nvidia-ctk` command modifies the `/etc/docker/daemon.json` file on the host. The file is updated so that Docker can use the NVIDIA Container Runtime.
    * Restart the Docker daemon:
        ```
        $ sudo systemctl restart docker
        ```
5. Configuring containerd (for Kubernetes)
    * Configure the container runtime by using the `nvidia-ctk` command:
        ```
        $ sudo nvidia-ctk runtime configure --runtime=containerd
        ```
        The `nvidia-ctk` command modifies the `/etc/containerd/config.toml` file on the host. The file is updated so that containerd can use the NVIDIA Container Runtime. Belew is a sample config file:
        ```
        version = 2

        [plugins."io.containerd.internal.v1.opt"]
        path = "/var/lib/rancher/k3s/agent/containerd"
        [plugins."io.containerd.grpc.v1.cri"]
        stream_server_address = "127.0.0.1"
        stream_server_port = "10010"
        enable_selinux = false
        enable_unprivileged_ports = true
        enable_unprivileged_icmp = true
        sandbox_image = "rancher/mirrored-pause:3.6"

        [plugins."io.containerd.grpc.v1.cri".containerd]
        snapshotter = "overlayfs"
        disable_snapshot_annotations = true

        [plugins."io.containerd.grpc.v1.cri".cni]
        bin_dir = "/var/lib/rancher/k3s/data/b13851fe661ab93938fc9a881cdce529da8c6b9b310b2440ef01a860f8b9c3a9/bin"
        conf_dir = "/var/lib/rancher/k3s/agent/etc/cni/net.d"

        [plugins."io.containerd.grpc.v1.cri".containerd.runtimes.runc]
        runtime_type = "io.containerd.runc.v2"

        [plugins."io.containerd.grpc.v1.cri".containerd.runtimes.runc.options]
        SystemdCgroup = true

        [plugins."io.containerd.grpc.v1.cri".registry]
        config_path = "/var/lib/rancher/k3s/agent/etc/containerd/certs.d"

        [plugins."io.containerd.grpc.v1.cri".containerd.runtimes."nvidia"]
        runtime_type = "io.containerd.runc.v2"
        [plugins."io.containerd.grpc.v1.cri".containerd.runtimes."nvidia".options]
        BinaryName = "/usr/bin/nvidia-container-runtime"
        SystemdCgroup = true
        ```
    * Restart containerd:
        ```
        $ sudo systemctl restart containerd
        ```
6. Verify Installation
* Docker runtime:
    ```
    $ sudo docker run --rm --runtime=nvidia --gpus all ubuntu nvidia-smi
    ```
    If installation success, you can see output below:
    ```
    mystic@LAPTOP-AU9BT5DO:~$ sudo docker run --rm --runtime=nvidia --gpus all ubuntu nvidia-smi
    [sudo] password for mystic:
    Mon Aug 19 07:14:17 2024
    +-----------------------------------------------------------------------------------------+
    | NVIDIA-SMI 560.31.01              Driver Version: 560.81         CUDA Version: 12.6     |
    |-----------------------------------------+------------------------+----------------------+
    | GPU  Name                 Persistence-M | Bus-Id          Disp.A | Volatile Uncorr. ECC |
    | Fan  Temp   Perf          Pwr:Usage/Cap |           Memory-Usage | GPU-Util  Compute M. |
    |                                         |                        |               MIG M. |
    |=========================================+========================+======================|
    |   0  NVIDIA GeForce RTX 3060 ...    On  |   00000000:01:00.0  On |                  N/A |
    | N/A   49C    P8             13W /   60W |     459MiB /   6144MiB |      1%      Default |
    |                                         |                        |                  N/A |
    +-----------------------------------------+------------------------+----------------------+
    
    +-----------------------------------------------------------------------------------------+
    | Processes:                                                                              |
    |  GPU   GI   CI        PID   Type   Process name                              GPU Memory |
    |        ID   ID                                                               Usage      |
    |=========================================================================================|
    |    0   N/A  N/A        30      G   /Xwayland                                   N/A      |
    +-----------------------------------------------------------------------------------------+
    
    ```
* Kubernetes runtime:
    Create a pod ussing the configuartion below:
    ```
    apiVersion: v1
    kind: Pod
    metadata:
    name: nbody-gpu-benchmark
    namespace: default
    spec:
    restartPolicy: OnFailure
    runtimeClassName: nvidia
    containers:
    - name: cuda-container
        image: nvcr.io/nvidia/k8s/cuda-sample:nbody
        args: ["nbody", "-gpu", "-benchmark"]
        env:
        - name: NVIDIA_VISIBLE_DEVICES
        value: all
        - name: NVIDIA_DRIVER_CAPABILITIES
        value: all
    ```
    After creation, you can check the result:
    ```
    root@W112JHRCK3:/home/<USER>/vllm/k3s# kubectl get pods
    NAME                  READY   STATUS    RESTARTS   AGE
    nbody-gpu-benchmark   1/1     Running   0          4s
    root@W112JHRCK3:/home/<USER>/vllm/k3s# kubectl logs nbody-gpu-benchmark
    Run "nbody -benchmark [-numbodies=<numBodies>]" to measure performance.
            -fullscreen       (run n-body simulation in fullscreen mode)
            -fp64             (use double precision floating point values for simulation)
            -hostmem          (stores simulation data in host memory)
            -benchmark        (run benchmark to measure performance)
            -numbodies=<N>    (number of bodies (>= 1) to run in simulation)
            -device=<d>       (where d=0,1,2.... for the CUDA device to use)
            -numdevices=<i>   (where i=(number of CUDA devices > 0) to use for simulation)
            -compare          (compares simulation results running once on the default GPU and once on the CPU)
            -cpu              (run n-body simulation on the CPU)
            -tipsy=<file.bin> (load a tipsy model file for simulation)

    NOTE: The CUDA Samples are not meant for performance measurements. Results may vary when GPU Boost is enabled.

    > Windowed mode
    > Simulation data stored in video memory
    > Single precision floating point simulation
    > 1 Devices used for simulation
    GPU Device 0: "Ampere" with compute capability 8.6

    > Compute 8.6 CUDA device: [NVIDIA RTX A2000 8GB Laptop GPU]
    20480 bodies, total time for 10 iterations: 25.075 ms
    = 167.272 billion interactions per second
    = 3345.449 single-precision GFLOP/s at 20 flops per interaction
    ```

## Deploying vLLM
1. Downloading LLM model from Huggingface
    * Installing `git-lfs`:
        ```
        $ sudo apt-get install git-lfs
        $ git lfs install
        ```
    * Downloading model:
        For example, download the `Qwen2-0.5B` which is use less vRAM that can run on laptop with 8GB vRAM
        ```
        $ git clone https://huggingface.co/Qwen/Qwen2-0.5B
        ```
        More model can be downloaded form HuggingFace. You can use the method below to caculate the reqirements for vRAM
        ![vRAM](./assets/vRAM.png)
        [Calculating GPU memory for serving LLMs](https://www.substratus.ai/blog/calculating-gpu-memory-for-llm)
    * Move model to the models folder
        ```
        $ mv Qwen2-0.5B  src/vllm/models/Qwen2-0.5B
        $ tree
        ├── docker-compose.yml
        ├── models
        │   ├── Qwen2-0.5B
        │   │   ├── LICENSE
        │   │   ├── README.md
        │   │   ├── config.json
        │   │   ├── generation_config.json
        │   │   ├── merges.txt
        │   │   ├── model.safetensors
        │   │   ├── tokenizer.json
        │   │   ├── tokenizer_config.json
        │   │   └── vocab.json
        │   └── place_models_here.txt
        ```

2. Installing Docker Compose
    ```
    sudo apt-get update
    sudo apt  install docker-compose
    ```
3. Update Hugging Face Token Open docker-compose.yml and replace <hugging_face_token> with your own Hugging Face token. The format should be like this:
    ```
    environment:
        - HUGGING_FACE_HUB_TOKEN=<hugging_face_token>
    ```
4. Copy Model Weights
    Download or copy the desired LLM model weights into the models directory within the cloned repository and update the model name.
    ```
    command: --model /models/Qwen2-0.5B
    ```
5. Running vLLM with docker
    ```
    cd x/src/vllm/wsl
    sudo docker-compose up
    ```
    For example:
    ```
    mystic@W112JHRCK3:~/vllm/vllm-wsl$ sudo  docker-compose up
    [sudo] password for mystic:
    Creating network "vllm-wsl_default" with the default driver
    Creating vllm-wsl_vllm-openai_1 ... done
    Attaching to vllm-wsl_vllm-openai_1
    vllm-openai_1  | INFO 08-19 07:40:01 api_server.py:339] vLLM API server version 0.5.4
    vllm-openai_1  | INFO 08-19 07:40:01 api_server.py:340] args: Namespace(host=None, port=8000, uvicorn_log_level='info', allow_credentials=False, allowed_origins=['*'], allowed_methods=['*'], allowed_headers=['*'], api_key=None, lora_modules=None, prompt_adapters=None, chat_template=None, response_role='assistant', ssl_keyfile=None, ssl_certfile=None, ssl_ca_certs=None, ssl_cert_reqs=0, root_path=None, middleware=[], return_tokens_as_token_ids=False, disable_frontend_multiprocessing=False, model='/models/Qwen2-0.5B', tokenizer=None, skip_tokenizer_init=False, revision=None, code_revision=None, tokenizer_revision=None, tokenizer_mode='auto', trust_remote_code=False, download_dir=None, load_format='auto', dtype='auto', kv_cache_dtype='auto', quantization_param_path=None, max_model_len=None, guided_decoding_backend='outlines', distributed_executor_backend=None, worker_use_ray=False, pipeline_parallel_size=1, tensor_parallel_size=1, max_parallel_loading_workers=None, ray_workers_use_nsight=False, block_size=16, enable_prefix_caching=False, disable_sliding_window=False, use_v2_block_manager=False, num_lookahead_slots=0, seed=0, swap_space=4, cpu_offload_gb=0, gpu_memory_utilization=0.9, num_gpu_blocks_override=None, max_num_batched_tokens=None, max_num_seqs=256, max_logprobs=20, disable_log_stats=False, quantization=None, rope_scaling=None, rope_theta=None, enforce_eager=False, max_context_len_to_capture=None, max_seq_len_to_capture=8192, disable_custom_all_reduce=False, tokenizer_pool_size=0, tokenizer_pool_type='ray', tokenizer_pool_extra_config=None, enable_lora=False, max_loras=1, max_lora_rank=16, lora_extra_vocab_size=256, lora_dtype='auto', long_lora_scaling_factors=None, max_cpu_loras=None, fully_sharded_loras=False, enable_prompt_adapter=False, max_prompt_adapters=1, max_prompt_adapter_token=0, device='auto', scheduler_delay_factor=0.0, enable_chunked_prefill=None, speculative_model=None, num_speculative_tokens=None, speculative_draft_tensor_parallel_size=None, speculative_max_model_len=None, speculative_disable_by_batch_size=None, ngram_prompt_lookup_max=None, ngram_prompt_lookup_min=None, spec_decoding_acceptance_method='rejection_sampler', typical_acceptance_sampler_posterior_threshold=None, typical_acceptance_sampler_posterior_alpha=None, disable_logprobs_during_spec_decoding=None, model_loader_extra_config=None, ignore_patterns=[], preemption_mode=None, served_model_name=None, qlora_adapter_name_or_path=None, otlp_traces_endpoint=None, engine_use_ray=False, disable_log_requests=False, max_log_len=None)
    vllm-openai_1  | WARNING 08-19 07:40:01 config.py:1454] Casting torch.bfloat16 to torch.float16.
    vllm-openai_1  | WARNING 08-19 07:40:01 arg_utils.py:766] Chunked prefill is enabled by default for models with max_model_len > 32K. Currently, chunked prefill might not work with some features or models. If you encounter any issues, please disable chunked prefill by setting --enable-chunked-prefill=False.
    vllm-openai_1  | INFO 08-19 07:40:01 config.py:820] Chunked prefill is enabled with max_num_batched_tokens=512.
    vllm-openai_1  | INFO 08-19 07:40:01 llm_engine.py:174] Initializing an LLM engine (v0.5.4) with config: model='/models/Qwen2-0.5B', speculative_config=None, tokenizer='/models/Qwen2-0.5B', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.bfloat16, max_seq_len=131072, download_dir=None, load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None), seed=0, served_model_name=/models/Qwen2-0.5B, use_v2_block_manager=False, enable_prefix_caching=False)
    vllm-openai_1  | WARNING 08-19 07:40:01 utils.py:578] Using 'pin_memory=False' as WSL is detected. This may slow down the performance.
    vllm-openai_1  | INFO 08-19 07:40:02 model_runner.py:720] Starting to load model /models/Qwen2-0.5B...
    Loading safetensors checkpoint shards:   0% Completed | 0/1 [00:00<?, ?it/s]
    Loading safetensors checkpoint shards: 100% Completed | 1/1 [00:00<00:00,  1.14it/s]
    Loading safetensors checkpoint shards: 100% Completed | 1/1 [00:00<00:00,  1.14it/s]
    vllm-openai_1  |
    vllm-openai_1  | INFO 08-19 07:40:04 model_runner.py:732] Loading model weights took 0.9381 GB
    vllm-openai_1  | INFO 08-19 07:40:04 gpu_executor.py:102] # GPU blocks: 27607, # CPU blocks: 21845
    vllm-openai_1  | INFO 08-19 07:40:05 model_runner.py:1024] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
    vllm-openai_1  | INFO 08-19 07:40:05 model_runner.py:1028] CUDA graphs can take additional 1~3 GiB memory per GPU. If you are running out of memory, consider decreasing `gpu_memory_utilization` or enforcing eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
    vllm-openai_1  | INFO 08-19 07:40:14 model_runner.py:1225] Graph capturing finished in 9 secs.
    vllm-openai_1  | WARNING 08-19 07:40:14 serving_embedding.py:171] embedding_mode is False. Embedding API will not work.
    vllm-openai_1  | INFO 08-19 07:40:14 launcher.py:14] Available routes are:
    vllm-openai_1  | INFO 08-19 07:40:14 launcher.py:22] Route: /openapi.json, Methods: GET, HEAD
    vllm-openai_1  | INFO 08-19 07:40:14 launcher.py:22] Route: /docs, Methods: GET, HEAD
    vllm-openai_1  | INFO 08-19 07:40:14 launcher.py:22] Route: /docs/oauth2-redirect, Methods: GET, HEAD
    vllm-openai_1  | INFO 08-19 07:40:14 launcher.py:22] Route: /redoc, Methods: GET, HEAD
    vllm-openai_1  | INFO 08-19 07:40:14 launcher.py:22] Route: /health, Methods: GET
    vllm-openai_1  | INFO 08-19 07:40:14 launcher.py:22] Route: /tokenize, Methods: POST
    vllm-openai_1  | INFO 08-19 07:40:14 launcher.py:22] Route: /detokenize, Methods: POST
    vllm-openai_1  | INFO 08-19 07:40:14 launcher.py:22] Route: /v1/models, Methods: GET
    vllm-openai_1  | INFO 08-19 07:40:14 launcher.py:22] Route: /version, Methods: GET
    vllm-openai_1  | INFO 08-19 07:40:14 launcher.py:22] Route: /v1/chat/completions, Methods: POST
    vllm-openai_1  | INFO 08-19 07:40:14 launcher.py:22] Route: /v1/completions, Methods: POST
    vllm-openai_1  | INFO 08-19 07:40:14 launcher.py:22] Route: /v1/embeddings, Methods: POST
    vllm-openai_1  | INFO:     Started server process [1]
    vllm-openai_1  | INFO:     Waiting for application startup.
    vllm-openai_1  | INFO:     Application startup complete.
    vllm-openai_1  | INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
    vllm-openai_1  | INFO 08-19 07:40:24 metrics.py:406] Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 0.0 tokens/s, Running: 0 reqs, Swapped: 0 reqs, Pending: 0 reqs, GPU KV cache usage: 0.0%, CPU KV cache usage: 0.0%.
    ```
    We can see the vRAM usage increased by about 5GB
    ![usage](./assets/usage.png)
6. Testing vLLM deployment
* Checking available models
    ```
    $ curl -s http://localhost:8000/v1/models | jq
    {
    "object": "list",
    "data": [
        {
        "id": "/models/Qwen2-0.5B",
        "object": "model",
        "created": 1724053476,
        "owned_by": "vllm",
        "root": "/models/Qwen2-0.5B",
        "parent": null,
        "max_model_len": 131072,
        "permission": [
            {
            "id": "modelperm-7aa9f64902554522a530269f3e8feb37",
            "object": "model_permission",
            "created": 1724053476,
            "allow_create_engine": false,
            "allow_sampling": true,
            "allow_logprobs": true,
            "allow_search_indices": false,
            "allow_view": true,
            "allow_fine_tuning": false,
            "organization": "*",
            "group": null,
            "is_blocking": false
            }
        ]
        }
    ]
    }
    ```
* Testing Deployment
Below is a sample promt:
    ```
    curl -s http://localhost:8000/v1/completions \
        -H "Content-Type: application/json" \
        -d '{
            "model": "/models/Qwen2-0.5B",
            "prompt": "FIDO is",
            "max_tokens": 200,
            "temperature": 0
        }' \
    |jq 
    ```
    * model: Specifies the path or name of the model to use. In this case, it's /models/Qwen2-0.5B.
    * prompt: The text provided to the model as a starting point. The model generates text based on this prompt. Here, it's "FIDO is".
    * max_tokens: Sets the maximum length of the generated text, measured in tokens. Here, it's 200.
    * temperature: Controls the randomness of the output. Lower values (like 0) make the output more deterministic, while higher values increase diversity. Here, it's 0.

    Below is a sample output:

        ```
        curl -s http://localhost:8000/v1/completions \
            -H "Content-Type: application/json" \
            -d '{
                "model": "/models/Qwen2-0.5B",
                "prompt": "FIDO is",
                "max_tokens": 200,
                "temperature": 0
            }' \
        |jq
        {
        "id": "cmpl-d773485d7ed14d6083dcfdd4c7c252fb",
        "object": "text_completion",
        "created": 1724053914,
        "model": "/models/Qwen2-0.5B",
        "choices": [
            {
            "index": 0,
            "text": " a standard for secure authentication and authorization of devices. It is a standard for secure authentication and authorization of devices. It is a standard for secure authentication and authorization of devices. It is a standard for secure authentication and authorization of devices. It is a standard for secure authentication and authorization of devices. It is a standard for secure authentication and authorization of devices. It is a standard for secure authentication and authorization of devices. It is a standard for secure authentication and authorization of devices. It is a standard for secure authentication and authorization of devices. It is a standard for secure authentication and authorization of devices. It is a standard for secure authentication and authorization of devices. It is a standard for secure authentication and authorization of devices. It is a standard for secure authentication and authorization of devices. It is a standard for secure authentication and authorization of devices. It is a standard for secure authentication and authorization of devices. It is a standard for secure authentication and authorization of devices. It is a standard for secure authentication and authorization of",
            "logprobs": null,
            "finish_reason": "length",
            "stop_reason": null
            }
        ],
        "usage": {
            "prompt_tokens": 3,
            "total_tokens": 203,
            "completion_tokens": 200
        }
        }
        ```
7. Running vLLM deployment with Kubernetes
    Creating a pod using the configuration below(src/vllm/deployment.yml):
    ```
    apiVersion: v1
    kind: Pod
    metadata:
    name: vllm-openai
    spec:
    runtimeClassName: nvidia
    containers:
        - name: vllm-openai
        image: vllm/vllm-openai:v0.5.4
        args: ["--model", "/models/Qwen2-0.5B"]
        ports:
            - containerPort: 8000
            hostPort: 8000
        volumeMounts:
            - mountPath: /root/.cache/huggingface
            name: huggingface-cache
            - mountPath: /models
            name: models
        env:
            - name: HUGGING_FACE_HUB_TOKEN
            value: hf_bfCI
    volumes:
        - name: huggingface-cache
        hostPath:
            path: /home/<USER>/.cache/huggingface
        - name: models
        hostPath:
            path: /home/<USER>/vllm/vllm-wsl/models
    hostIPC: true
    hostNetwork: true
    ```

    Check the pod status
    
    ```
    root@W112JHRCK3:/home/<USER>/vllm/vllm-wsl# kubectl get pods
    NAME          READY   STATUS    RESTARTS   AGE
    vllm-openai   1/1     Running   0          5s
    root@W112JHRCK3:/home/<USER>/vllm/vllm-wsl# kubectl logs vllm-openai
    INFO 08-19 08:03:57 api_server.py:339] vLLM API server version 0.5.4
    INFO 08-19 08:03:57 api_server.py:340] args: Namespace(host=None, port=8000, uvicorn_log_level='info', allow_credentials=False, allowed_origins=['*'], allowed_methods=['*'], allowed_headers=['*'], api_key=None, lora_modules=None, prompt_adapters=None, chat_template=None, response_role='assistant', ssl_keyfile=None, ssl_certfile=None, ssl_ca_certs=None, ssl_cert_reqs=0, root_path=None, middleware=[], return_tokens_as_token_ids=False, disable_frontend_multiprocessing=False, model='/models/Qwen2-0.5B', tokenizer=None, skip_tokenizer_init=False, revision=None, code_revision=None, tokenizer_revision=None, tokenizer_mode='auto', trust_remote_code=False, download_dir=None, load_format='auto', dtype='auto', kv_cache_dtype='auto', quantization_param_path=None, max_model_len=None, guided_decoding_backend='outlines', distributed_executor_backend=None, worker_use_ray=False, pipeline_parallel_size=1, tensor_parallel_size=1, max_parallel_loading_workers=None, ray_workers_use_nsight=False, block_size=16, enable_prefix_caching=False, disable_sliding_window=False, use_v2_block_manager=False, num_lookahead_slots=0, seed=0, swap_space=4, cpu_offload_gb=0, gpu_memory_utilization=0.9, num_gpu_blocks_override=None, max_num_batched_tokens=None, max_num_seqs=256, max_logprobs=20, disable_log_stats=False, quantization=None, rope_scaling=None, rope_theta=None, enforce_eager=False, max_context_len_to_capture=None, max_seq_len_to_capture=8192, disable_custom_all_reduce=False, tokenizer_pool_size=0, tokenizer_pool_type='ray', tokenizer_pool_extra_config=None, enable_lora=False, max_loras=1, max_lora_rank=16, lora_extra_vocab_size=256, lora_dtype='auto', long_lora_scaling_factors=None, max_cpu_loras=None, fully_sharded_loras=False, enable_prompt_adapter=False, max_prompt_adapters=1, max_prompt_adapter_token=0, device='auto', scheduler_delay_factor=0.0, enable_chunked_prefill=None, speculative_model=None, num_speculative_tokens=None, speculative_draft_tensor_parallel_size=None, speculative_max_model_len=None, speculative_disable_by_batch_size=None, ngram_prompt_lookup_max=None, ngram_prompt_lookup_min=None, spec_decoding_acceptance_method='rejection_sampler', typical_acceptance_sampler_posterior_threshold=None, typical_acceptance_sampler_posterior_alpha=None, disable_logprobs_during_spec_decoding=None, model_loader_extra_config=None, ignore_patterns=[], preemption_mode=None, served_model_name=None, qlora_adapter_name_or_path=None, otlp_traces_endpoint=None, engine_use_ray=False, disable_log_requests=False, max_log_len=None)
    WARNING 08-19 08:03:57 config.py:1454] Casting torch.bfloat16 to torch.float16.
    WARNING 08-19 08:03:57 arg_utils.py:766] Chunked prefill is enabled by default for models with max_model_len > 32K. Currently, chunked prefill might not work with some features or models. If you encounter any issues, please disable chunked prefill by setting --enable-chunked-prefill=False.
    INFO 08-19 08:03:57 config.py:820] Chunked prefill is enabled with max_num_batched_tokens=512.
    INFO 08-19 08:03:57 llm_engine.py:174] Initializing an LLM engine (v0.5.4) with config: model='/models/Qwen2-0.5B', speculative_config=None, tokenizer='/models/Qwen2-0.5B', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.bfloat16, max_seq_len=131072, download_dir=None, load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None), seed=0, served_model_name=/models/Qwen2-0.5B, use_v2_block_manager=False, enable_prefix_caching=False)
    WARNING 08-19 08:03:57 utils.py:578] Using 'pin_memory=False' as WSL is detected. This may slow down the performance.
    INFO 08-19 08:04:00 model_runner.py:720] Starting to load model /models/Qwen2-0.5B...
    Loading safetensors checkpoint shards:   0% Completed | 0/1 [00:00<?, ?it/s]
    Loading safetensors checkpoint shards: 100% Completed | 1/1 [00:00<00:00,  1.04it/s]
    Loading safetensors checkpoint shards: 100% Completed | 1/1 [00:00<00:00,  1.04it/s]

    INFO 08-19 08:04:01 model_runner.py:732] Loading model weights took 0.9381 GB
    INFO 08-19 08:04:01 gpu_executor.py:102] # GPU blocks: 27607, # CPU blocks: 21845
    INFO 08-19 08:04:02 model_runner.py:1024] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
    INFO 08-19 08:04:02 model_runner.py:1028] CUDA graphs can take additional 1~3 GiB memory per GPU. If you are running out of memory, consider decreasing `gpu_memory_utilization` or enforcing eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
    INFO 08-19 08:04:12 model_runner.py:1225] Graph capturing finished in 9 secs.
    WARNING 08-19 08:04:12 serving_embedding.py:171] embedding_mode is False. Embedding API will not work.
    INFO 08-19 08:04:12 launcher.py:14] Available routes are:
    INFO 08-19 08:04:12 launcher.py:22] Route: /openapi.json, Methods: GET, HEAD
    INFO 08-19 08:04:12 launcher.py:22] Route: /docs, Methods: GET, HEAD
    INFO 08-19 08:04:12 launcher.py:22] Route: /docs/oauth2-redirect, Methods: GET, HEAD
    INFO 08-19 08:04:12 launcher.py:22] Route: /redoc, Methods: GET, HEAD
    INFO 08-19 08:04:12 launcher.py:22] Route: /health, Methods: GET
    INFO 08-19 08:04:12 launcher.py:22] Route: /tokenize, Methods: POST
    INFO 08-19 08:04:12 launcher.py:22] Route: /detokenize, Methods: POST
    INFO 08-19 08:04:12 launcher.py:22] Route: /v1/models, Methods: GET
    INFO 08-19 08:04:12 launcher.py:22] Route: /version, Methods: GET
    INFO 08-19 08:04:12 launcher.py:22] Route: /v1/chat/completions, Methods: POST
    INFO 08-19 08:04:12 launcher.py:22] Route: /v1/completions, Methods: POST
    INFO 08-19 08:04:12 launcher.py:22] Route: /v1/embeddings, Methods: POST
    INFO:     Started server process [1]
    INFO:     Waiting for application startup.
    INFO:     Application startup complete.
    INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
    ```
    Testing vLLM deployment

    ```
    # curl -s http://localhost:8000/v1/completions       -H "Content-Type: application/json"       -d '{
          "model": "/models/Qwen2-0.5B",
          "prompt": "Kubernetes is",
          "max_tokens": 200,
          "temperature": 0
      }'   |jq
    {
    "id": "cmpl-7fe49cdb035040f780416d3035a20567",
    "object": "text_completion",
    "created": 1724054880,
    "model": "/models/Qwen2-0.5B",
    "choices": [
        {
        "index": 0,
        "text": " a container orchestration platform that allows you to deploy, scale, and manage applications in a single, centralized environment. It is designed to be easy to use and flexible, with a focus on simplicity and scalability. Kubernetes is a popular choice for many organizations, and it is becoming increasingly popular as a tool for managing containerized applications. In this article, we will explore the basics of Kubernetes and how it can be used to manage containerized applications.\n\nWhat is Kubernetes?\nKubernetes is a container orchestration platform that allows you to deploy, scale, and manage applications in a single, centralized environment. It is designed to be easy to use and flexible, with a focus on simplicity and scalability. Kubernetes is a popular choice for many organizations, and it is becoming increasingly popular as a tool for managing containerized applications.\n\nWhat is a container?\nA container is a software package that runs on a host machine. Containers are lightweight, portable applications that can be run on any machine that has a processor and memory.",
        "logprobs": null,
        "finish_reason": "length",
        "stop_reason": null
        }
    ],
    "usage": {
        "prompt_tokens": 3,
        "total_tokens": 203,
        "completion_tokens": 200
    }
    }
    ```