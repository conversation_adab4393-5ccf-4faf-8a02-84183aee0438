# Kubeflow Exploration
Initially, we thought that Charmed Kubeflow automated many tasks for us, helping us configure and link various components of Kubeflow. After researching, we found that Charmed Kubeflow likely only validated the functionality of the open-source solution and did not provide the value add we anticipated. To prove this, I tried installing the Kubeflow platform on a k3s cluster in WSL on my laptop and found that from a functional perspective, there wasn't much difference compared to Charmed Kubeflow.
## What is Kubeflow
Kubeflow is a community and ecosystem of open-source projects to address each stage in the machine learning (ML) lifecycle with support for best-in-class open source tools and frameworks. Kubeflow makes AI/ML on Kubernetes simple, portable, and scalable.
## What is Kubeflow Platform
The Kubeflow Platform refers to the full suite of Kubeflow components bundled together with additional integration and management tools. Using Kubeflow as a platform means deploying a comprehensive ML toolkit for the entire ML lifecycle.

In addition to the standalone Kubeflow components, the Kubeflow Platform includes

* Kubeflow Notebooks for interactive data exploration and model development.
* Central Dashboard for easy navigation and management with Kubeflow Profiles for access control.

Additional tooling for data management (PVC Viewer), visualization (TensorBoards), and more.

## Install Kubeflow Platfrom on K3s
### Prerequisites
* `Kubernetes` (up to `1.26`) with a default StorageClass
* `kustomize`
* `kubectl`
### Installing
```
git clone https://github.com/kubeflow/manifests.git
cd manifest
while ! kustomize build example | kubectl apply -f -; do echo "Retrying to apply resources"; sleep 10; done
```
Once, everything is installed successfully, you can access the Kubeflow Central Dashboard by logging in to your cluster.

Below are pods deployed on the k3s cluster:
```
root@W112JHRCK3:/home/<USER>/manifests# kubectl get pods,pvc,sc -A
NAMESPACE                   NAME                                                         READY   STATUS             RESTARTS       AGE
auth                        pod/dex-5f5c9d96bf-mfx7j                                     1/1     Running            0              26m
cert-manager                pod/cert-manager-5fcfc99f7-mlw4c                             1/1     Running            0              26m
cert-manager                pod/cert-manager-cainjector-75cfc9f6b7-v8hkr                 1/1     Running            0              26m
cert-manager                pod/cert-manager-webhook-74b65dbf6f-wr2sx                    1/1     Running            0              26m
istio-system                pod/cluster-local-gateway-64565d7fc8-zzd4b                   1/1     Running            0              26m
istio-system                pod/istio-ingressgateway-646db9cf8f-cpftq                    1/1     Running            0              26m
istio-system                pod/istiod-78f6c4898b-98nlf                                  1/1     Running            0              26m
istio-system                pod/kubeflow-m2m-oidc-configurator-28737195-z5252            0/1     Running            1 (11s ago)    17s
knative-serving             pod/activator-7d8b9586c-cfsq2                                2/2     Running            0              25m
knative-serving             pod/autoscaler-bbf5c5b76-tgfr7                               2/2     Running            0              25m
knative-serving             pod/controller-56c5d7db7b-dszdg                              2/2     Running            0              25m
knative-serving             pod/net-istio-controller-67dcc79c56-szrch                    2/2     Running            0              25m
knative-serving             pod/net-istio-webhook-75b7f9d555-d78nb                       2/2     Running            0              25m
knative-serving             pod/webhook-6d8468dd7f-cnvsb                                 2/2     Running            0              25m
kube-system                 pod/coredns-576bfc4dc7-k4rg6                                 1/1     Running            3 (178m ago)   2d3h
kube-system                 pod/helm-install-nvidia-device-plugin-78g48                  0/1     Completed          0              2d3h
kube-system                 pod/helm-install-traefik-bsh58                               0/1     Completed          1              2d3h
kube-system                 pod/helm-install-traefik-crd-qrrzt                           0/1     Completed          0              2d3h
kube-system                 pod/local-path-provisioner-6795b5f9d8-k5z6p                  1/1     Running            3 (178m ago)   2d3h
kube-system                 pod/metrics-server-557ff575fb-gjv8v                          1/1     Running            3 (178m ago)   2d3h
kube-system                 pod/nvidia-device-plugin-daemonset-7b4nw                     1/1     Running            3 (178m ago)   2d3h
kube-system                 pod/svclb-traefik-c744d731-jgdc2                             2/2     Running            6 (178m ago)   2d3h
kube-system                 pod/traefik-5fb479b77-8zxvv                                  1/1     Running            3 (178m ago)   2d3h
kubeflow-user-example-com   pod/ml-pipeline-ui-artifact-56dcf4989-ddj7w                  2/2     Running            0              24m
kubeflow-user-example-com   pod/ml-pipeline-visualizationserver-58887bbbf5-w9lbh         2/2     Running            0              24m
kubeflow                    pod/admission-webhook-deployment-6dfbf7c8c6-zhqkd            1/1     Running            0              25m
kubeflow                    pod/cache-server-58f5d8c7d5-624cq                            2/2     Running            0              25m
kubeflow                    pod/centraldashboard-d6f49bb67-swdgj                         2/2     Running            0              25m
kubeflow                    pod/jupyter-web-app-deployment-bc78b48f8-6r5hb               2/2     Running            0              25m
kubeflow                    pod/katib-controller-754877f9f-lbzwp                         1/1     Running            0              25m
kubeflow                    pod/katib-db-manager-64d9c694dd-hjxk2                        1/1     Running            1 (24m ago)    25m
kubeflow                    pod/katib-mysql-74f9795f8b-wjcm7                             1/1     Running            0              25m
kubeflow                    pod/katib-ui-858f447bfb-dbqjc                                2/2     Running            0              25m
kubeflow                    pod/kserve-controller-manager-b96c76496-r4zpj                2/2     Running            0              25m
kubeflow                    pod/kserve-models-web-app-5d7d5857df-j8qxv                   2/2     Running            0              25m
kubeflow                    pod/kubeflow-pipelines-profile-controller-7795c68cfd-q5zb5   1/1     Running            0              25m
kubeflow                    pod/metacontroller-0                                         1/1     Running            0              25m
kubeflow                    pod/metadata-envoy-deployment-758c78ccb9-lbcfv               1/1     Running            0              25m
kubeflow                    pod/metadata-grpc-deployment-68d6f447cc-72frw                2/2     Running            3 (24m ago)    25m
kubeflow                    pod/metadata-writer-5495b59ffd-bl9lz                         2/2     Running            0              25m
kubeflow                    pod/minio-59b68688b5-jd9kc                                   2/2     Running            0              25m
kubeflow                    pod/ml-pipeline-cd5945965-dqnx9                              2/2     Running            2 (24m ago)    25m
kubeflow                    pod/ml-pipeline-persistenceagent-5679776bbf-jcgzb            2/2     Running            0              25m
kubeflow                    pod/ml-pipeline-scheduledworkflow-b6658c7c-vl9kx             2/2     Running            0              25m
kubeflow                    pod/ml-pipeline-ui-577d569cc8-9jm55                          2/2     Running            0              25m
kubeflow                    pod/ml-pipeline-viewer-crd-56d7584db6-pnv2f                  2/2     Running            1 (25m ago)    25m
kubeflow                    pod/ml-pipeline-visualizationserver-5f57d6b9cf-jr8n2         2/2     Running            0              25m
kubeflow                    pod/mysql-5f8cbd6df7-w6wq2                                   2/2     Running            0              25m
kubeflow                    pod/notebook-controller-deployment-5d664bc767-jm6h5          2/2     Running            0              25m
kubeflow                    pod/profiles-deployment-5f69674595-5xj7q                     3/3     Running            0              25m
kubeflow                    pod/pvcviewer-controller-manager-5b8487cb6c-jz2hp            3/3     Running            0              25m
kubeflow                    pod/tensorboard-controller-deployment-59d6c5dc44-h2pvv       3/3     Running            1 (25m ago)    25m
kubeflow                    pod/tensorboards-web-app-deployment-5585954f8f-jt768         2/2     Running            0              25m
kubeflow                    pod/training-operator-78f4df6758-2l58p                       1/1     Running            0              25m
kubeflow                    pod/volumes-web-app-deployment-756c7fb9c7-mskfl              2/2     Running            0              25m
kubeflow                    pod/workflow-controller-7c9c86b578-9fx68                     2/2     Running            0              25m
oauth2-proxy                pod/oauth2-proxy-68df7496c4-cpfjr                            1/1     Running            0              26m
oauth2-proxy                pod/oauth2-proxy-68df7496c4-n2dkl                            1/1     Running            0              26m

NAMESPACE   NAME                                   STATUS   VOLUME                                     CAPACITY   ACCESS MODES   STORAGECLASS   VOLUMEATTRIBUTESCLASS   AGE
kubeflow    persistentvolumeclaim/katib-mysql      Bound    pvc-7d9fe444-85de-4fbd-ba98-9c40696798a2   10Gi       RWO            local-path     <unset>                 26m
kubeflow    persistentvolumeclaim/minio-pvc        Bound    pvc-79d0d868-c363-4483-bd81-2c821b53f300   20Gi       RWO            local-path     <unset>                 26m
kubeflow    persistentvolumeclaim/mysql-pv-claim   Bound    pvc-08a3f0df-2dd2-4112-89c5-88f94a2da2f4   20Gi       RWO            local-path     <unset>                 26m

NAMESPACE   NAME                                               PROVISIONER             RECLAIMPOLICY   VOLUMEBINDINGMODE      ALLOWVOLUMEEXPANSION   AGE
            storageclass.storage.k8s.io/local-path (default)   rancher.io/local-path   Delete          WaitForFirstConsumer   false                  2d3h
```
### Quickstart
To view the console of the `Kubeflow`, you can use the command below:
```
kubectl port-forward svc/istio-ingressgateway -n istio-system 8080:80
```
Then, open `http://localhost:8080` in your bowser

The default <NAME_EMAIL> and the password is 12341234. Once you log in, you will be greeted by the Kubeflow dashboard.

![image](./assets/image1.png)

We can create a notebook on the notebook tab
![image](./assets/image2.png)
Also the pipelines can be visualized
![image](./assets/image3.png)
