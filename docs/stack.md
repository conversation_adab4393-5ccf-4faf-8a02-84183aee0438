## Stack
- PE w/ GPU, [detail in env doc](env.md)
- Ubuntu with driver [Ubuntu 22.04 LTS](https://ubuntu.com/download/server)
- K8s [Charmed Kubernetes](https://ubuntu.com/kubernetes/docs/quickstart)
- k8s storage (select the easiest one) or external storage
- kubeflow https://www.kubeflow.org/
- JupterHub https://jupyter.org/hub
- vLLM https://docs.vllm.ai/en/latest/getting_started/quickstart.html

## Reference
- [How do I create a completely unattended install of Ubuntu](https://askubuntu.com/questions/122505/how-do-i-create-a-completely-unattended-install-of-ubuntu)
- [Install Canonical Kubernetes](https://ubuntu.com/kubernetes/install)
- [Get started with Charmed Kubeflow](https://charmed-kubeflow.io/docs/get-started-with-charmed-kubeflow)


## Stacks (raw data)
Framework BP	AI based dynamic Framework BP currently being designed/developed
Istio Service Mesh	
JuypterHub	Note: Is this required as well as Kubeflow Notebooks?
Kubernetes	K3s is currently standard within Nir team - but can/should we extend to vanilla K8s?
Kubernetes NFS storage class	
Kubernetes Node Management	Increase/decrease cluster size
Kubernetes Upgrading	Upgrade (no downgrade) Kubernetes version
PyTorch	
TensorFlow	
BootOS based deployment into EO	Need to understand scope of baremetal deployment
Compute discovery	"Scan CIDR range for MAC address etc. of potential ECEs
Populate into Inventory Service"
Network discovery	Scan CIDR range for switches and other networking devices and then populate into Inventory Service
Network Switch Management	Utilise existing SFM BPs? Aim for automation in 1.0 not 0.5
AMD ROCm library	Need to deploy out ROCm libraries so they are usable from within Kubernetes apps/pods
DSP deployment	The top level BP that controls/uses all other blueprints
Kubeflow - Dashboard	
Kubeflow - Kserve	
Kubeflow - MPI Operator	
Kubeflow - NoteBooks	
Kubeflow - Training Operator	
Kubernetes additional storage classes	Any additional storage classes specific to DSP
OpenLDAP / OIDC	
Port-configuration (compute)	
Port-configuration (networking)	
vLLM installation	
vLLM model exposure	
Inferencing stack	Stack to be defined and blueprints exposed
Telemetry	"Scope and definition needs review - what do we cover and how?
Align with existing solutions?
Take to Architects discussion"
