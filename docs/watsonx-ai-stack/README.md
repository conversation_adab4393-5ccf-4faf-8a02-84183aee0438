# WatsonX Exploration
A list of software stack in WatsonX AI
## Training and validation
* [CodeFlare](https://github.com/project-codeflare/codeflare)
* [KubeRay](https://github.com/ray-project/kuberay)
* [PyTorch Distributed](https://pytorch.org/tutorials/beginner/dist_overview.html)
* [Kubeflow Training Operator](https://github.com/kubeflow/training-operator)
* [Kueue](https://github.com/kubernetes-sigs/kueue)
## Tuning and inference
* [Kserve](https://github.com/kserve/kserve)
* [fms-hs-tuning](https://github.com/foundation-model-stack/fms-hf-tuning)
* [vLLM](https://github.com/vllm-project/vllm)
* [TGIS](https://github.com/IBM/text-generation-inference)
* [PyTorch](https://github.com/pytorch/pytorch)
* [InstaSlice](https://github.com/project-codeflare/instaslice)
## AI development
* [KubeFlow pipelines](https://github.com/kubeflow/pipelines)
* [Open Data Hub](https://github.com/opendatahub-io)
* [InstructLab](https://github.com/instructlab/instructlab)
