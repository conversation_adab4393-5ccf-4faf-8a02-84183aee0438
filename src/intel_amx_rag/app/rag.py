import os
from langchain_community.vectorstores import Chroma
from langchain_community.embeddings import HuggingFaceBgeEmbeddings
from langchain.schema.output_parser import StrOutputParser
from langchain_community.document_loaders import PyPDFLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema.runnable import RunnablePassthrough
from langchain.prompts import PromptTemplate
from langchain.vectorstores.utils import filter_complex_metadata
from langchain.chains import RetrievalQA
from langchain_openai import ChatOpenAI

inference_server_url = "http://localhost:8333/v1"

llm = ChatOpenAI(
    model="models/Llama-3.2-3B-Instruct-w8a8",
    temperature=0.5,
    max_tokens=None,
    timeout=None,
    max_retries=2,
    openai_api_key="EMPTY",
    openai_api_base=inference_server_url,
)
CHROMA_PATH = "chroma"
# set up embedding model
model_name = "BAAI/bge-small-en"
model_kwargs = {"device": "cpu"}
encode_kwargs = {"normalize_embeddings": True}
hf = HuggingFaceBgeEmbeddings(
    model_name=model_name, model_kwargs=model_kwargs, encode_kwargs=encode_kwargs
)

class ChatPDF:
    vector_store = None
    retriever = None
    chain = None

    def __init__(self):
        self.model = llm
        self.text_splitter = RecursiveCharacterTextSplitter(chunk_size=1024, chunk_overlap=100)
        self.prompt = PromptTemplate.from_template(
            """Use the following pieces of context to answer the question about the story at the end.
            If the context doesn't provide enough information, just say that you don't know, don't try to make up an answer.
            Pay attention to the context of the question rather than just looking for similar keywords in the corpus.
            keep the answer as concise as possible.
            {context}
            Question: {question}
            Helpful Answer:
            """
        )

    def ingest(self, pdf_file_path: str):
        docs = PyPDFLoader(file_path=pdf_file_path).load()
        chunks = self.text_splitter.split_documents(docs)
        chunks = filter_complex_metadata(chunks)

        vector_store = Chroma.from_documents(documents=chunks, embedding=hf, persist_directory=CHROMA_PATH)
        #vector_store = Chroma.from_documents(documents=chunks, embedding=)
        self.retriever = vector_store.as_retriever(
            search_type="similarity_score_threshold",
            search_kwargs={
                "k": 3,
                "score_threshold": 0.5,
            },
        )

        self.chain = RetrievalQA.from_chain_type(llm=llm, chain_type="stuff", retriever=self.retriever)

    def ask(self, query: str):
        if not self.chain:
            return "Please, add a PDF document first."
        result = self.chain.invoke(query)
        print(result)
        return result['result']

    def clear(self):
        self.vector_store = None
        self.retriever = None
        self.chain = None

