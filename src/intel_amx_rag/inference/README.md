# Inference

## Installation
1. Create a Venv

```
python -m venv venv
source venv/bin/activate
```

2. Build wheel from source

```bash
git clone https://github.com/vllm-project/vllm.git
cd vllm
```
First, install recommended compiler. We recommend to use `gcc/g++ >= 12.3.0` as the default compiler to avoid potential problems. For example, on Ubuntu 22.4, you can run:
```bash
sudo apt-get update  -y
sudo apt-get install -y gcc-12 g++-12 libnuma-dev
sudo update-alternatives --install /usr/bin/gcc gcc /usr/bin/gcc-12 10 --slave /usr/bin/g++ g++ /usr/bin/g++-12
```
Second, install Python packages for vLLM CPU backend building:
```
pip install --upgrade pip
pip install cmake>=3.26 wheel packaging ninja "setuptools-scm>=8" numpy
pip install -v -r requirements-cpu.txt --extra-index-url https://download.pytorch.org/whl/cpu
```
Finally, build and install vLLM CPU backend:
```
VLLM_TARGET_DEVICE=cpu python setup.py install
```

## Start inference server
```
export VLLM_CPU_KVCACHE_SPACE=40
export HUGGING_FACE_HUB_TOKEN=*************************************
vllm serve "models/Llama-3.2-3B-Instruct-w8a8" --port 8333
```
Testing
```
# Call the server using curl:
curl -X POST "http://localhost:8333/v1/chat/completions" \
	-H "Content-Type: application/json" \
	--data '{
		"model": "models/Llama-3.2-3B-Instruct-w8a8",
		"messages": [
			{
				"role": "user",
				"content": "What is the capital of France?"
			}
		]
	}'
```