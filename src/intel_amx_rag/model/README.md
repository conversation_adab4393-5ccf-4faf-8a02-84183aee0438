# Overview
This model is optimized for Intel AMX technology to significantly boost the inference performance of large language models. Because Intel AMX accelerates matrix multiplication of bfloat16 and int8 data types, only models quantized to these types can benefit from AMX acceleration. Therefore, we quantize both activations and weights to int8, which reduces the computational load during the prefill stage and alleviates memory bandwidth pressure during the decode stage.


## Installation
```bash
python -m venv venv
source venv/bin/activate
pip install llmcompressor
```
## Processing

```
python model.py
```
