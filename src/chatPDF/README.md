# ChatPDF
ChatPDF backed by vLLM

# Installation
1. install required package
```
python3 -m venv venv
source venv/bin/activate
pip install langchain streamlit streamlit_chat chromadb pypdf fastembed langchain-openai langchain_community
```
2. start the GUI
```
python3 -m streamlit run app.py
```
# Usage
Below is the UI of ChatPDF, which can answer questions based on the content of the file uploaded by the user. It will consume the vLLM inference server that deployed inside k8s cluster.
![usage](./assets/image.png) 
