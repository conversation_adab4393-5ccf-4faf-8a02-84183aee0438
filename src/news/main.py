import os
import sys
from anchor.sdxl_lightning import AnchorGenerator
from content.cnn_news import CnnNewsGenerator
from audio.f5_tts import F5TtsGenerator
from animation.musev import MuseVGenerator
from video.musetalk import MuseTalkGenerator
from caption.caption import CaptionGenerator

current_file_dir = os.path.dirname(os.path.abspath(__file__))


def main():
    output_dir = "/root/yq/news_test2"
    anchor_desc = "a full-body color photo of a middle-aged man wearing a suit and glasses in a studio"

    print(f"generating text file...")
    txtGenerator = CnnNewsGenerator(output_dir)
    txt_file = txtGenerator.generate()
    print(f"get text file {txt_file}\n*******************")

    print(f"generating anchor image...")
    anchorGenerator = AnchorGenerator(output_dir, description=anchor_desc)
    anchor_image_file = anchorGenerator.generate()
    print(f"get anchor image {anchor_image_file}\n*******************")

    print(f"generating audio file...")
    audioGenerator = F5TtsGenerator(output_dir, gen_text_file=txt_file)
    audio_file = audioGenerator.generate()
    print(f"get audio file {audio_file}\n*******************")

    print(f"generating animation file...")
    animationGenerator = MuseVGenerator(output_dir, image_file=anchor_image_file)
    ani_file = animationGenerator.generate()
    print(f"get animation file {ani_file}\n*******************")

    print(f"generating video file...")
    videoGenerator = MuseTalkGenerator(output_dir, animation_file=ani_file, audio_file=audio_file)
    video_file = videoGenerator.generate()
    print(f"get video file {video_file}\n*******************")

    print(f"adding caption to video file...")
    captionGenerator = CaptionGenerator(output_dir, text_file=txt_file, audio_file=audio_file, video_file=video_file)
    caption_video_file = captionGenerator.generate()
    print(f"added caption video file {caption_video_file}\n*******************")

    print("all done!!!")

if __name__ == "__main__":
    sys.exit(main())