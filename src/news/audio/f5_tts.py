import os
import sys
import subprocess
import shutil

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from base import AbstractGenerator

input_dir_indocker = "/workspace/input"
output_dir_indocker = "/workspace/output"

class F5TtsGenerator(AbstractGenerator):
    default_output_filename = "infer_cli_out.wav"

    def __init__(self, output_dir, gen_text_file, ref_text="", ref_audio_file="", speed="1.0"):
        super().__init__(output_dir)
        
        self.gen_text_file = gen_text_file
        self.ref_text = ref_text
        self.ref_audio_file = ref_audio_file
        self.speed = speed
        
    def generate(self) -> str:
        gen_text_filename = os.path.basename(self.gen_text_file)

        opt_mount_cmd = ""
        opt_cmd = ""
        if self.ref_text:
            opt_cmd += f" --ref_text {self.ref_text} "
        if self.ref_audio_file:
            ref_audio_filename = os.path.basename(self.ref_audio_file)
            opt_mount_cmd += f" -v {self.ref_audio_file}:{input_dir_indocker}/{ref_audio_filename}"
            opt_cmd += f" --ref_audio {input_dir_indocker}/{ref_audio_filename} "
        
        cmd = f"docker run --rm --gpus all \
                -v {self.gen_text_file}:{input_dir_indocker}/{gen_text_filename} \
                -v {self.output_dir}:{output_dir_indocker} \
                {opt_mount_cmd} \
                ghcr.io/swivid/f5-tts:main \
                bash -c \"f5-tts_infer-cli --model 'F5-TTS' \
                                        --gen_file {input_dir_indocker}/{gen_text_filename} \
                                        --output_dir {output_dir_indocker} \
                                        --speed {self.speed} \
                                        {opt_cmd} \""
        
        result = subprocess.run(cmd, capture_output=True, text=True, shell=True)

        if result.returncode != 0:
            print("Error:", result.stderr)
            raise Exception(result.stderr)
        else:
            target_filename, _ = os.path.splitext(os.path.basename(self.gen_text_file))
            target_file = os.path.join(self.output_dir, f"{target_filename}.wav")
            output_file = os.path.join(self.output_dir, F5TtsGenerator.default_output_filename)
            shutil.move(output_file, target_file)
            return target_file


if __name__ == "__main__":
    generator = F5TtsGenerator("/root/yq/news_test2", "/root/yq/news_test2/news_2024_11_11.txt")
    print(generator.generate())
