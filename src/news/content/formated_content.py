from transformers import pipeline
import gc
import torch


def format_article(text):
    formated_article_generator = pipeline("text-generation", model="Qwen/Qwen2.5-7B-Instruct", device="cuda")
    
    new_article_prompt = f'''
Write an article based on the {text}.
1. Add a welcoming sentence.
2. Copy each item.
2. Between each item, add some transition sentences.
3. Add an ending sentence to hope audience happy and look forward next meeting.
'''

    messages = [
        {"role": "user", "content": new_article_prompt},
    ]

    try:
        output = formated_article_generator(messages, max_length=5000, do_sample=True)
        generated_text = output[0]['generated_text']

        for item in generated_text:
            if item.get("role") == "assistant":
                return item["content"]
        return ""
    finally:
        # Delete the text_generator and force garbage collection
        del output
        del formated_article_generator
        gc.collect()
        torch.cuda.empty_cache()
