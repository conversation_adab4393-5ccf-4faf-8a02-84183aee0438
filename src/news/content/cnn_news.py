import os
import sys
import requests
from bs4 import BeautifulSoup
from datetime import datetime

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from base import AbstractGenerator
from content.summarize import summarize_article
from content.formated_content import format_article

CNN_URL = "https://www.cnn.com/world/china"
DEFAULT_MAX_HEADLINES = 10
WELCOME_STR = "Welcome to today's China news"
ENDING_STR = "Thanks for watching. See you next day."

class CnnNewsGenerator(AbstractGenerator):
    
    def generate(self) -> str:
        articles = []
        headlines = self.get_headlines_from_cnn()
        for index, hl in enumerate(headlines):
            title = hl['title']
            link = hl['link']

            article = self.get_content_from_url(link, title)
            summarized_article = summarize_article(article)
            articles.append(f"{index}. {summarized_article}")

        formated_article = format_article("\n".join(articles))
        datetime_str = datetime.now().strftime("%Y_%m_%d")
        target_file = os.path.join(self.output_dir, f"news_{datetime_str}.txt") 
        with open(target_file, 'w') as file:
            # file.write(WELCOME_STR)
            file.write(formated_article)
            # file.write(ENDING_STR)
        return target_file

    def get_headlines_from_cnn(self, max_headlines=DEFAULT_MAX_HEADLINES):
        headers = {'User-Agent': 'Mozilla/5.0'}
        headlines = []

        response = requests.get(CNN_URL, headers=headers)
        soup = BeautifulSoup(response.text, 'html.parser')

        divs = soup.find_all('div', class_='card container__item container__item--type-media-image container__item--type-section container_vertical-strip__item container_vertical-strip__item--type-section')
        for div in divs:
            first_link = div.find('a', class_="container__link container__link--type-article container_vertical-strip__link")
            if first_link:
                headline_link = "https://www.cnn.com" + first_link.get('href')
                span = div.find('span', class_='container__headline-text')
                headline_text = span.get_text(strip=True).replace('\xa0', ' ')
                headlines.append({'title': headline_text, 'link': headline_link})

            if len(headlines) >= max_headlines:
                break
        
        divs = soup.find_all('div', class_='card container__item container__item--type-media-image container__item--type-section container_list-headlines__item container_list-headlines__item--type-section')
        for div in divs:
            first_link = div.find('a', class_="container__link container__link--type-article container_list-headlines__link")
            if first_link:
                headline_link = "https://www.cnn.com" + first_link.get('href')
                span = div.find('span', class_='container__headline-text')
                headline_text = span.get_text(strip=True).replace('\xa0', ' ')
                headlines.append({'title': headline_text, 'link': headline_link})

            if len(headlines) > max_headlines:
                break
        
        return headlines

    def get_content_from_url(self, url, title=""):
        headers = {'User-Agent': 'Mozilla/5.0'}
        content = ""

        response = requests.get(url, headers=headers)
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            paragraphs = soup.find_all('p', class_='paragraph inline-placeholder vossi-paragraph')
            content = title + ' '.join([para.get_text() for para in paragraphs])
        else:
            print(f"Failed to retrieve the article. Status code: {response.status_code}")
        return content


if __name__ == "__main__":
    generator = CnnNewsGenerator("/root/yq/news_test")
    print(generator.generate())
