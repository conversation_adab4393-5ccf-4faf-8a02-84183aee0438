# FROM anchorxia/musev:latest
FROM ubuntu:22.04

# Update and install any necessary packages
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    wget \
    git \
    python3 \
    python3-pip \
    software-properties-common \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /workspace

# Clone the MuseV repository
RUN git clone --recursive https://github.com/TMElyralab/MuseTalk.git

WORKDIR /workspace/MuseTalk
RUN pip install -r requirements.txt
RUN pip install --no-cache-dir -U openmim 
RUN mim install mmengine 
RUN mim install "mmcv>=2.0.1" 
RUN mim install "mmdet>=3.1.0" 
RUN mim install "mmpose>=1.1.0"
RUN pip3 install huggingface_hub==0.25.1

# RUN add-apt-repository ppa:jonathonf/ffmpeg-4
RUN apt update
RUN apt -y install ffmpeg

# Donwload models
RUN mkdir models
RUN mkdir -p models/musetalk
RUN mkdir -p models/dwpose
RUN mkdir -p models/whisper
RUN mkdir -p models/sd-vae-ft-mse
RUN mkdir -p models/face-parse-bisent

WORKDIR /workspace/MuseTalk/models/musetalk
RUN wget https://huggingface.co/TMElyralab/MuseTalk/resolve/main/musetalk/musetalk.json?download=true -O musetalk.json
RUN wget https://huggingface.co/TMElyralab/MuseTalk/resolve/main/musetalk/pytorch_model.bin

WORKDIR /workspace/MuseTalk/models/dwpose
RUN wget https://huggingface.co/yzd-v/DWPose/resolve/main/dw-ll_ucoco_384.pth

WORKDIR /workspace/MuseTalk/models/whisper
RUN wget https://openaipublic.azureedge.net/main/whisper/models/65147644a518d12f04e32d6f3b26facc3f8dd46e5390956a9424a650c0ce22b9/tiny.pt

WORKDIR /workspace/MuseTalk/models/sd-vae-ft-mse
RUN wget https://huggingface.co/stabilityai/sd-vae-ft-mse/resolve/main/config.json?download=true -O config.json
RUN wget https://huggingface.co/stabilityai/sd-vae-ft-mse/resolve/main/diffusion_pytorch_model.bin

WORKDIR /workspace/MuseTalk/models/face-parse-bisent
RUN wget https://huggingface.co/vivym/face-parsing-bisenet/resolve/main/79999_iter.pth?download=true -O 79999_iter.pth
RUN wget https://download.pytorch.org/models/resnet18-5c106cde.pth

WORKDIR /workspace/MuseTalk