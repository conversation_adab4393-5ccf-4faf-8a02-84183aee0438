import os
import sys
import subprocess
import shutil
import yaml

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from base import AbstractGenerator

input_dir_indocker = "/workspace/input"
output_dir_indocker = "/workspace/output"
config_dir_indocker = "/workspace/config"

class MuseTalkGenerator(AbstractGenerator):

    def __init__(self, output_dir, animation_file, audio_file):
        super().__init__(output_dir)
        
        self.animation_file = animation_file
        self.audio_file = audio_file
        
    def generate(self) -> str:
        animation_name, _ = os.path.splitext(os.path.basename(self.animation_file))
        audio_name, _ = os.path.splitext(os.path.basename(self.audio_file))
        
        config_file = self.gen_config(audio_name)
        config_filename = os.path.basename(config_file)
        if not os.path.exists(config_file):
            raise Exception("generating config yaml file failed")
        
        cmd = f'docker run -v {config_file}:{config_dir_indocker}/{config_filename} \
                -v {self.animation_file}:{input_dir_indocker}/{os.path.basename(self.animation_file)} \
                -v {self.audio_file}:{input_dir_indocker}/{os.path.basename(self.audio_file)} \
                -v {self.output_dir}:{output_dir_indocker} \
                --gpus all  \
                musetalk_ready:latest \
                bash -i -c " \
                    cd /workspace/MuseTalk && \
                    python3 -m scripts.inference --inference_config {config_dir_indocker}/{config_filename} \
                                                --result_dir {output_dir_indocker} "'
        
        result = subprocess.run(cmd, capture_output=True, text=True, shell=True)

        if result.returncode != 0:
            print("Error:", result.stderr)
            raise Exception(result.stderr)
        else:
            filename_keyword = f"{animation_name}_{audio_name}"
            output_filename = ""
            for file_name in os.listdir(self.output_dir):
                # Check if the keyword is in the file name
                if filename_keyword in file_name:
                    output_filename = file_name
                    break
            if not output_filename:
                raise Exception("no generated animation file")
            
            target_file = os.path.join(self.output_dir, f"{audio_name}.mp4")
            output_file = os.path.join(self.output_dir, output_filename)
            shutil.move(output_file, target_file)
            return target_file

    def gen_config(self, task_name="task_0"):
        config_file = os.path.join(self.output_dir, f"musetalk_config_{task_name}.yaml")

        data = {
            task_name: {
                'video_path': os.path.join(input_dir_indocker, os.path.basename(self.animation_file)),
                'audio_path': os.path.join(input_dir_indocker, os.path.basename(self.audio_file)),
            }
        }

        # Write the data to a YAML file
        if os.path.exists(config_file):
            os.remove(config_file)
        with open(config_file, 'w') as f:
            yaml.dump(data, f, default_flow_style=False)
        return config_file

if __name__ == "__main__":
    generator = MuseTalkGenerator(output_dir="/root/yq/news_test1", \
                                animation_file="/root/yq/news_test2/anchor_2024_11_11.mp4", \
                                audio_file="/root/yq/news_test2/news_2024_11_11.wav")
    print(generator.generate())
