import os
import sys
import torch
import gc
from datetime import datetime
from diffusers import StableDiffusionXLPipeline, UNet2DConditionModel, EulerDiscreteScheduler
from huggingface_hub import hf_hub_download
from safetensors.torch import load_file

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from base import AbstractGenerator

class AnchorGenerator(AbstractGenerator):

    def __init__(self, output_dir, description="A girl smiling stand in grass"):
        super().__init__(output_dir)
        
        self.description = description
        
        base = "stabilityai/stable-diffusion-xl-base-1.0"
        repo = "ByteDance/SDXL-Lightning"
        ckpt = "sdxl_lightning_4step_unet.safetensors" # Use the correct ckpt for your step setting!

        # Load model.
        unet = UNet2DConditionModel.from_config(base, subfolder="unet").to("cuda", torch.float16)
        unet.load_state_dict(load_file(hf_hub_download(repo, ckpt), device="cuda"))
        self.pipe = StableDiffusionXLPipeline.from_pretrained(base, unet=unet, torch_dtype=torch.float16, variant="fp16").to("cuda")

        # Ensure sampler uses "trailing" timesteps.
        self.pipe.scheduler = EulerDiscreteScheduler.from_config(self.pipe.scheduler.config, timestep_spacing="trailing")
        
    def generate(self) -> str:
        try:
            datetime_str = datetime.now().strftime("%Y_%m_%d")
            target_file = os.path.join(self.output_dir, f"anchor_{datetime_str}.png")
            # Ensure using the same inference steps as the loaded model and CFG set to 0.
            output = self.pipe(self.description, num_inference_steps=4, guidance_scale=0)
            output.images[0].save(target_file)
            return target_file
        finally:
            # Delete the text_generator and force garbage collection
            del output
            gc.collect()
            torch.cuda.empty_cache()
    
    def __del__(self):
        del self.pipe

        
if __name__ == "__main__":
    generator = AnchorGenerator("/root/yq/news_test2", "a full-body color photo of a middle-aged man wearing a suit and glasses")
    print(generator.generate())