FROM anchorxia/musev:latest

WORKDIR /workspace

# Clone the MuseV repository
RUN git clone --recursive https://github.com/TMElyralab/MuseV.git

# Set up the PYTHONPATH environment variable
RUN current_dir=$(pwd) && \
    export PYTHONPATH=${PYTHONPATH}:${current_dir}/MuseV && \
    export PYTHONPATH=${PYTHONPATH}:${current_dir}/MuseV/MMCM && \
    export PYTHONPATH=${PYTHONPATH}:${current_dir}/MuseV/diffusers/src && \
    export PYTHONPATH=${PYTHONPATH}:${current_dir}/MuseV/controlnet_aux/src

WORKDIR /workspace/MuseV

# Clone the checkpoints repository
RUN git clone https://huggingface.co/TMElyralab/MuseV ./checkpoints

# Create the .env file with environment variables
RUN echo "export PYTHONPATH='/workspace/MuseV:/workspace/MuseV/MMCM:/workspace/MuseV/diffusers/src:/workspace/MuseV/controlnet_aux/src'" >> ~/.bashrc

