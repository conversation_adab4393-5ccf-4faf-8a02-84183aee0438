import os
import sys
import subprocess
import shutil
import yaml

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from base import AbstractGenerator

input_dir_indocker = "/workspace/input"
output_dir_indocker = "/workspace/output"
config_dir_indocker = "/workspace/config"

class MuseVGenerator(AbstractGenerator):

    def __init__(self, output_dir, image_file):
        super().__init__(output_dir)
        
        self.image_file = image_file
        
    def generate(self) -> str:
        image_filename = os.path.basename(self.image_file)
        image_name, _ = os.path.splitext(image_filename)
        
        config_file = self.gen_config(image_name)
        config_filename = os.path.basename(config_file)
        if not os.path.exists(config_file):
            raise Exception("generating config yaml file failed")
        
        cmd = f'docker run -v {config_file}:{config_dir_indocker}/{config_filename} \
            -v {self.image_file}:{input_dir_indocker}/{image_filename} \
            -v {self.output_dir}:{output_dir_indocker} \
            --gpus all  \
            musev_ready:latest \
            bash -i -c " \
                source ~/.bashrc && \
                cd /workspace/MuseV && \
                python scripts/inference/text2video.py  --sd_model_name majicmixRealv6Fp16   \
                                                        --unet_model_name musev_referencenet \
                                                        --referencenet_model_name musev_referencenet \
                                                        --ip_adapter_model_name musev_referencenet   \
                                                        -test_data_path {config_dir_indocker}/{config_filename}  \
                                                        --output_dir {output_dir_indocker}  \
                                                        --n_batch 1  \
                                                        --target_datas {image_name}  \
                                                        --vision_clip_extractor_class_name ImageClipVisionFeatureExtractor \
                                                        --vision_clip_model_path ./checkpoints/IP-Adapter/models/image_encoder  \
                                                        --time_size 120 \
                                                        --context_overlap 0 \
                                                        --fps 12 "'
        result = subprocess.run(cmd, capture_output=True, text=True, shell=True)

        if result.returncode != 0:
            print("Error:", result.stderr)
            raise Exception(result.stderr)
        else:
            filename_keyword = f"referencenet_case={image_name}"
            output_filename = ""
            for file_name in os.listdir(self.output_dir):
                # Check if the keyword is in the file name
                if filename_keyword in file_name:
                    output_filename = file_name
                    break
            if not output_filename:
                raise Exception("no generated animation file")
            
            target_file = os.path.join(self.output_dir, f"{image_name}.mp4")
            output_file = os.path.join(self.output_dir, output_filename)
            shutil.move(output_file, target_file)
            return target_file

    def gen_config(self, config_name):
        image_file_in_docker = os.path.join(input_dir_indocker, os.path.basename(self.image_file))
        config_file = os.path.join(self.output_dir, f"musev_config_{config_name}.yaml")

        # Define the data to be written to the YAML file
        data = [
            {
                'condition_images': image_file_in_docker,
                'eye_blinks_factor': 1.5,
                'height': 1308,
                'img_length_ratio': 0.957,
                'ipadapter_image': '${.condition_images}',
                'name': config_name,
                'prompt': '(masterpiece, best quality, highres:1),(1man, solo:1),(eye blinks:1.5),(head wave:1.3),(arm wave:1.5)',
                'refer_image': '${.condition_images}',
                'video_path': '',
                'width': 736
            },
        ]

        # Write the data to a YAML file
        if os.path.exists(config_file):
            os.remove(config_file)
        with open(config_file, 'w') as f:
            yaml.dump(data, f, default_flow_style=False)
        return config_file

if __name__ == "__main__":
    generator = MuseVGenerator("/root/yq/news_test2", "/root/yq/news_test2/anchor_2024_11_11.png")
    print(generator.generate())
