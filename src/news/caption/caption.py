import os
import sys
from pydub import AudioSegment
import moviepy.editor as mp

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from base import AbstractGenerator

class CaptionGenerator(AbstractGenerator):

    def __init__(self, output_dir, text_file, audio_file, video_file):
        super().__init__(output_dir)
        
        self.text_file = text_file
        self.audio_file = audio_file
        self.video_file = video_file
        
    def generate(self) -> str:
        video_name, _ = os.path.splitext(os.path.basename(self.video_file))

        text_content = ""
        with open(self.text_file, 'r') as rf:
            text_content = rf.read()
        audio = AudioSegment.from_wav(self.audio_file)
        duration = len(audio) / 1000  # Duration in seconds

        captions = self.gen_captions(text_content, duration)
        target_file = os.path.join(self.output_dir, f"{video_name}_with_caption.mp4")

        self.add_captions_to_video(video_file=self.video_file, captions=captions, output_file=target_file)

        return target_file

    def gen_captions(self, text, duration, max_caption_length=10):
        words = text.split()
        captions = []
        start_time = 0
        for i in range(0, len(words), max_caption_length):
            caption_text = ' '.join(words[i:i + max_caption_length])
            caption_duration = duration / (len(words) / max_caption_length)
            captions.append({"text": caption_text, "start": start_time, "duration": caption_duration})
            start_time += caption_duration
        return captions

    def add_captions_to_video(self, video_file, captions, output_file):
        video = mp.VideoFileClip(video_file)
        video_width = video.size[0]
        video_height = video.size[1]
        
        text_clips = []
        for caption in captions:
            text_clip = mp.TextClip(caption['text'], fontsize=36, color='white', bg_color='black', font='Montserrat', size=(video_width, None))
            text_clip = text_clip.set_position(('center', video_height * 3 / 4)).set_duration(caption['duration']).set_start(caption['start'])
            text_clips.append(text_clip)
        
        final_video = mp.CompositeVideoClip([video] + text_clips)
        final_video.write_videofile(output_file, codec='libx264')


if __name__ == "__main__":
    generator = CaptionGenerator(output_dir="/root/yq/news_test2", \
                                text_file="/root/yq/news_test2/news_2024_11_11.txt", \
                                audio_file="/root/yq/news_test2/news_2024_11_11.wav", \
                                video_file="/root/yq/news_test2/news_2024_11_11.mp4")
    print(generator.generate())
