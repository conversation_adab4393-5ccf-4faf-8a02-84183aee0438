FROM golang:1.22 as builder
WORKDIR /app
COPY . .
RUN go mod tidy
RUN go build -o ubuntu-installer cmd/main.go

FROM ubuntu:22.04
ENV DEBIAN_FRONTEND=noninteractive
ENV ISO_PATH="/input/ubuntu.iso"
ENV CONFIG_PATH="/input/unattended-config"
ENV IDRAC_IP="************"
ENV IDRAC_USERNAME="root"
ENV IDRAC_PASSWORD="760xa123"
RUN apt-get update && apt-get install -y \
    curl \
    genisoimage \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*
WORKDIR /app
COPY --from=builder /app/ubuntu-installer /app/ubuntu-installer
EXPOSE 8080
CMD ["./ubuntu-installer", "-iso", "$ISO_PATH", "-config", "$CONFIG_PATH", "-idrac-ip", "$IDRAC_IP", "-username", "$IDRAC_USERNAME", "-password", "$IDRAC_PASSWORD"]