package main

import (
	"flag"
	"fmt"
	"log"
	"ubuntu-installer/pkg/installer"
)

func main() {
	isoPath := flag.String("iso", "", "Path to the Ubuntu ISO file")
	configPath := flag.String("config", "", "Path to the Ubuntu unattended configuration file")
	idracIP := flag.String("idrac-ip", "", "iDRAC IP address")
	username := flag.String("username", "", "iDRAC username")
	password := flag.String("password", "", "iDRAC password")
	flag.Parse()

	if *isoPath == "" || *configPath == "" || *idracIP == "" || *username == "" || *password == "" {
		log.Fatal("All parameters are required")
	}

	err := installer.Run(*isoPath, *configPath, *idracIP, *username, *password)
	if err != nil {
		log.Fatalf("Installation failed: %v", err)
	}

	fmt.Println("Ubuntu installation completed successfully")
}
