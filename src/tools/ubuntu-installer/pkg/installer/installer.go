package installer

import (
	"fmt"
	"log"
	"net/http"
	"os/exec"
	"time"
)

func Run(isoPath, configPath, idracIP, username, password string) error {
	newISOPath, err := GenerateISO(isoPath, configPath, idracIP)
	if err != nil {
		return fmt.Errorf("failed to generate ISO: %w", err)
	}

	go func() {
		http.Handle("/", http.FileServer(http.Dir("/tmp")))
		log.Fatal(http.ListenAndServe(":8080", nil))
	}()

	err = MountISO(idracIP, username, password, newISOPath)
	if err != nil {
		return fmt.Errorf("failed to mount ISO: %w", err)
	}

	err = RebootServer(idracIP, username, password)
	if err != nil {
		return fmt.Errorf("failed to reboot server: %w", err)
	}

	err = WaitForInstallation(idracIP)
	if err != nil {
		return fmt.Errorf("installation failed: %w", err)
	}

	return nil
}

func GenerateISO(isoPath, configPath, idracIP string) (string, error) {
	newISOPath := fmt.Sprintf("/tmp/%s/ubuntu.iso", idracIP)
	cmd := exec.Command("genisoimage", "-o", newISOPath, "-c", configPath, isoPath)
	err := cmd.Run()
	if err != nil {
		return "", fmt.Errorf("failed to generate ISO: %w", err)
	}
	return newISOPath, nil
}

func MountISO(idracIP, username, password, isoPath string) error {
	cmd := exec.Command("curl", "-k", "-u", fmt.Sprintf("%s:%s", username, password),
		"-X", "POST", "-d", fmt.Sprintf(`{"Image": "%s", "Inserted": true, "WriteProtected": true}`, isoPath),
		fmt.Sprintf("https://%s/redfish/v1/Managers/iDRAC.Embedded.1/VirtualMedia/CD/Actions/VirtualMedia.InsertMedia", idracIP))
	err := cmd.Run()
	if err != nil {
		return fmt.Errorf("failed to mount ISO: %w", err)
	}
	return nil
}

func RebootServer(idracIP, username, password string) error {
	cmd := exec.Command("curl", "-k", "-u", fmt.Sprintf("%s:%s", username, password),
		"-X", "POST", "-d", `{"ResetType": "ForceRestart"}`,
		fmt.Sprintf("https://%s/redfish/v1/Systems/System.Embedded.1/Actions/ComputerSystem.Reset", idracIP))
	err := cmd.Run()
	if err != nil {
		return fmt.Errorf("failed to reboot server: %w", err)
	}
	return nil
}

func WaitForInstallation(idracIP string) error {
	timeout := time.After(30 * time.Minute)
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-timeout:
			return fmt.Errorf("installation timed out")
		case <-ticker.C:
			if checkInstallationComplete(idracIP) {
				return nil
			}
		}
	}
}

func checkInstallationComplete(idracIP string) bool {
	// 这里添加检查安装是否完成的逻辑，例如ping或ssh
	return true
}
