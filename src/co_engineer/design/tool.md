# Tool Interaction Design

## 1. LLM Prompt Structure

### Tool Definition Format
Each tool is defined with:
- **Name**: Prefix with "tool_" (e.g. `tool_file_read`)
- **Description**: Explains purpose and usage
- **Parameters**: List with type and constraints
- **Example**: XML usage example

### Example Tools

#### tool_file_read
Description: Read contents of a file  
Parameters:  
- path: string (required) - File path to read  
Example:  
```xml
<tool_file_read>
<path>src/main.js</path>
</tool_file_read>
```

#### tool_file_write
Description: Write content to a file  
Parameters:  
- path: string (required) - File path to write  
- content: string (required) - Content to write  
Example:  
```xml
<tool_file_write>
<path>src/main.js</path>
<content>
console.log("Hello World");
</content>
</tool_file_write>
```

## 2. Response Handling Implementation

```python
import xml.etree.ElementTree as ET
import re
import os
from typing import Any, Dict

class ToolHandler:
    def __init__(self):
        self.tools = {
            'tool_file_read': self._handle_read,
            'tool_file_write': self._handle_write
        }
        self.tool_schemas = {
            'tool_file_read': {'path': str},
            'tool_file_write': {'path': str, 'content': str}
        }

    def execute(self, llm_response: str) -> str:
        tool_xml = self._extract_tool_xml(llm_response)
        if not tool_xml:
            return "Error: No valid tool call found"
            
        try:
            root = ET.fromstring(tool_xml)
            tool_name = root.tag
            params = {child.tag: child.text.strip() for child in root}
            
            if tool_name not in self.tools:
                return f"Error: Unknown tool {tool_name}"
                
            # Convert parameter types according to the schema
            typed_params = self._convert_params(params, self.tool_schemas[tool_name])
            
            return self.tools[tool_name](**typed_params)
            
        except ET.ParseError:
            return "Error: Invalid XML format"

    def _extract_tool_xml(self, text: str) -> str:
        match = re.search(r'<(tool_\w+)>(.*?)</\1>', text, re.DOTALL)
        return match.group(0) if match else None

    def _convert_params(self, params: Dict[str, str], schema: Dict[str, Any]) -> Dict[str, Any]:
        typed_params = {}
        for key, value in params.items():
            if key in schema:
                if schema[key] == int:
                    typed_params[key] = int(value)
                elif schema[key] == float:
                    typed_params[key] = float(value)
                else:
                    typed_params[key] = value
            else:
                return f"Error: Parameter {key} not defined in schema"
        return typed_params

    def _handle_read(self, path: str) -> str:
        try:
            with open(path, 'r') as f:
                return f.read()
        except Exception as e:
            return f"Read error: {str(e)}"

    def _handle_write(self, path: str, content: str) -> str:
        try:
            os.makedirs(os.path.dirname(path), exist_ok=True)
            with open(path, 'w') as f:
                f.write(content)
            return f"File {path} updated"
        except Exception as e:
            return f"Write error: {str(e)}"
```

## 3. Tool Mapping Implementation

```python
# Initialize handler
tool_handler = ToolHandler()

# Example LLM response
llm_response = '''
Here's the file content:
<tool_file_write>
<path>demo.txt</path>
<content>Sample content</content>
</tool_file_write>
'''

# Execute tool call
result = tool_handler.execute(llm_response)
print(result)  # Output: "File demo.txt updated"
```

## 4. Adding New Tools Guide

### Step-by-Step Guide

1. **Define Tool in Prompt**:
```markdown
### tool_file_delete
Description: Delete a file  
Parameters:  
- path: string (required) - File to delete  
Example:  
<tool_file_delete>
<path>temp.txt</path>
</tool_file_delete>
```

2. **Implement Handler Method**:
```python
def _handle_delete(self, path: str) -> str:
    try:
        os.remove(path)
        return f"Deleted {path}"
    except Exception as e:
        return f"Delete error: {str(e)}"
```

3. **Register Tool**:
```python
def __init__(self):
    self.tools = {
        ...
        'tool_file_delete': self._handle_delete
    }
    self.tool_schemas = {
        ...
        'tool_file_delete': {'path': str}
    }
```

4. **Update Error Handling**:
```python
# Add new error types as needed
```

5. **Test Integration**:
```python
# Test valid call
assert tool_handler.execute("""
<tool_file_delete>
<path>temp.txt</path>
</tool_file_delete>
""") == "Deleted temp.txt"

# Test missing path
assert "Delete error" in tool_handler.execute(
"""<tool_file_delete>
<path>temp.txt</path>
</tool_file_delete>"""
)
```
