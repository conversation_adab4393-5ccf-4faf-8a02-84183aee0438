# DPC Multi-Agent System Architecture

## Component Diagram

```mermaid
graph TB
    SystemDesigner["SystemDesigner (Assistant)"]
    D<PERSON>Researcher["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Assistant)"]
    NanoDev["NanoDev (Agent)"]
    BlueprintDev["BlueprintDev (Agent)"]
    OrchestratorDev["Orchestra<PERSON><PERSON><PERSON> (Agent)"]
    DPCKnowledgeBase["DPCKnowledgeBase (Knowledge Base)"]

    SystemDesigner -->|Assigns DPCstructure tasks| NanoDev
    SystemDesigner -->|Assigns blueprint tasks| BlueprintDev
    SystemDesigner -->|Assigns orchestration tasks| OrchestratorDev
    DPCResearcher -->|Queries validation data| DPCKnowledgeBase
    NanoDev -->|Accesses deployment guides| DPCKnowledgeBase
    BlueprintDev -->|Retrieves template specs| DPCKnowledgeBase
    OrchestratorDev -->|Gets orchestration patterns| DPCKnowledgeBase
    DPCResearcher -->|Stores validated procedures| DPCKnowledgeBase
```

## Component Responsibilities

### SystemDesigner (LLM Assistant)
Core responsibility: User requirement interface, breakdown High Level Requirement into Agent Tasks

### D<PERSON><PERSON><PERSON><PERSON><PERSON> (LLM Assistant) 
Core responsibility: DPC Technical solution validation and documentation

### NanoDev (LLM Agent)
Core responsibility: Nano Scripts Developement

### BlueprintDev (LLM Agent)
Core responsibility: blueprint Developement

### OrchestratorDev (LLM Agent)
Core responsibility: Workflow Developement 

### DPCKnowledgeBase
Core responsibility: DPC Technical knowledge repository



# Design

## 目标
通过Agent的方式来帮助Private Cloud的产品研发，包括
- 新的Private Cloud的研究，比如如何搭建新的Private Cloud Cluster的资料收集和操作手册
- 在实际的环境中操作测试汇总信息的正确性，调整操作手册
- 基于测试结果生成可以重复操作的代码

## 方法
通过多个Agent，在关键步骤中假如人为验证和确认
- Research Agent，可以根据用户提供的资料和网上的信息，生成针对Private Cloud的一个操作手册
- Operator Agent, 通过操作手册，在测试环境上测试，验证操作手册中错误的内容
- Dev Agent, 基于操作手册的内容，形成代码

### 总体流程
```mermaid
graph TD
    A[生成操作手册] --> B[验证操作手册]
    B --> C[架构文档,手写已有]
    C --> D[代码生成]
    D --> E[测试]
    E --> F[打包发布]
```


### 生成操作手册
- 输入: 任务描述和参考资料
- 输出: 操作手册
```mermaid
flowchart TD
    A[用户输入研究任务描述] --> B[研究Agent生成操作手册]
    B --> C[保存到/research目录并提交PR到git]
    C --> D{用户Review}
    D --> |有Comments| E[Research Agent修改操作手册]
    E --> C
    D --> |无Comments| F[流程结束]
```

### 验证操作手册
- 输入: 操作手册，测试所需的软硬件信息
- 输出: 验证结果
```mermaid
flowchart TD
    A[用户准备测试软硬件信息] --> B[保存为research/verification/*.md]
    B --> C[用户发起操作手册验证]
    C --> D[Operator Agent验证手册]
    D --> E{验证通过?}
    E --> |否| F[记录错误并输出结果]
    E --> |是| G[记录验证过程并输出结果]
    F --> H[结束流程]
    G --> H
```

### 代码生成
- 输入: 验证通过的操作手册，架构，测试软硬件信息
- 输出：代码和测试结果
```mermaid
graph TD
    A[增加Memory Bank] --> B[构建项目描述文件]
    B --> C[Chat Window开发测试]
    C --> D[推送代码到Git]
```


## 实例
Nutanix Cluster Setup

```mermaid
flowchart TD
    A[User Input\n- Node count\n- Storage capacity\n- Network config] --> B[Research Agent]
    B --> |Generates| C[Draft Operational Procedure]
    C --> D[Human SME Review]
    D --> |Approves| E[Operator Agent]
    E --> |Executes| F[Test Cluster]
    F --> G{Errors?}
    G -->|Yes| H[Human Intervention]
    H --> |Corrected Procedure| E
    G -->|No| I[Dev Agent]
    I --> |Generates| J[Automation Code]
    J --> K[Code Review & Testing]
    K --> L[Production Deployment]
```

## 项目文件夹的结构


## PEAgent
### 模块
- MultiStep Agent - smolagent
- Knowledge Base MCP - https://github.com/chroma-core/chroma-mcp
- File Operation - https://github.com/mark3labs/mcp-filesystem-server
- Git Operation - already have
- Terminal - https://github.com/MladenSU/cli-mcp-server
- Prompot (to dev)
- 测试环境MCP

### 知识库
- 项目的结构规划
- PowerEdge的领域知识，通过idarc， redfish连接和管理
- PowerEdge的样例代码，repo库
- 基本的工作流程 
    - 代码生成，根据生成代码和测试用例，测试修改代码，更新接口文档
- 模块的设计 - 每个都是一个可执行的脚本
- 



