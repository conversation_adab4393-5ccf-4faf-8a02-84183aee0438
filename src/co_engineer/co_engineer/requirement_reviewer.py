import asyncio
from autogen_agentchat.agents import <PERSON><PERSON><PERSON>
from autogen_agentchat.ui import <PERSON>sole
from co_engineer.models import model_client

system_prompt = """
You are the reviewer of the engineer requirement, make sure the engineer requirement is understandable and doable 
"""

# Define an AssistantAgent with the model, tool, system message, and reflection enabled.
# The system message instructs the agent via natural language.
agent = AssistantAgent(
    name="weather_agent",
    model_client=model_client,
    system_message="You are a helpful assistant.",
    reflect_on_tool_use=True,
    model_client_stream=True,  # Enable streaming tokens from the model client.
)

# Run the agent and stream the messages to the console.
