import asyncio
import json
import os
from autogen_ext.tools.mcp import (
    StdioServerParams,
    SseServerParams,
    mcp_server_tools
)

mcp_setting_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "../mcp_settings.json")

async def load_all_mcp_tools(config_path: str):
    all_tools = []

    with open(config_path, "r") as f:
        config = json.load(f)

    mcp_servers = config.get("mcpServers", {})

    for name, params in mcp_servers.items():
        try:
            if "command" in params:
                server_params = StdioServerParams(
                    command=params["command"],
                    args=params.get("args", []),
                    env=params.get("env", {}),
                    read_timeout_seconds=params.get("timeout", 5)
                )
            elif "url" in params:
                server_params = SseServerParams(
                    url=params["url"],
                    headers=params.get("headers", {}),
                    timeout=params.get("timeout", 5),
                    sse_read_timeout=params.get("sse_read_timeout", 300)
                )
            else:
                print(f"Skipping {name}: Unknown server type.")
                continue

            tools = await mcp_server_tools(server_params)
            all_tools.extend(tools)
        except Exception as e:
            print(f"Failed to load tools from {name}: {e}")
    return all_tools

# Run the asynchronous function
tools = asyncio.run(load_all_mcp_tools(mcp_setting_path))
