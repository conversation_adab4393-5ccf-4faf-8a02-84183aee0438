import xml.etree.ElementTree as ET
import re
import os
import logging
from typing import Any, Dict

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ToolHandler:
    def __init__(self, ext_tools=None):
        self.tools = {}
        self.tool_schemas = {}

        logger.info("Initializing ToolHandler")

        if ext_tools:
            for tool in ext_tools:
                tool_name = tool.__name__
                self.tools[tool_name] = tool
                # Dynamically infer the schema from the function parameters
                schema = self._infer_schema(tool)
                self.tool_schemas[tool_name] = schema
                logger.info(f"Added tool '{tool_name}' with schema {schema}")
    
    def _infer_schema(self, tool):
        import inspect
        sig = inspect.signature(tool)
        schema = {}
        for param in sig.parameters.values():
            if param.annotation is not param.empty:
                schema[param.name] = param.annotation
            else:
                schema[param.name] = str  # Default to str if no annotation is provided
        return schema

    def execute(self, llm_response: str) -> str:
        tool_xml = self._extract_tool_xml(llm_response)
        if not tool_xml:
            return None
            
        try:
            root = ET.fromstring(tool_xml)
            tool_name = root.tag
            params = {child.tag: child.text.strip() for child in root}
            
            if tool_name not in self.tools:
                return f"Error: Unknown tool {tool_name}"
                
            # Convert parameter types according to the schema
            typed_params = self._convert_params(params, self.tool_schemas[tool_name])
            
            return self.tools[tool_name](**typed_params)
            
        except ET.ParseError:
            return "Error: Invalid XML format"

    def _extract_tool_xml(self, text: str) -> str:
        match = re.search(r'<(tool_\w+)>(.*?)</\1>', text, re.DOTALL)
        return match.group(0) if match else None

    def _convert_params(self, params: Dict[str, str], schema: Dict[str, Any]) -> Dict[str, Any]:
        typed_params = {}
        for key, value in params.items():
            if key in schema:
                if schema[key] == int:
                    typed_params[key] = int(value)
                elif schema[key] == float:
                    typed_params[key] = float(value)
                else:
                    typed_params[key] = value
            else:
                return f"Error: Parameter {key} not defined in schema"
        return typed_params
