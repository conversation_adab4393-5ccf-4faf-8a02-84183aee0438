from autogen_ext.models.openai import OpenAIChatCompletionClient

# Define a model client. You can use other model client that implements
# the `ChatCompletionClient` interface.
model_client_deepseek_r1 = OpenAIChatCompletionClient(
    model="deepseek-R1",
    base_url="http://100.80.20.5:4000/v1",
    api_key="API_KEY",
    temperature=0.2,
    model_capabilities={
        "vision": False,
        "function_calling": True,
        "json_output": False,
    }
)

model_client_qwq = OpenAIChatCompletionClient(
    model="QwQ-32B",
    base_url="http://100.80.20.5:4000/v1",
    api_key="API_KEY",
    model_capabilities={
        "vision": False,
        "function_calling": True,
        "json_output": False,
    }
)

model_client_deepseek_v3_0324 = OpenAIChatCompletionClient(
    model="DeepSeek-V3-0324",
    base_url="http://100.80.20.5:4000/v1",
    api_key="API_KEY",
    model_capabilities={
        "vision": False,
        "function_calling": True,
        "json_output": False,
    }
)
