#!/usr/bin/env python3
"""
Command-line interface for the KVM Virtual Machine Creation Agent.

This script provides a command-line interface for creating KVM virtual machines
using cloud-init for automated installations.
"""

import argparse
import asyncio
import logging
import os
import sys

from autogen_core import CancellationToken
from autogen_agentchat.ui import Console

# Add the parent directory to the path so we can import the agent module
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from kvm.agent import create_agent, create_target_disk, run_installation, boot_installed_system, create_kvm_vm

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def interactive_mode():
    """
    Run the agent in interactive mode, allowing the user to chat with the agent.
    """
    agent = await create_agent()

    print("=== KVM Virtual Machine Creation Agent ===")
    print("Type 'exit' or 'quit' to end the conversation.")
    print("Example: Create a KVM virtual machine with a 10GB disk, using the Ubuntu ISO at /path/to/ubuntu.iso and cloud-init seed at /path/to/seed.iso")
    print("=" * 50)

    while True:
        user_input = input("\nYou: ")
        if user_input.lower() in ["exit", "quit"]:
            break

        await Console(
            agent.run_stream(
                task=user_input,
                cancellation_token=CancellationToken()
            )
        )

async def direct_mode(args):
    """
    Create a KVM virtual machine directly using the provided arguments.
    """
    # Create output directory if it doesn't exist
    output_dir = args.output_dir or os.getcwd()
    os.makedirs(output_dir, exist_ok=True)

    if args.create_disk_only:
        # Only create the target disk
        img_path = await create_target_disk(
            size=args.disk_size,
            output_path=output_dir
        )
        print(f"\nTarget disk created successfully: {img_path}")
    elif args.boot_only:
        # Only boot the installed system
        success = await boot_installed_system(
            img_path=args.img_path,
            memory=args.memory
        )
        if success:
            print(f"\nVM booted successfully from: {args.img_path}")
    else:
        # Create a complete KVM VM
        result = await create_kvm_vm(
            img_size=args.disk_size,
            memory=args.memory,
            seed_path=args.seed_path,
            iso_path=args.iso_path,
            output_dir=output_dir
        )
        
        print(f"\nKVM virtual machine created successfully:")
        print(f"Disk image: {result['img_path']}")
        print(f"Cloud-init seed ISO: {result['seed_path']}")
        print(f"Ubuntu ISO: {result['iso_path']}")

def main():
    """
    Main function to parse command-line arguments and run the appropriate mode.
    """
    parser = argparse.ArgumentParser(description="KVM Virtual Machine Creation Agent")
    subparsers = parser.add_subparsers(dest="mode", help="Mode of operation")

    # Interactive mode (default)
    subparsers.add_parser("interactive", help="Interactive mode with the agent")

    # Direct mode
    direct_parser = subparsers.add_parser("direct", help="Direct mode with specific parameters")
    direct_parser.add_argument("--disk-size", default="10G", help="Size of the disk image (e.g., '10G', '20G')")
    direct_parser.add_argument("--memory", default="2048", help="Amount of memory to allocate to the VM (in MB)")
    direct_parser.add_argument("--seed-path", help="Path to the cloud-init seed ISO")
    direct_parser.add_argument("--iso-path", help="Path to the Ubuntu ISO")
    direct_parser.add_argument("--output-dir", help="Directory to save the disk image (default: current directory)")
    direct_parser.add_argument("--create-disk-only", action="store_true", help="Only create the target disk")
    direct_parser.add_argument("--boot-only", action="store_true", help="Only boot the installed system")
    direct_parser.add_argument("--img-path", help="Path to the disk image (required for --boot-only)")

    args = parser.parse_args()

    # Default to interactive mode if no mode is specified
    if not args.mode:
        args.mode = "interactive"

    # Run the appropriate mode
    if args.mode == "interactive":
        asyncio.run(interactive_mode())
    elif args.mode == "direct":
        # Validate arguments
        if args.boot_only and not args.img_path:
            parser.error("--img-path is required when using --boot-only")
        
        asyncio.run(direct_mode(args))

if __name__ == "__main__":
    main()
