#!/usr/bin/env python3
"""
Example script for using the KVM Virtual Machine Creation Agent.

This script demonstrates how to use the agent to create a KVM virtual machine
for automated installations.
"""

import asyncio
import logging
import os
import sys

from autogen_core import CancellationToken
from autogen_agentchat.ui import Console

# Add the parent directory to the path so we can import the agent module
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from kvm.agent import create_agent, create_target_disk, run_installation, boot_installed_system, create_kvm_vm

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def example_with_agent():
    """
    Example using the agent to create a KVM virtual machine.
    """
    # Create the agent
    agent = await create_agent()

    # Define the task for the agent
    task = """
    Create a KVM virtual machine with the following specifications:
    - Disk size: 10GB
    - Memory: 2048MB
    - Ubuntu ISO path: /path/to/ubuntu.iso
    - Cloud-init seed ISO path: /path/to/seed.iso
    
    IMPORTANT: You MUST follow these steps in order:
    1. First, create the target disk
    2. Then, run the installation
    3. Finally, boot the installed system
    
    Do not skip any steps and execute them in this exact order.
    """

    # Run the agent with the task and stream the output
    await Console(
        agent.run_stream(
            task=task,
            cancellation_token=CancellationToken()
        )
    )

    print("\nAgent execution completed.")
    print("Check the current directory for the created disk image.")

async def example_with_direct_api():
    """
    Example using the API directly to create a KVM virtual machine.
    """
    print("=== Direct API Example ===\n")

    # Step 1: Create a target disk
    print("Step 1: Creating target disk...")
    img_path = await create_target_disk(
        size="10G",
        output_path=os.getcwd()
    )
    print(f"Target disk created at: {img_path}\n")

    # For demonstration purposes, we'll assume the ISO paths
    # In a real scenario, you would need to provide actual paths
    seed_path = "/path/to/seed.iso"
    iso_path = "/path/to/ubuntu.iso"

    # Step 2: Run the installation (commented out for demonstration)
    print("Step 2: Running installation...")
    print("(This step is commented out in the example to avoid actual execution)")
    await run_installation(
        img_path=img_path,
        seed_path=seed_path,
        iso_path=iso_path,
        memory="2048"
    )
    print("Installation would be run with the following command:")
    print(f"kvm -no-reboot -m 2048 \\")
    print(f"    -drive file={img_path},format=raw,cache=none,if=virtio \\")
    print(f"    -drive file={seed_path},format=raw,cache=none,if=virtio \\")
    print(f"    -cdrom {iso_path} \\")
    print(f"    -kernel /mnt/casper/vmlinuz \\")
    print(f"    -initrd /mnt/casper/initrd \\")
    print(f"    -append 'autoinstall' \\")
    print(f"    -no-reboot\n")

    # Step 3: Boot the installed system (commented out for demonstration)
    print("Step 3: Booting installed system...")
    print("(This step is commented out in the example to avoid actual execution)")
    await boot_installed_system(
        img_path=img_path,
        memory="2048"
    )
    print("VM would be booted with the following command:")
    print(f"kvm -no-reboot -m 2048 \\")
    print(f"    -drive file={img_path},format=raw,cache=none,if=virtio\n")

    print("KVM virtual machine creation process demonstrated successfully!")

async def main():
    """
    Main function to demonstrate the KVM Virtual Machine Creation Agent.
    """
    # Uncomment one of the examples below to run it

    # Example 1: Using the agent
    # await example_with_agent()

    # Example 2: Using the API directly
    await example_with_direct_api()

if __name__ == "__main__":
    asyncio.run(main())
