#!/usr/bin/env python3
"""
KVM Virtual Machine Creation Agent

This agent helps create KVM virtual machines using cloud-init for automated installations.
It provides tools to create disk images, run installations, and boot the installed system.
"""

import asyncio
import logging
import os
import subprocess
from typing import Dict, List, Optional

from autogen_agentchat.agents import AssistantAgent
from autogen_core import CancellationToken
from co_engineer.models import model_client_qwq

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# System message for the agent
SYSTEM_MESSAGE = """
You are a KVM Virtual Machine Creation assistant. You help users create virtual machines
using KVM and cloud-init for automated deployments.

You MUST ALWAYS follow these steps in this exact order when creating a KVM virtual machine:

STEP 1: Create a target disk
   - Create a disk image file for the VM
   - This step is mandatory and must be completed before proceeding

STEP 2: Run the installation
   - Use KVM to run the installation with the cloud-init ISO
   - This step depends on STEP 1 being completed

STEP 3: Boot the installed system
   - Boot the VM using the installed disk image
   - This step depends on STEP 2 being completed

NEVER skip any steps. Always follow the steps in this exact order.

When helping users, explain what you're doing at each step and provide clear instructions.
"""

# Tool functions
async def create_target_disk(size: str = "10G", output_path: Optional[str] = None) -> str:
    """
    Create a target VM disk for the installation.

    Args:
        size: Size of the disk (e.g., "10G", "20G")
        output_path: Path where the disk image should be saved (default: current directory)

    Returns:
        Path to the created disk image file
    """
    if output_path is None:
        output_path = os.getcwd()
    else:
        # Ensure the specified output directory exists
        os.makedirs(output_path, exist_ok=True)

    # Create the disk image file
    img_path = os.path.join(output_path, "image.img")

    logger.info(f"Creating target disk with size {size} at {img_path}")
    try:
        # Use truncate to create the disk image
        subprocess.run(["truncate", "-s", size, img_path], check=True)
        logger.info(f"Created target disk at {img_path}")
        return img_path
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to create target disk: {e}")
        raise

async def run_installation(img_path: str,
                          seed_path: str,
                          iso_path: str,
                          memory: str = "2048") -> bool:
    """
    Run the VM installation using KVM.

    Args:
        img_path: Path to the target disk image
        seed_path: Path to the cloud-init seed ISO
        iso_path: Path to the Ubuntu ISO
        memory: Amount of memory to allocate to the VM (in MB)

    Returns:
        True if the installation was successful
    """
    logger.info(f"Running VM installation with KVM")
    try:
        # Run the KVM command to install the system
        subprocess.run([
            "kvm", "-no-reboot", "-m", memory,
            "-drive", f"file={img_path},format=raw,cache=none,if=virtio",
            "-drive", f"file={seed_path},format=raw,cache=none,if=virtio",
            "-cdrom", iso_path,
            "-kernel", "/mnt/casper/vmlinuz",
            "-initrd", "/mnt/casper/initrd",
            "-append", "autoinstall",
            "-no-reboot"
        ], check=True)

        logger.info("VM installation completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to run VM installation: {e}")
        raise

async def boot_installed_system(img_path: str, memory: str = "2048") -> bool:
    """
    Boot the installed VM system.

    Args:
        img_path: Path to the installed disk image
        memory: Amount of memory to allocate to the VM (in MB)

    Returns:
        True if the boot was successful
    """
    logger.info(f"Booting installed VM system")
    try:
        # Run the KVM command to boot the installed system
        subprocess.run([
            "kvm", "-no-reboot", "-m", memory,
            "-drive", f"file={img_path},format=raw,cache=none,if=virtio"
        ], check=True)

        logger.info("VM booted successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to boot VM: {e}")
        raise

async def create_kvm_vm(img_size: str = "10G",
                       memory: str = "2048",
                       seed_path: str = None,
                       iso_path: str = None,
                       output_dir: Optional[str] = None) -> Dict[str, str]:
    """
    Create a KVM virtual machine with the specified parameters.

    This is a convenience function that combines all the steps:
    1. Create a target disk
    2. Run the installation
    3. Boot the installed system

    Args:
        img_size: Size of the disk image (e.g., "10G", "20G")
        memory: Amount of memory to allocate to the VM (in MB)
        seed_path: Path to the cloud-init seed ISO
        iso_path: Path to the Ubuntu ISO
        output_dir: Directory to save the disk image (default: current directory)

    Returns:
        Dictionary with paths to the created resources
    """
    # Step 1: Create a target disk
    img_path = await create_target_disk(size=img_size, output_path=output_dir)

    # Step 2: Run the installation
    if seed_path and iso_path:
        await run_installation(
            img_path=img_path,
            seed_path=seed_path,
            iso_path=iso_path,
            memory=memory
        )
    else:
        logger.warning("Skipping installation step because seed_path or iso_path is not provided")

    # Step 3: Boot the installed system
    await boot_installed_system(img_path=img_path, memory=memory)

    return {
        "img_path": img_path,
        "seed_path": seed_path,
        "iso_path": iso_path
    }

# Create the agent
async def create_agent():
    """
    Create and configure the KVM Virtual Machine Creation agent.

    Returns:
        Configured AssistantAgent
    """
    model_client = model_client_qwq

    # Create the agent with tools
    agent = AssistantAgent(
        name="kvm_vm_agent",
        model_client=model_client,
        system_message=SYSTEM_MESSAGE,
        tools=[
            create_target_disk,
            run_installation,
            boot_installed_system,
            create_kvm_vm
        ],
        reflect_on_tool_use=True,
        model_client_stream=True,
    )

    logger.info("KVM Virtual Machine Creation agent initialized")
    return agent

async def main():
    """
    Main function to run the KVM Virtual Machine Creation agent.
    """
    agent = await create_agent()

    result = await agent.run(
        task="Create a KVM virtual machine with a 10GB disk, using the Ubuntu ISO at /path/to/ubuntu.iso and cloud-init seed at /path/to/seed.iso",
        cancellation_token=CancellationToken()
    )

    print("\nAgent execution completed.")
    print(f"Result: {result}")

if __name__ == "__main__":
    asyncio.run(main())