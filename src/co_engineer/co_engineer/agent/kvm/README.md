# KVM Virtual Machine Creation Agent

This agent helps create KVM virtual machines using cloud-init for automated installations. It provides tools to create disk images, run installations, and boot installed systems.

## Features

- Create target disk images for KVM virtual machines
- Run automated installations using cloud-init
- Boot installed systems
- Customize disk size, memory, and other VM parameters

## Prerequisites

- Python 3.8 or higher
- AutoGen library
- KVM installed on the host system
- Cloud-init seed ISO (can be created using the ubuntu_cloudinit agent)

## Installation

1. Install the required Python packages:

```bash
pip install autogen-agentchat autogen-core autogen-ext
```

2. Ensure KVM is installed on your system:

```bash
sudo apt-get install qemu-kvm libvirt-daemon-system libvirt-clients bridge-utils
```

## Usage

### Basic Usage

```python
import asyncio
from autogen_core import CancellationToken
from kvm.agent import create_agent

async def main():
    # Create the agent
    agent = await create_agent()

    # Run the agent with a task
    result = await agent.run(
        task="Create a KVM virtual machine with a 10GB disk, using the Ubuntu ISO at /path/to/ubuntu.iso and cloud-init seed at /path/to/seed.iso",
        cancellation_token=CancellationToken()
    )

    print(f"Result: {result}")

if __name__ == "__main__":
    asyncio.run(main())
```

### Example Script

You can run the included example script:

```bash
python -m co_engineer.agent.kvm.example
```

## Command Line Usage

You can use the agent directly from the command line in different modes:

### Interactive Mode

Interactive mode allows you to chat with the agent and ask it to create KVM virtual machines:

```bash
python -m co_engineer.agent.kvm.cli interactive
```

### Direct Mode

Direct mode allows you to create a KVM virtual machine directly with specific parameters:

```bash
python -m co_engineer.agent.kvm.cli direct \
  --disk-size 10G \
  --memory 2048 \
  --seed-path /path/to/seed.iso \
  --iso-path /path/to/ubuntu.iso \
  --output-dir /path/to/output
```

You can also use specific options to perform individual steps:

```bash
# Only create the target disk
python -m co_engineer.agent.kvm.cli direct --create-disk-only --disk-size 10G

# Only boot an existing disk image
python -m co_engineer.agent.kvm.cli direct --boot-only --img-path /path/to/image.img
```

## KVM Virtual Machine Creation Process

The agent follows these steps in order:

1. **Create a target disk**
   - Creates a disk image file for the VM using the `truncate` command
   - Example: `truncate -s 10G image.img`

2. **Run the installation**
   - Uses KVM to run the installation with the cloud-init ISO
   - Example:
     ```bash
     kvm -no-reboot -m 2048 \
         -drive file=image.img,format=raw,cache=none,if=virtio \
         -drive file=seed.iso,format=raw,cache=none,if=virtio \
         -cdrom ubuntu.iso \
         -kernel /mnt/casper/vmlinuz \
         -initrd /mnt/casper/initrd \
         -append 'autoinstall' \
         -no-reboot
     ```

3. **Boot the installed system**
   - Boots the VM using the installed disk image
   - Example:
     ```bash
     kvm -no-reboot -m 2048 \
         -drive file=image.img,format=raw,cache=none,if=virtio
     ```

## Integration with Ubuntu Cloud-Init Agent

This agent works well with the Ubuntu Cloud-Init ISO Generator agent. You can use the cloud-init agent to create a seed ISO, and then use this KVM agent to create and boot a virtual machine with that seed ISO.

Example workflow:

1. Create a cloud-init seed ISO:
   ```bash
   python -m co_engineer.agent.ubuntu_cloudinit.cli direct \
     --hostname my-server \
     --username admin \
     --password secure123 \
     --output-iso seed.iso
   ```

2. Create and boot a KVM virtual machine with the seed ISO:
   ```bash
   python -m co_engineer.agent.kvm.cli direct \
     --seed-path seed.iso \
     --iso-path ubuntu-24.04.2-live-server-amd64.iso
   ```

## Notes

- The agent requires access to the KVM command-line tools
- The cloud-init seed ISO must be properly configured for automated installation
- The Ubuntu ISO should be a server installation image that supports cloud-init
