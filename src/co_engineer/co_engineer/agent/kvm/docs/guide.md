#Create a KVM virtual machine with the following specifications:

## Create a target disk
Create the target VM disk for the installation:
```bash
truncate -s 10G image.img
```

## Run the installation
Change `<iso-path>`, `<seed-path>` and `<img-path>` in the following command to match the release ISO you downloaded.
```bash
kvm -no-reboot -m 2048 \
    -drive file=<img-path>,format=raw,cache=none,if=virtio \
    -drive file=<seed-path>,format=raw,cache=none,if=virtio \
    -cdrom <iso-path> \
    -kernel /mnt/casper/vmlinuz \
    -initrd /mnt/casper/initrd \
    -append 'autoinstall' \
    -no-reboot
```

## Boot the installed system
```bash
kvm -no-reboot -m 2048 \
    -drive file=<img-path>.img,format=raw,cache=none,if=virtio
```
