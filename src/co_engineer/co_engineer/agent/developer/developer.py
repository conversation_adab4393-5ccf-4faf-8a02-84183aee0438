# This is an agent represent a developer
import logging
import requests
import markdownify
from co_engineer.agent.developer.ext_assistant_agent import ExtAssistantAgent
from co_engineer.models import model_client_deepseek_r1
from co_engineer.utils.devenv import Docker<PERSON>anager
from autogen_agentchat.teams import RoundRobinGroupChat
from autogen_agentchat.conditions import TextMentionTermination
from co_engineer.agent.developer.tool_agent import ToolAgent

logger = logging.getLogger(__name__)

system_prompt = """you are a software developer, python expert. Get the engineer requirement, developer and test the code.
- If the requirement is not clear, clarify it first
- Write code to fulfill the requirement
- You have a develop environment ubuntu 22.04, you have full permission on this. 
- Use `tool_file_read` to read the contents of a file
- Use `tool_file_write` to write content to a file
- Use `tool_run_cli` to execute commandline commands
- Use `tool_fetch_web_content` to fetch the web content
- Use one tool per message. 
- If any tool call in return message, don't say TASK_COMPLETE
- say TASK complete when everything is done

# TOOL USE
You have access to a set of tools. You can use one tool per message, and will receive the result of that tool use in the user's response. You use tools step-by-step to accomplish a given task, with each tool use informed by the result of the previous tool use.

# Tool Use Formatting
Each tool is defined with:
- **Name**: Prefix with "tool_" (e.g. `tool_file_read`)
- **Description**: Explains purpose and usage
- **Parameters**: List with type and constraints
- **Example**: XML usage example

# Tools

### tool_file_read
Description: Read contents of a file  
Parameters:  
- path: string (required) - File path to read  
Example:  
```xml
<tool_file_read>
<path>src/main.js</path>
</tool_file_read>
```

### tool_file_write
Description: Write content to a file  
Parameters:  
- path: string (required) - File path to write  
- content: string (required) - Content to write  
Example:  
```xml
<tool_file_write>
<path>src/main.js</path>
<content>
console.log("Hello World");
console.log("Hello World");
</content>
</tool_file_write>
```

### tool_run_cli
Description: Execute a CLI command in the Docker container  
Parameters:  
- command: string (required) - The CLI command to execute  
Example:  
```xml
<tool_run_cli>
<command>npm run dev</command>
</tool_run_cli>
```

### tool_fetch_web_content
Description: Fetch web content  
Parameters:  
- url: string (required) - The URL of the fetch web content
Example:  
```xml
<tool_fetch_web_content>
<url>http://www.google.com</url>
</tool_fetch_web_content>
```

"""

try:
    docker_manager = DockerManager()
    container_id = docker_manager.start_container()
    logger.info(f"Initialized Docker container: {container_id}")
except Exception as e:
    logger.error(f"Failed to initialize Docker environment: {str(e)}")
    raise

def tool_run_cli(command: str) -> str:
    """Execute a CLI command in the Docker container"""
    logger.info(f"Tool call: tool_run_cli with command: {command}")
    try:
        result = docker_manager.run_cli(command)
        logger.debug(f"CLI result: {result[:200]}...")  # Truncate long output
        return result
    except Exception as e:
        logger.error(f"CLI command failed: {str(e)}")
        return f"Command failed: {str(e)}"

def tool_file_read(path: str) -> str:
    """Read a file from the Docker container"""
    logger.info(f"Tool call: tool_read_file with path: {path}")
    try:
        content = docker_manager.read_file(path)
        if content:
            logger.debug(f"Read {len(content)} characters from {path}")
            return f"file {path} read result: ```{content}```"
        return f"file {path} read failed: File not found"
    except Exception as e:
        logger.error(f"File read failed: {str(e)}")
        return f"file {path} read failed: {str(e)}"

def tool_file_write(path: str, content: str) -> str:
    """Write content to a file in the Docker container"""
    logger.info(f"Tool call: tool_write_file with path: {path} and content: {content[:50]}...")  # Truncate long content
    try:
        docker_manager.write_file(path, content)
        logger.debug(f"Successfully wrote to {path}")
        return "File written successfully."
    except Exception as e:
        logger.error(f"File write failed: {str(e)}")
        return f"Write failed: {str(e)}"

def tool_fetch_web_content(url: str) -> str:
    """Fetch web content and convert to markdown"""
    logger.info(f"Tool call: tool_fetch_web_content with url: {url}")
    try:
        # Fetch web content
        response = requests.get(url)
        response.raise_for_status()
        
        # Convert HTML to Markdown
        markdown_content = markdownify.markdownify(response.text)
        
        return f"Fetched content from {url}:\n```markdown\n{markdown_content}\n```"
    except Exception as e:
        logger.error(f"Web fetch failed: {str(e)}")
        return f"Failed to fetch {url}: {str(e)}"
    

_developer_agent = ExtAssistantAgent(
    name="developer_agent",
    model_client=model_client_deepseek_r1,
    system_message=system_prompt,
    # tools=[run_cli, read_file, write_file],
    # ext_tools=[tool_run_cli, tool_read_file, tool_write_file],
    reflect_on_tool_use=True,
    model_client_stream=True,
)

_tool_agent = ToolAgent("tool_agent", ext_tools=[tool_run_cli, tool_file_read, tool_file_write, tool_fetch_web_content])
termination_condition = TextMentionTermination("TASK_COMPLETED", sources=["developer_agent"])
developer_agent = RoundRobinGroupChat(
    [_developer_agent, _tool_agent],
    termination_condition=termination_condition,
)

logger.info("Developer agent initialized with Docker tools")
