# Agent to handle tool call
import logging
from typing import AsyncGenerator, List, Sequence

from autogen_agentchat.agents import BaseChatAgent
from autogen_agentchat.base import Response
from autogen_agentchat.messages import BaseAgentEvent, BaseChatMessage, TextMessage
from autogen_core import CancellationToken
from co_engineer.tool.tool_handler import ToolHandler

logger = logging.getLogger(__name__)

class ToolAgent(BaseChatAgent):
    def __init__(self, name: str, ext_tools):
        super().__init__(name, "Tool Agent.")
        self.tool_handler = Tool<PERSON>andler(ext_tools=ext_tools)

    @property
    def produced_message_types(self) -> Sequence[type[BaseChatMessage]]:
        return (TextMessage,)

    async def on_messages(self, messages: Sequence[BaseChatMessage], cancellation_token: CancellationToken) -> Response:
        # Calls the on_messages_stream.
        response: Response | None = None
        message_content = messages[-1].content
        response = await self.call_tool(message_content)
        # try:
        #     tool_call_result = self.tool_handler.execute(message_content)
        #     if tool_call_result:
        #         # TODO - send response back to agent
        #         response = Response(chat_message=TextMessage(content=tool_call_result, source=self.name))
        #     else:
        #         response = Response(chat_message=TextMessage(content="please continue", source=self.name))
        # except Exception as e:
        #     logger.error(f"Error executing tool: {e}")
        #     response = Response(chat_message=TextMessage(content=f"Error executing tool: {e}", source=self.name))
        # # async for message in self.on_messages_stream(messages, cancellation_token):
        # #     if isinstance(message, Response):
        # #         response = message
        assert response is not None
        return response

    # async def on_messages_stream(
    #     self, messages: Sequence[BaseChatMessage], cancellation_token: CancellationToken
    # ) -> AsyncGenerator[BaseAgentEvent | BaseChatMessage | Response, None]:
    #     message_content = messages[0].content
    #     response = await self.call_tool(message_content)
    #     yield response

    async def on_reset(self, cancellation_token: CancellationToken) -> None:
        pass


    async def call_tool(self, message_content: str) -> Response:
        
        try:
            tool_call_result = self.tool_handler.execute(message_content)
            if tool_call_result:
                # TODO - send response back to agent
                response = Response(chat_message=TextMessage(content=tool_call_result, source=self.name))
            else:
                response = Response(chat_message=TextMessage(content=f"no tool call in message {message_content}", source=self.name))
        except Exception as e:
            logger.error(f"Error executing tool: {e}")
            response = Response(chat_message=TextMessage(content=f"Error executing tool: {e}", source=self.name))
        return response


# Use asyncio.run(run_countdown_agent()) when running in a script.
# await run_countdown_agent()
