#!/usr/bin/env python3
"""
ExtAssistantAgent - A custom assistant agent that overrides message handling methods.
"""

import logging
import re
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.base import Response
from autogen_agentchat.messages import TextMessage
from co_engineer.tool.tool_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>

# Configure logging
# logging.basicConfig(
#     level=logging.INFO,
#     format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
# )
logger = logging.getLogger(__name__)


def split_string(input_string):
    # Split the string at the first occurrence of "</think>"
    parts = input_string.split("</think>", 1)
    
    # Check if the split was successful and return the second part
    if len(parts) > 1:
        return parts[1]
    else:
        return input_string





class ExtAssistantAgent(AssistantAgent):
    
    def __init__(self, *args, ext_tools=None, **kwargs):
        super().__init__(*args, **kwargs)
        # self.tool_handler = Too<PERSON>Handler(ext_tools=ext_tools)


    
    async def on_message(self, message, sender, silent=False):
        """Override base message handling"""
        # Custom logic here
        logger.info(f"Handling message: {message} from {sender}")
        response = await super().on_message(message, sender, silent)
        if isinstance(response, Response):
            response_content = response.chat_message.content
            response_content = split_string(split_string)
            response.chat_message.content = response_content
            # if isinstance(response_content, str):
            #     # tool_calls = self.parse_tool_call(response_content)
            #     try:
            #         tool_call_result = self.tool_handler.execute(response_content)
            #         if tool_call_result:
            #             # TODO - send response back to agent
            #             logger.info(f"Executed tool Result {tool_call_result}")
            #             self.on_message([TextMessage(content=tool_call_result, source="user")])
            #     except Exception as e:
            #         logger.error(f"Error executing tool: {e}")
        return response
        
    async def on_message_stream(self, message, sender, silent=False):
        """Override streaming message handling"""
        # Custom streaming logic here
        logger.info(f"Handling streaming message: {message} from {sender}")
        async for response in super().on_message_stream(message, sender, silent):
            if isinstance(response, Response):
                response_content = response.chat_message.content
                response_content = split_string(split_string)
                response.chat_message.content = response_content
                # if isinstance(response_content, str):
                #     # tool_calls = self.parse_tool_call(response_content)
                #     try:
                #         tool_call_result = self.tool_handler.execute(response_content)
                #         if tool_call_result:
                #             # TODO - send response back to agent
                #             logger.info(f"Executed tool Result {tool_call_result}")
                #             self.on_message([TextMessage(content=tool_call_result, source="user")])
                            
                #     except Exception as e:
                #         logger.error(f"Error executing tool: {e}")
                # else:
                yield response
            else:
                yield response
