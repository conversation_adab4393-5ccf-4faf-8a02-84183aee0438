# Debug 功能使用指南

## 概述

circular_thinker 代理现在支持 debug 模式，可以结构化地显示发送给 OpenAI API 的消息内容。这对于调试和理解代理与模型之间的交互非常有用。

## 功能特性

- 🔍 **JSON格式输出**: 以标准JSON格式显示完整的OpenAI请求和响应
- 📤 **请求调试**: 显示发送给OpenAI API的完整请求数据
- 📥 **响应调试**: 显示从OpenAI API接收的完整响应数据
- 🛠️ **工具信息**: 包含完整的工具定义和调用信息
- 📝 **完整数据**: 不截断任何内容，显示完整的JSON数据

## 使用方法

### 1. 通过 CLI 启用 debug 模式

```bash
# 启用 debug 模式
python cli.py --debug

# 同时设置温度和 debug 模式
python cli.py --temperature 0.7 --debug

# 完整示例
python cli.py --workspace /path/to/workspace --temperature 0.7 --debug --problem-statement "请帮我写一个Python脚本"
```

### 2. 在代码中启用 debug 模式

```python
from utils.llm_client import get_client

# 创建带有 debug 功能的客户端
client = get_client(
    "openai-direct",
    model_name="deepseek-ai/DeepSeek-R1-0528",
    debug=True  # 启用 debug 模式
)
```

### 3. 运行示例

```bash
# 运行 debug 示例
python debug_example.py
```

## Debug 输出格式

当启用 debug 模式时，您将看到类似以下的结构化输出：

```
============================================================
                    OpenAI Request Messages
============================================================

[Message 1]
Role: system
Content: 你是一个有用的编程助手。
----------------------------------------

[Message 2]
Role: user
Content:
  [1] Type: text
      Text: 你好，请帮我写一个Python函数来计算斐波那契数列。
----------------------------------------

============================================================
                        OpenAI Tools
============================================================

[Tool 1]
Type: function
Name: write_code
Description: 写代码的工具
Parameters: {
  "type": "object",
  "properties": {
    "language": {
      "type": "string",
      "description": "编程语言"
    },
    "code": {
      "type": "string",
      "description": "代码内容"
    }
  },
  "required": ["language", "code"],
  "strict": true
}
----------------------------------------
============================================================
```

## 显示的信息类型

### 消息类型
- **System**: 系统提示消息
- **User**: 用户输入消息
- **Assistant**: 助手响应消息（包括工具调用）
- **Tool**: 工具执行结果消息

### 内容类型
- **文本内容**: 显示消息的文本内容（长文本会被截断）
- **工具调用**: 显示工具调用的ID、类型、函数名和参数
- **工具结果**: 显示工具执行的结果

### 工具信息
- **工具名称**: 工具的名称
- **工具描述**: 工具的功能描述
- **参数模式**: 工具接受的参数结构

## 配置选项

### CLI 参数

| 参数 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `--debug` | flag | False | 启用 debug 模式 |
| `--temperature` | float | 0.0 | 模型温度设置 |
| `--workspace` | string | "." | 工作空间路径 |
| `--problem-statement` | string | None | 问题描述 |

### 代码配置

```python
# 在创建客户端时配置
client = get_client(
    "openai-direct",
    model_name="your-model-name",
    debug=True,           # 启用 debug
    temperature=0.7,      # 设置温度
    base_url="your-url",  # 自定义 API 地址
    api_key="your-key"    # 自定义 API 密钥
)
```

## 注意事项

1. **性能影响**: Debug 模式会增加输出，可能会影响性能
2. **敏感信息**: Debug 输出可能包含敏感信息，请谨慎使用
3. **日志文件**: Debug 输出会同时写入日志文件
4. **文本截断**: 长文本会被自动截断以提高可读性

## 故障排除

### 常见问题

1. **没有 debug 输出**: 确保使用了 `--debug` 标志或在代码中设置了 `debug=True`
2. **输出过多**: 可以使用 `--minimize-stdout-logs` 来减少控制台输出
3. **API 错误**: 检查 API 密钥和网络连接

### 示例命令

```bash
# 最小化输出的 debug 模式
python cli.py --debug --minimize-stdout-logs

# 查看日志文件中的 debug 信息
tail -f agent_logs-*.txt
```

## 扩展功能

如果您需要自定义 debug 输出格式，可以修改 `utils/llm_client.py` 中的 `pretty_print_openai_messages` 函数。
