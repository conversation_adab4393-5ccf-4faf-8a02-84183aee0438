# This is an agent representing a verifier
import logging
import asyncio
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.messages import TextMessage
from autogen_core import CancellationToken
from autogen_ext.tools.mcp import StdioServerParams, mcp_server_tools
from co_engineer.models import model_client_qwq
from co_engineer.tools import tools


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

system_prompt = """
You are a software verifier. Get the engineer requirement, generate reasonable test cases, verify the code, and check if the result is correct. At last, generate a verification report.
- You need to know information about the running evnrionment.
- You need to clarify the dependences before executing the code.
- You need to know what are the inputs for executing the code.
- You need to know how to execute the code.
- You need to clarify the check point after executing the code.
- You have a environment ubuntu 22.04 for verifying, you have full permission on this.
- Use tools accordingly to complete your task.

## Verification Report Template
Verification Summary:
Start Time:
End Time:
Test Case:
Running Envrioment Info:
Execution Inputs and Outputs:
Check Points:
    1. Check point 1:
        - Check point:
        - Check method:
        - Check output:
        - Check result:
    2. Check point 2:
        - Check point:
        - Check method:
        - Check output:
        - Check result:
"""

# Get the fetch tool from mcp-server-fetch
async def generate_agent():
    # lab_mcp_server = StdioServerParams(
    #     command="docker", 
    #     args=[
    #         "run",
    #         "-i",
    #         "--rm",
    #         "************:5000/mcp/mcp-lab:latest"
    #     ]
    # )
    # tools = await mcp_server_tools(lab_mcp_server)

    agent = AssistantAgent(
        name="verifier_agent",
        model_client=model_client_qwq,
        system_message=system_prompt,
        tools=tools,
        reflect_on_tool_use=True,
        model_client_stream=True,
    )

    logger.info("Verifier agent initialized")
    return agent



async def main():
    task = """
**As an IT Admin,**  
**I want to create a Python script to install Ubuntu 22.04 on my PowerEdge server,**  
**So that I can automate the OS installation process and ensure consistency across multiple servers.**

**Acceptance Criteria:**
- Criteria 1: The Python script should accept input parameters for the Ubuntu OS image URL and PowerEdge server access information.
- Criteria 2: The script should use iDRAC and Redfish APIs to mount the ISO file on the server.
- Criteria 3: The script should automate the installation process in unattended mode, including partitioning the disk and setting up the necessary configurations.
- Criteria 4: The script should provide logs and error messages for troubleshooting.
- Criteria 5: The script should be tested and verified on a PowerEdge server.

**Definition of Done:**
- All acceptance criteria are met.
- Code is reviewed and approved.
- Necessary tests are written and pass.
- Documentation is updated, if applicable.
- Feature is deployed to the testing environment.

** Outcome:**
- python script named install_ubuntu.py
"""
    verifier_agent = await generate_agent()
    result = await verifier_agent.run(
        task=task,
        cancellation_token=CancellationToken()
    )

    if result and result.messages:
        last_message = result.messages[-1]
        if isinstance(last_message, TextMessage):
            print(last_message.content)
            return
    
    print("Failed to verify.")


if __name__ == "__main__":
    asyncio.run(main())
