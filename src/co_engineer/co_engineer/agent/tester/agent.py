import asyncio
import logging
from google.adk.models.lite_llm import Lite<PERSON><PERSON>
from google.adk import Agent
from co_engineer.agent.a2a.deepseekr1.deepseekr1 import DeepseekR1Llm
from google.adk.tools import built_in_code_execution
from co_engineer.utils.devenv import DockerManager


import datetime
from zoneinfo import ZoneInfo
from google.adk.agents import Agent

logger = logging.getLogger(__name__)

system_prompt = """
You are a Tester of software. You should run test based on the test plan and report the test result.
# Working env
You have full permission of ubuntu 22.04 server, 
- you have full permission on this working env.
- you working folder is /app
- use tool run_cli_command_in_working_env to run command on working env
- use tool file_read_from_working_env to read file from working env
- use tool file_write_to_working_env to write file to working env

"""

hosted_model = DeepseekR1Llm(
    # model="openai/QwQ-32B",
    model="openai/deepseek-R1",
    api_base="http://***********:4000/v1",
    api_key="DUMMY_API_KEY"
)

try:
    docker_manager = DockerManager()
    container_id = docker_manager.start_container()
    logger.info(f"Initialized Docker container: {container_id}")
except Exception as e:
    logger.error(f"Failed to initialize Docker environment: {str(e)}")
    raise

def run_cli_command_in_working_env(command: str) -> str:
    """Executes a CLI command in the working environment.

    Args:
        command (str): The CLI command to execute in the container.

    Returns:
        str: Command output or error message.
    """
    logger.info(f"Tool call: tool_run_cli with command: {command}")
    try:
        result = docker_manager.run_cli(command)
        logger.debug(f"CLI result: {result[:200]}...")  # Truncate long output
        return result
    except Exception as e:
        logger.error(f"CLI command failed: {str(e)}")
        return f"Command failed: {str(e)}"

def file_read_from_working_env(path: str) -> str:
    """Reads file contents from the working environment.

    Args:
        path (str): Path to the file within the container.

    Returns:
        str: File contents or error message.
    """
    logger.info(f"Tool call: tool_read_file with path: {path}")
    try:
        content = docker_manager.read_file(path)
        if content:
            logger.debug(f"Read {len(content)} characters from {path}")
            return f"file {path} read result: ```{content}```"
        return f"file {path} read failed: File not found"
    except Exception as e:
        logger.error(f"File read failed: {str(e)}")
        return f"file {path} read failed: {str(e)}"

def file_write_to_working_env(path: str, content: str) -> str:
    """Writes content to a file in the working environment.

    Args:
        path (str): Path to the file within the working environment.
        content (str): Content to write to the file.

    Returns:
        str: Success message or error description.
    """
    logger.info(f"Tool call: tool_write_file with path: {path} and content: {content[:50]}...")  # Truncate long content
    try:
        docker_manager.write_file(path, content)
        logger.debug(f"Successfully wrote to {path}")
        return "File written successfully."
    except Exception as e:
        logger.error(f"File write failed: {str(e)}")
        return f"Write failed: {str(e)}"

root_agent = Agent(
    name="tester_agent",
    # model="gemini-2.0-flash",
    model=hosted_model,
    description=(
        "Tester Agent."
    ),
    instruction=system_prompt,
    tools=[run_cli_command_in_working_env, file_read_from_working_env, file_write_to_working_env],
)
