#!/usr/bin/env python3
"""
Example script for using the Ubuntu Cloud-Init ISO Generator Agent.

This script demonstrates how to use the agent to create a cloud-init ISO
for automated Ubuntu installations.
"""

import asyncio
import logging
import os
import sys

from autogen_core import CancellationToken
from autogen_agentchat.ui import Console

# Add the parent directory to the path so we can import the agent module
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from ubuntu_cloudinit.agent import create_agent, create_ubuntu_cloudinit_iso, download_ubuntu_iso

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def example_with_agent():
    """
    Example using the agent to create a cloud-init ISO.
    """
    # Create the agent
    agent = await create_agent()

    # Define the task for the agent
    task = """
    Create a cloud-init ISO with the following specifications:
    - Hostname: ubuntu-server
    - Username: admin
    - Password: password123
    - Install the following packages: vim, git, curl
    - Save the ISO to the current directory as 'my-cloud-init.iso'

    IMPORTANT: You MUST follow these steps in order:
    1. First, download the Ubuntu 24.04.2 ISO (check if it exists locally)
    2. Then, create the cloud-init configuration files
    3. Finally, generate the bootable ISO

    Do not skip any steps and execute them in this exact order.
    """

    # Run the agent with the task and stream the output
    await Console(
        agent.run_stream(
            task=task,
            cancellation_token=CancellationToken()
        )
    )

    print("\nAgent execution completed.")
    print("Check the current directory for the generated cloud-init ISO.")

async def example_with_direct_api():
    """
    Example using the API directly to create a cloud-init ISO.
    """
    print("=== Direct API Example ===\n")

    # Step 1: Check if Ubuntu ISO exists locally, download if needed
    print("Step 1: Checking for Ubuntu ISO...")
    ubuntu_iso = await download_ubuntu_iso(
        version="24.04.2",
        architecture="amd64",
        # Let it use the default /tmp/ubuntu-24.04.2/ directory
        output_path=None
    )
    print(f"Ubuntu ISO available at: {ubuntu_iso}\n")

    # Step 2: Create cloud-init ISO with all-in-one function
    print("Step 2: Creating cloud-init ISO...")
    result = await create_ubuntu_cloudinit_iso(
        hostname="ubuntu-server",
        username="admin",
        password="password123",
        packages=["vim", "git", "curl"],
        # Let it use the default /tmp/ubuntu-24.04.2/ directory
        output_dir=None,
        output_iso="my-cloud-init.iso",
        ubuntu_version="24.04.2",
        architecture="amd64"
    )

    print("\nCloud-init ISO created successfully!")
    print(f"Cloud-init ISO: {result['cloud_init_iso']}")
    print(f"User data file: {result['user_data']}")
    print(f"Meta data file: {result['meta_data']}")
    print(f"Ubuntu ISO: {result['ubuntu_iso']}")

async def main():
    """
    Main function to demonstrate the Ubuntu Cloud-Init ISO Generator.
    """
    # Uncomment one of the examples below to run it

    # Example 1: Using the agent
    await example_with_agent()

    # Example 2: Using the API directly
    # await example_with_direct_api()

if __name__ == "__main__":
    asyncio.run(main())
