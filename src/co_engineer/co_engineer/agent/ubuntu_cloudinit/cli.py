#!/usr/bin/env python3
"""
Command-line interface for the Ubuntu Cloud-Init ISO Generator Agent.

This script provides a command-line interface for creating cloud-init ISOs
for automated Ubuntu installations.
"""

import argparse
import asyncio
import logging
import os
import sys

from autogen_core import CancellationToken
from autogen_agentchat.ui import Console

# Add the parent directory to the path so we can import the agent module
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from ubuntu_cloudinit.agent import create_agent, create_cloud_init_config, generate_cloud_init_iso, create_ubuntu_cloudinit_iso, download_ubuntu_iso

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def interactive_mode():
    """
    Run the agent in interactive mode, allowing the user to chat with the agent.
    """
    agent = await create_agent()

    print("=== Ubuntu Cloud-Init ISO Generator Agent ===")
    print("Type 'exit' or 'quit' to end the conversation.")
    print("Example: Create a cloud-init ISO with hostname 'my-server', username 'admin', and password 'secure123'")
    print("=" * 50)

    while True:
        user_input = input("\nYou: ")
        if user_input.lower() in ["exit", "quit"]:
            break

        await Console(
            agent.run_stream(
                task=user_input,
                cancellation_token=CancellationToken()
            )
        )

async def direct_mode(args):
    """
    Create a cloud-init ISO directly using the provided arguments.
    """
    # Create output directory if it doesn't exist
    output_dir = args.output_dir or os.getcwd()
    os.makedirs(output_dir, exist_ok=True)

    # Create cloud-init configuration
    ssh_keys = args.ssh_keys.split(',') if args.ssh_keys else None
    packages = args.packages.split(',') if args.packages else None

    # Check if we should download Ubuntu ISO
    if args.download_ubuntu_iso:
        # Check if Ubuntu ISO exists locally, download if needed
        ubuntu_iso = await download_ubuntu_iso(
            version=args.ubuntu_version,
            architecture=args.architecture,
            output_path=output_dir
        )
        print(f"Ubuntu ISO available at: {ubuntu_iso}")

    # Use the all-in-one function if requested
    if args.all_in_one:
        # Create everything in one step
        output_iso = os.path.join(output_dir, args.output_iso)
        result = await create_ubuntu_cloudinit_iso(
            hostname=args.hostname,
            username=args.username,
            password=args.password,
            ssh_authorized_keys=ssh_keys,
            packages=packages,
            output_dir=output_dir,
            output_iso=output_iso,
            ubuntu_version=args.ubuntu_version,
            architecture=args.architecture
        )

        print(f"\nCloud-init ISO created successfully: {result['cloud_init_iso']}")
        print(f"User data file: {result['user_data']}")
        print(f"Meta data file: {result['meta_data']}")
        print(f"Ubuntu ISO: {result['ubuntu_iso']}")
    else:
        # Create cloud-init configuration
        config_paths = await create_cloud_init_config(
            hostname=args.hostname,
            username=args.username,
            password=args.password,
            ssh_authorized_keys=ssh_keys,
            packages=packages,
            output_dir=output_dir,
            ubuntu_version=args.ubuntu_version
        )

        # Generate cloud-init ISO
        output_iso = os.path.join(output_dir, args.output_iso)
        cloud_init_iso = await generate_cloud_init_iso(
            user_data_path=config_paths["user_data"],
            meta_data_path=config_paths["meta_data"],
            output_iso=output_iso
        )

        print(f"\nCloud-init ISO created successfully: {cloud_init_iso}")
        print(f"User data file: {config_paths['user_data']}")
        print(f"Meta data file: {config_paths['meta_data']}")

def main():
    """
    Main function to parse command-line arguments and run the appropriate mode.
    """
    parser = argparse.ArgumentParser(description="Ubuntu Cloud-Init ISO Generator")
    subparsers = parser.add_subparsers(dest="mode", help="Mode of operation")

    # Interactive mode (default)
    subparsers.add_parser("interactive", help="Interactive mode with the agent")

    # Direct mode
    direct_parser = subparsers.add_parser("direct", help="Create a cloud-init ISO directly")
    direct_parser.add_argument("--hostname", default="ubuntu-server", help="Hostname for the new system")
    direct_parser.add_argument("--username", default="ubuntu", help="Username for the primary user")
    direct_parser.add_argument("--password", default="ubuntu", help="Password for the primary user")
    direct_parser.add_argument("--ssh-keys", help="Comma-separated list of SSH public keys")
    direct_parser.add_argument("--packages", help="Comma-separated list of packages to install")
    direct_parser.add_argument("--output-dir", help="Directory to save the configuration files")
    direct_parser.add_argument("--output-iso", default="cloud-init.iso", help="Filename for the output ISO")
    direct_parser.add_argument("--ubuntu-version", default="24.04.2", help="Ubuntu version to use (e.g., '24.04.2', '22.04')")
    direct_parser.add_argument("--architecture", default="amd64", help="System architecture (e.g., 'amd64', 'arm64')")
    direct_parser.add_argument("--download-ubuntu-iso", action="store_true", help="Download Ubuntu ISO if not available locally")
    direct_parser.add_argument("--all-in-one", action="store_true", help="Use the all-in-one function to create everything in one step")

    args = parser.parse_args()

    # Default to interactive mode if no mode is specified
    if args.mode is None or args.mode == "interactive":
        asyncio.run(interactive_mode())
    elif args.mode == "direct":
        asyncio.run(direct_mode(args))

if __name__ == "__main__":
    main()
