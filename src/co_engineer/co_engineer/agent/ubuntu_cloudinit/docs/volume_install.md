# Autoinstall quick start[¶](https://canonical-subiquity.readthedocs-hosted.com/en/latest/howto/autoinstall-quickstart.html#autoinstall-quick-start "Link to this heading")
This guide provides instructions on how to use autoinstall with a current version of Ubuntu for the amd64 architecture in a virtual machine (VM) on your computer.
For older Ubuntu releases, substitute the version in the name of the ISO image. The instructions should otherwise be the same. See [Autoinstall quick start for s390x](https://canonical-subiquity.readthedocs-hosted.com/en/latest/howto/autoinstall-quickstart-s390x.html#autoinstall-quick-start-s390x) for instructions on installing on the s390x architecture.
## Providing the autoinstall data over the network[¶](https://canonical-subiquity.readthedocs-hosted.com/en/latest/howto/autoinstall-quickstart.html#providing-the-autoinstall-data-over-the-network "Link to this heading")
This method describes a network-based installation. When booting over a network, use this method to deliver autoinstall to perform the installation.
## Using volume to provide the autoinstall configuration[¶](https://canonical-subiquity.readthedocs-hosted.com/en/latest/howto/autoinstall-quickstart.html#using-another-volume-to-provide-the-autoinstall-configuration "Link to this heading")
Use this method to create an installation medium to plug into a computer to have it be installed.
### Download the ISO[¶](https://canonical-subiquity.readthedocs-hosted.com/en/latest/howto/autoinstall-quickstart.html#download-the-iso "Link to this heading")
Download the latest release of the Ubuntu Server image (ISO) from the [Ubuntu ISO download page](https://releases.ubuntu.com/) (currently 24.04).
### Create user-data and meta-data files[¶](https://canonical-subiquity.readthedocs-hosted.com/en/latest/howto/autoinstall-quickstart.html#create-user-data-and-meta-data-files "Link to this heading")
```bash
mkdir -p ~/cidata
cd ~/cidata
cat > user-data << 'EOF'
#cloud-config
autoinstall:
  version: 1
  identity:
    hostname: ubuntu-server
    password: "$6$exDY1mhS4KUYCE/2$zmn9ToZwTKLhCw.b4/b.ZRTIZM30JZ4QrOQ2aOXJ8yk96xpcCof0kxKwuX1kqLG/ygbJ1f8wxED22bTL4F46P0"
    username: ubuntu
EOF
touch meta-data
```
The encrypted password is `ubuntu`.
### Create an ISO to use as a cloud-init data source[¶](https://canonical-subiquity.readthedocs-hosted.com/en/latest/howto/autoinstall-quickstart.html#create-an-iso-to-use-as-a-cloud-init-data-source "Link to this heading")
Install utilities for working with cloud images:
```bash
sudo apt install cloud-image-utils
```
Create the ISO image for cloud-init:
```bash
cloud-localds ~/seed.iso user-data meta-data
```
