# Autoinstall quick start[¶](https://canonical-subiquity.readthedocs-hosted.com/en/latest/howto/autoinstall-quickstart.html#autoinstall-quick-start "Link to this heading")
This guide provides instructions on how to use autoinstall with a current version of Ubuntu for the amd64 architecture in a virtual machine (VM) on your computer.
For older Ubuntu releases, substitute the version in the name of the ISO image. The instructions should otherwise be the same. See [Autoinstall quick start for s390x](https://canonical-subiquity.readthedocs-hosted.com/en/latest/howto/autoinstall-quickstart-s390x.html#autoinstall-quick-start-s390x) for instructions on installing on the s390x architecture.
## Providing the autoinstall data over the network[¶](https://canonical-subiquity.readthedocs-hosted.com/en/latest/howto/autoinstall-quickstart.html#providing-the-autoinstall-data-over-the-network "Link to this heading")
This method describes a network-based installation. When booting over a network, use this method to deliver autoinstall to perform the installation.
### Download the ISO[¶](https://canonical-subiquity.readthedocs-hosted.com/en/latest/howto/autoinstall-quickstart.html#download-the-iso "Link to this heading")
Download the latest release of the Ubuntu Server image (ISO) from the [Ubuntu ISO download page](https://releases.ubuntu.com/) (currently 23.10 (Mantic Minotaur)).
### Mount the ISO[¶](https://canonical-subiquity.readthedocs-hosted.com/en/latest/howto/autoinstall-quickstart.html#mount-the-iso "Link to this heading")
Make the content of the ISO image accessible from a local directory:
```bash
sudo mount -r ~/Downloads/ubuntu-<version-number>-live-server-amd64.iso /mnt
```
Change `<version-number>` to match the number of the release you have downloaded.
### Write your autoinstall configuration[¶](https://canonical-subiquity.readthedocs-hosted.com/en/latest/howto/autoinstall-quickstart.html#write-your-autoinstall-configuration "Link to this heading")
Create a cloud-init configuration:
```bash
mkdir -p ~/www
cd ~/www
cat > user-data << 'EOF'
#cloud-config
autoinstall:
  version: 1
  identity:
    hostname: ubuntu-server
    password: "$6$exDY1mhS4KUYCE/2$zmn9ToZwTKLhCw.b4/b.ZRTIZM30JZ4QrOQ2aOXJ8yk96xpcCof0kxKwuX1kqLG/ygbJ1f8wxED22bTL4F46P0"
    username: ubuntu
EOF
touch meta-data
```
The encrypted password is `ubuntu`.
### Serve the cloud-init configuration over HTTP[¶](https://canonical-subiquity.readthedocs-hosted.com/en/latest/howto/autoinstall-quickstart.html#serve-the-cloud-init-configuration-over-http "Link to this heading")
Leave the HTTP server running in a terminal:
```bash
cd ~/www
python3 -m http.server 3003
