# Ubuntu Cloud-Init ISO Generator Agent

This agent helps generate Ubuntu cloud-init ISOs for automated installations. It provides tools to create cloud-init configuration files and generate bootable ISOs with cloud-init data.

## Features

- Create cloud-init configuration files (user-data and meta-data)
- Generate cloud-init ISOs using cloud-localds
- Customize hostname, username, password, SSH keys, and packages
- Automated installation of required dependencies

## Prerequisites

- Python 3.8 or higher
- AutoGen library
- cloud-image-utils package (will be installed automatically if missing)

## Installation

1. Install the required Python packages:

```bash
pip install autogen-agentchat autogen-core autogen-ext
```

2. The agent will automatically install the `cloud-image-utils` package if it's not already installed.

## Usage

### Basic Usage

```python
import asyncio
from autogen_core import CancellationToken
from ubuntu_cloudinit.agent import create_agent

async def main():
    # Create the agent
    agent = await create_agent()

    # Run the agent with a task
    result = await agent.run(
        task="Create a cloud-init ISO with hostname 'my-server', username 'admin', and password 'secure123'",
        cancellation_token=CancellationToken()
    )

    print(f"Result: {result}")

if __name__ == "__main__":
    asyncio.run(main())
```

### Example Script

You can run the included example script:

```bash
python -m co_engineer.agent.ubuntu_cloudinit.example
```

## Command Line Usage

You can use the agent directly from the command line in different modes:

### Interactive Mode

Interactive mode allows you to chat with the agent and ask it to create cloud-init ISOs:

```bash
python -m co_engineer.agent.ubuntu_cloudinit.cli interactive
```

### Direct Mode

Direct mode allows you to create a cloud-init ISO directly with specific parameters:

```bash
python -m co_engineer.agent.ubuntu_cloudinit.cli direct \
  --hostname my-server \
  --username admin \
  --password secure123 \
  --ssh-keys "ssh-rsa AAAA..." \
  --packages vim,git,curl \
  --output-iso my-cloud-init.iso
```

### Example Script

You can also run the included example script:

```bash
python -m co_engineer.agent.ubuntu_cloudinit.example
```

## Cloud-Init Configuration

The agent creates a basic cloud-init configuration with the following settings:

```yaml
#cloud-config
autoinstall:
  version: 1
  identity:
    hostname: ubuntu-server
    username: ubuntu
    password: "$6$exDY1mhS4KUYCE/2$zmn9ToZwTKLhCw.b4/b.ZRTIZM30JZ4QrOQ2aOXJ8yk96xpcCof0kxKwuX1kqLG/ygbJ1f8wxED22bTL4F46P0"
```

You can customize this configuration by providing different parameters to the agent.

## References

- [Ubuntu Autoinstall Documentation](https://canonical-subiquity.readthedocs-hosted.com/en/latest/howto/autoinstall-quickstart.html)
- [Cloud-Init Documentation](https://cloudinit.readthedocs.io/en/latest/)
