#!/usr/bin/env python3
"""
Ubuntu Cloud-Init ISO Generator Agent

This agent helps generate Ubuntu cloud-init ISOs for automated installations.
It provides tools to download Ubuntu ISOs, create cloud-init configurations,
and generate bootable ISOs with cloud-init data.
"""

import asyncio
import logging
import os
import subprocess
from typing import Dict, List, Optional

from autogen_agentchat.agents import AssistantAgent
from autogen_core import Can<PERSON>ation<PERSON>oken
from co_engineer.models import model_client_qwq

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# System message for the agent
SYSTEM_MESSAGE = """
You are an Ubuntu Cloud-Init ISO Generator assistant. You help users create customized Ubuntu installation ISOs
with cloud-init for automated deployments.

You MUST ALWAYS follow these steps in this exact order when creating a cloud-init ISO:

STEP 1: Download Ubuntu ISO images
   - First check if the ISO already exists locally
   - If not, download the requested Ubuntu ISO version
   - This step is mandatory and must be completed before proceeding

STEP 2: Create cloud-init configuration files (user-data and meta-data)
   - Generate the user-data file with hostname, username, password, etc.
   - Create an empty meta-data file
   - This step depends on STEP 1 being completed

STEP 3: Generate bootable ISOs with cloud-init data
   - Use cloud-localds to create the final cloud-init ISO
   - This step depends on STEP 2 being completed

NEVER skip STEP 1. Always check for and download the Ubuntu ISO first, even if the user doesn't explicitly mention it.

When helping users, explain what you're doing at each step and provide clear instructions.
"""

# Tool functions
async def download_ubuntu_iso(version: str = "24.04.2", architecture: str = "amd64", output_path: Optional[str] = None) -> str:
    """
    Download an Ubuntu Server ISO image.

    Args:
        version: Ubuntu version (e.g., "24.04.2", "22.04")
        architecture: System architecture (e.g., "amd64", "arm64")
        output_path: Path where the ISO should be saved (default: /tmp/ubuntu-<version>/)

    Returns:
        Path to the downloaded ISO file
    """
    # If output_path is not specified, use /tmp/ubuntu-<version>/
    if output_path is None:
        # Create a version-specific directory in /tmp
        output_path = f"/tmp/ubuntu-{version}/"
        os.makedirs(output_path, exist_ok=True)
        logger.info(f"Using temporary directory at {output_path}")
    else:
        # Ensure the specified output directory exists
        os.makedirs(output_path, exist_ok=True)

    iso_filename = f"ubuntu-{version}-live-server-{architecture}.iso"
    iso_path = os.path.join(output_path, iso_filename)

    # Check if ISO already exists
    if os.path.exists(iso_path):
        logger.info(f"ISO already exists at {iso_path}")
        return iso_path

    # Determine URL based on version
    url = f"https://releases.ubuntu.com/{version}/{iso_filename}"

    logger.info(f"Downloading Ubuntu {version} ({architecture}) ISO from {url}")
    try:
        # Use wget to download the ISO
        subprocess.run(["wget", "-O", iso_path, url], check=True)
        logger.info(f"Downloaded ISO to {iso_path}")
        return iso_path
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to download ISO: {e}")
        raise

async def create_cloud_init_config(hostname: str = "ubuntu-server",
                               username: str = "ubuntu",
                               password: str = "ubuntu",
                               ssh_authorized_keys: Optional[List[str]] = None,
                               packages: Optional[List[str]] = None,
                               output_dir: Optional[str] = None,
                               ubuntu_version: str = "24.04.2") -> Dict[str, str]:
    """
    Create cloud-init configuration files (user-data and meta-data).

    Args:
        hostname: Hostname for the new system
        username: Username for the primary user
        password: Password for the primary user (will be encrypted)
        ssh_authorized_keys: List of SSH public keys to add to authorized_keys
        packages: List of packages to install
        output_dir: Directory to save the configuration files (default: /tmp/ubuntu-<version>/)
        ubuntu_version: Ubuntu version (e.g., "24.04.2", "22.04") used for default output directory

    Returns:
        Dictionary with paths to the user-data and meta-data files
    """
    if output_dir is None:
        # Use the same directory structure as in download_ubuntu_iso
        output_dir = f"/tmp/ubuntu-{ubuntu_version}/"
        os.makedirs(output_dir, exist_ok=True)
        logger.info(f"Using temporary directory at {output_dir}")
    else:
        os.makedirs(output_dir, exist_ok=True)

    # Encrypt the password if it's not already encrypted
    if not password.startswith('$'):
        try:
            # Use mkpasswd to encrypt the password
            result = subprocess.run(
                ["mkpasswd", "--method=SHA-512", password],
                capture_output=True,
                text=True,
                check=True
            )
            encrypted_password = result.stdout.strip()
        except (subprocess.CalledProcessError, FileNotFoundError):
            # Fallback to a pre-encrypted password if mkpasswd is not available
            logger.warning("mkpasswd not available, using pre-encrypted password")
            encrypted_password = "$6$exDY1mhS4KUYCE/2$zmn9ToZwTKLhCw.b4/b.ZRTIZM30JZ4QrOQ2aOXJ8yk96xpcCof0kxKwuX1kqLG/ygbJ1f8wxED22bTL4F46P0"
    else:
        encrypted_password = password

    # Create user-data file
    user_data_path = os.path.join(output_dir, "user-data")
    with open(user_data_path, "w") as f:
        f.write("#cloud-config\n")
        f.write("autoinstall:\n")
        f.write("  version: 1\n")
        f.write("  identity:\n")
        f.write(f"    hostname: {hostname}\n")
        f.write(f"    username: {username}\n")
        f.write(f"    password: \"{encrypted_password}\"\n")

        # Add SSH authorized keys if provided
        if ssh_authorized_keys:
            f.write("  ssh:\n")
            f.write("    install-server: true\n")
            f.write("    authorized-keys:\n")
            for key in ssh_authorized_keys:
                f.write(f"      - {key}\n")

        # Add packages if provided
        if packages:
            f.write("  packages:\n")
            for package in packages:
                f.write(f"    - {package}\n")

    # Create meta-data file (empty is fine for basic usage)
    meta_data_path = os.path.join(output_dir, "meta-data")
    with open(meta_data_path, "w") as f:
        pass

    logger.info(f"Created cloud-init configuration in {output_dir}")
    return {
        "user_data": user_data_path,
        "meta_data": meta_data_path,
        "config_dir": output_dir
    }

async def generate_cloud_init_iso(user_data_path: str,
                              meta_data_path: str,
                              output_iso: Optional[str] = None) -> str:
    """
    Generate a cloud-init ISO using cloud-localds.

    Args:
        user_data_path: Path to the user-data file
        meta_data_path: Path to the meta-data file
        output_iso: Path where the ISO should be saved (default: current directory/cloud-init.iso)

    Returns:
        Path to the generated ISO file
    """
    if output_iso is None:
        output_iso = os.path.join(os.getcwd(), "cloud-init.iso")

    logger.info(f"Generating cloud-init ISO at {output_iso}")
    try:
        # Check if cloud-localds is available
        subprocess.run(["which", "cloud-localds"], check=True, stdout=subprocess.PIPE)

        # Generate the ISO using cloud-localds
        subprocess.run(
            ["cloud-localds", output_iso, user_data_path, meta_data_path],
            check=True
        )
        logger.info(f"Generated cloud-init ISO at {output_iso}")
        return output_iso
    except subprocess.CalledProcessError:
        # If cloud-localds is not available, try to install it
        logger.warning("cloud-localds not found, attempting to install cloud-image-utils")
        try:
            subprocess.run(["apt-get", "update"], check=True)
            subprocess.run(["apt-get", "install", "-y", "cloud-image-utils"], check=True)

            # Try again after installation
            subprocess.run(
                ["cloud-localds", output_iso, user_data_path, meta_data_path],
                check=True
            )
            logger.info(f"Generated cloud-init ISO at {output_iso}")
            return output_iso
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to generate cloud-init ISO: {e}")
            raise

# Create the agent
async def create_agent():
    """
    Create and configure the Ubuntu Cloud-Init ISO Generator agent.

    Returns:
        Configured AssistantAgent
    """
    model_client = model_client_qwq

    # Create the agent with tools
    agent = AssistantAgent(
        name="ubuntu_cloudinit_agent",
        model_client=model_client,
        system_message=SYSTEM_MESSAGE,
        tools=[
            download_ubuntu_iso,
            create_cloud_init_config,
            generate_cloud_init_iso
        ],
        reflect_on_tool_use=True,
        model_client_stream=True,
    )

    logger.info("Ubuntu Cloud-Init ISO Generator agent initialized")
    return agent

async def main():
    """
    Main function to run the Ubuntu Cloud-Init ISO Generator agent.
    """
    agent = await create_agent()

    result = await agent.run(
        task="Create a cloud-init ISO for Ubuntu 24.04.2 with hostname 'my-server', username 'admin', and password 'secure123'. ",
        cancellation_token=CancellationToken()
    )

    print("\nAgent execution completed.")
    print(f"Result: {result}")

if __name__ == "__main__":
    asyncio.run(main())
