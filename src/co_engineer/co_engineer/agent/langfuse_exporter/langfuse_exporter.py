#!/usr/bin/env python3
"""
Langfuse Exporter Script

This script exports Langfuse trace observations (input/output) to CSV format.
It accepts a parameter to specify how many days of data to export.

Usage:
    python langfuse_exporter.py <days>
    
Example:
    python langfuse_exporter.py 7  # Export last 7 days of data
"""

import os
import sys
import csv
import json
import argparse
from datetime import datetime, timed<PERSON><PERSON>
from pathlib import Path
from typing import List, Dict, Any, Optional
from dotenv import load_dotenv
from langfuse import Langfuse


def load_env_config(env_file_path: str) -> Dict[str, str]:
    """Load Langfuse configuration from .env file."""
    if not os.path.exists(env_file_path):
        raise FileNotFoundError(f"Environment file not found: {env_file_path}")
    
    load_dotenv(env_file_path)
    
    config = {
        'secret_key': os.getenv('LANGFUSE_SECRET_KEY'),
        'public_key': os.getenv('LANGFUSE_PUBLIC_KEY'),
        'host': os.getenv('LANGFUSE_HOST')
    }
    
    # Validate required configuration
    missing_keys = [key for key, value in config.items() if not value]
    if missing_keys:
        raise ValueError(f"Missing required environment variables: {missing_keys}")
    
    return config


def initialize_langfuse_client(config: Dict[str, str]) -> Langfuse:
    """Initialize Langfuse client with configuration."""
    return Langfuse(
        secret_key=config['secret_key'],
        public_key=config['public_key'],
        host=config['host']
    )


def get_date_range(days: int) -> tuple[datetime, datetime]:
    """Get date range for the specified number of days."""
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days)
    return start_date, end_date


def format_json_field(data: Any) -> str:
    """Format JSON data for CSV export."""
    if data is None:
        return ""
    if isinstance(data, (dict, list)):
        return json.dumps(data, ensure_ascii=False, separators=(',', ':'))
    return str(data)


def extract_observation_data(observation: Any) -> Dict[str, Any]:
    """Extract relevant data from an observation."""
    return {
        'id': getattr(observation, 'id', ''),
        'trace_id': getattr(observation, 'trace_id', ''),
        'parent_observation_id': getattr(observation, 'parent_observation_id', ''),
        'type': getattr(observation, 'type', ''),
        'name': getattr(observation, 'name', ''),
        'start_time': getattr(observation, 'start_time', ''),
        'end_time': getattr(observation, 'end_time', ''),
        'model': getattr(observation, 'model', ''),
        'input': format_json_field(getattr(observation, 'input', None)),
        'output': format_json_field(getattr(observation, 'output', None)),
        'metadata': format_json_field(getattr(observation, 'metadata', None)),
        'level': getattr(observation, 'level', ''),
        'status_message': getattr(observation, 'status_message', ''),
        'version': getattr(observation, 'version', ''),
        'usage_input': getattr(observation, 'usage', {}).get('input', '') if hasattr(observation, 'usage') and observation.usage else '',
        'usage_output': getattr(observation, 'usage', {}).get('output', '') if hasattr(observation, 'usage') and observation.usage else '',
        'usage_total': getattr(observation, 'usage', {}).get('total', '') if hasattr(observation, 'usage') and observation.usage else '',
    }


def fetch_observations(client: Langfuse, start_date: datetime, end_date: datetime) -> List[Dict[str, Any]]:
    """Fetch observations from Langfuse within the specified date range."""
    observations_data = []

    try:
        print(f"Fetching observations from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}...")

        # Get traces first, then extract observations from them
        page = 1
        limit = 50  # Adjust based on your needs

        while True:
            print(f"Fetching traces page {page}...")

            try:
                # Fetch traces with date filter
                traces = client.fetch_traces(
                    page=page,
                    limit=limit,
                    from_timestamp=start_date,
                    to_timestamp=end_date
                )

                if not traces.data:
                    break

                print(f"Found {len(traces.data)} traces on page {page}")

                # Extract observations from each trace
                for trace in traces.data:
                    try:
                        # Get full trace with observations
                        full_trace = client.fetch_trace(trace.id)
                        if hasattr(full_trace, 'observations') and full_trace.observations:
                            for obs in full_trace.observations:
                                obs_data = extract_observation_data(obs)
                                observations_data.append(obs_data)
                    except Exception as trace_error:
                        print(f"Warning: Could not fetch trace {trace.id}: {trace_error}")
                        continue

                # Check if there are more pages
                if len(traces.data) < limit:
                    break

                page += 1

            except AttributeError:
                # Try alternative API method names
                try:
                    traces = client.get_traces(
                        page=page,
                        limit=limit,
                        from_timestamp=start_date,
                        to_timestamp=end_date
                    )

                    if not traces or (hasattr(traces, 'data') and not traces.data):
                        break

                    trace_list = traces.data if hasattr(traces, 'data') else traces
                    print(f"Found {len(trace_list)} traces on page {page}")

                    for trace in trace_list:
                        try:
                            full_trace = client.get_trace(trace.id)
                            if hasattr(full_trace, 'observations') and full_trace.observations:
                                for obs in full_trace.observations:
                                    obs_data = extract_observation_data(obs)
                                    observations_data.append(obs_data)
                        except Exception as trace_error:
                            print(f"Warning: Could not fetch trace {trace.id}: {trace_error}")
                            continue

                    if len(trace_list) < limit:
                        break

                    page += 1

                except Exception as api_error:
                    print(f"Error with API methods: {api_error}")
                    # Try direct observations API if available
                    try:
                        observations = client.fetch_observations(
                            page=page,
                            limit=limit,
                            from_timestamp=start_date,
                            to_timestamp=end_date
                        )

                        if not observations or (hasattr(observations, 'data') and not observations.data):
                            break

                        obs_list = observations.data if hasattr(observations, 'data') else observations

                        for obs in obs_list:
                            obs_data = extract_observation_data(obs)
                            observations_data.append(obs_data)

                        print(f"Processed {len(obs_list)} observations from page {page}")

                        if len(obs_list) < limit:
                            break

                        page += 1

                    except Exception as final_error:
                        print(f"All API methods failed: {final_error}")
                        raise

    except Exception as e:
        print(f"Error fetching observations: {e}")
        raise

    return observations_data


def export_to_csv(data: List[Dict[str, Any]], output_file: str) -> None:
    """Export observations data to CSV file."""
    if not data:
        print("No data to export.")
        return
    
    # Ensure output directory exists
    output_path = Path(output_file)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Define CSV headers
    headers = [
        'id', 'trace_id', 'parent_observation_id', 'type', 'name',
        'start_time', 'end_time', 'model', 'input', 'output',
        'metadata', 'level', 'status_message', 'version',
        'usage_input', 'usage_output', 'usage_total'
    ]
    
    try:
        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=headers)
            writer.writeheader()
            writer.writerows(data)
        
        print(f"Successfully exported {len(data)} observations to {output_file}")
        
    except Exception as e:
        print(f"Error writing CSV file: {e}")
        raise


def main():
    """Main function to run the exporter."""
    parser = argparse.ArgumentParser(
        description="Export Langfuse observations to CSV format",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python langfuse_exporter.py 7     # Export last 7 days
  python langfuse_exporter.py 30    # Export last 30 days
        """
    )
    parser.add_argument(
        'days',
        type=int,
        help='Number of days of data to export (e.g., 7 for last 7 days)'
    )
    
    args = parser.parse_args()
    
    if args.days <= 0:
        print("Error: Days must be a positive integer.")
        sys.exit(1)
    
    try:
        # Get the directory of this script
        script_dir = Path(__file__).parent
        env_file = script_dir / '.env'
        
        # Load configuration
        print("Loading Langfuse configuration...")
        config = load_env_config(str(env_file))
        
        # Initialize Langfuse client
        print("Initializing Langfuse client...")
        client = initialize_langfuse_client(config)
        
        # Get date range
        start_date, end_date = get_date_range(args.days)
        
        # Fetch observations
        observations = fetch_observations(client, start_date, end_date)
        
        # Generate output filename
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = script_dir / 'llm_data' / f'langfuse_observations_{args.days}days_{timestamp}.csv'
        
        # Export to CSV
        export_to_csv(observations, str(output_file))
        
        print(f"\nExport completed successfully!")
        print(f"File saved to: {output_file}")
        print(f"Total observations exported: {len(observations)}")
        
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
