# Langfuse Exporter

This tool exports Langfuse trace observations (input/output data) to CSV format for analysis and backup purposes.

## Features

- Export observations from specified number of days
- Extracts input, output, metadata, and usage information
- Saves data in CSV format with proper JSON formatting
- Handles pagination for large datasets
- Configurable via environment variables

## Setup

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Configure your Langfuse credentials in the `.env` file:
```
LANGFUSE_SECRET_KEY="your-secret-key"
LANGFUSE_PUBLIC_KEY="your-public-key"
LANGFUSE_HOST="your-langfuse-host"
```

## Usage

Run the exporter with the number of days you want to export:

```bash
python langfuse_exporter.py <days>
```

### Examples

```bash
# Export last 7 days of data
python langfuse_exporter.py 7

# Export last 30 days of data
python langfuse_exporter.py 30

# Export last 1 day of data
python langfuse_exporter.py 1
```

## Output

The script will create a CSV file in the `./llm_data` directory with the following naming pattern:
```
langfuse_observations_{days}days_{timestamp}.csv
```

For example: `langfuse_observations_7days_20241207_143022.csv`

## CSV Structure

The exported CSV contains the following columns:

- `id`: Observation ID
- `trace_id`: Associated trace ID
- `parent_observation_id`: Parent observation ID (if applicable)
- `type`: Observation type (e.g., "generation", "span")
- `name`: Observation name
- `start_time`: Start timestamp
- `end_time`: End timestamp
- `model`: Model used (if applicable)
- `input`: Input data (JSON formatted)
- `output`: Output data (JSON formatted)
- `metadata`: Additional metadata (JSON formatted)
- `level`: Log level
- `status_message`: Status message
- `version`: Version information
- `usage_input`: Input token usage
- `usage_output`: Output token usage
- `usage_total`: Total token usage

## Error Handling

The script includes comprehensive error handling for:
- Missing environment variables
- Network connectivity issues
- API authentication problems
- File system permissions
- Invalid date ranges

## Requirements

- Python 3.7+
- langfuse>=2.0.0
- python-dotenv>=1.0.0

## Notes

- The script uses pagination to handle large datasets efficiently
- JSON data is properly formatted for CSV compatibility
- Output directory (`./llm_data`) is created automatically if it doesn't exist
- All timestamps are handled in the local timezone
