#!/bin/bash

# Langfuse Exporter Runner Script
# This script helps you run the Langfuse exporter with proper setup

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}Langfuse Exporter Runner${NC}"
echo "=========================="

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}Error: Python 3 is not installed or not in PATH${NC}"
    exit 1
fi

# Check if pip is available
if ! command -v pip3 &> /dev/null; then
    echo -e "${RED}Error: pip3 is not installed or not in PATH${NC}"
    exit 1
fi

# Install dependencies if requirements.txt exists
if [ -f "requirements.txt" ]; then
    echo -e "${YELLOW}Installing dependencies...${NC}"
    pip3 install -r requirements.txt
    echo -e "${GREEN}Dependencies installed successfully${NC}"
else
    echo -e "${YELLOW}No requirements.txt found, skipping dependency installation${NC}"
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo -e "${RED}Error: .env file not found${NC}"
    echo "Please create a .env file with your Langfuse credentials:"
    echo "LANGFUSE_SECRET_KEY=\"your-secret-key\""
    echo "LANGFUSE_PUBLIC_KEY=\"your-public-key\""
    echo "LANGFUSE_HOST=\"your-langfuse-host\""
    exit 1
fi

# Run test if requested
if [ "$1" = "test" ]; then
    echo -e "${YELLOW}Running tests...${NC}"
    python3 test_exporter.py
    exit 0
fi

# Check if days parameter is provided
if [ $# -eq 0 ]; then
    echo -e "${YELLOW}Usage: $0 <days> [or 'test' to run tests]${NC}"
    echo "Examples:"
    echo "  $0 7     # Export last 7 days"
    echo "  $0 30    # Export last 30 days"
    echo "  $0 test  # Run tests"
    exit 1
fi

# Validate days parameter
if ! [[ "$1" =~ ^[0-9]+$ ]] || [ "$1" -le 0 ]; then
    echo -e "${RED}Error: Days must be a positive integer${NC}"
    exit 1
fi

# Run the exporter
echo -e "${YELLOW}Running Langfuse exporter for last $1 days...${NC}"
python3 langfuse_exporter.py "$1"

echo -e "${GREEN}Export completed!${NC}"
