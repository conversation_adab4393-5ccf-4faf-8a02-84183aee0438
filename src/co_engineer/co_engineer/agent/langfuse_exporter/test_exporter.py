#!/usr/bin/env python3
"""
Test script for Langfuse Exporter

This script tests the basic functionality of the langfuse_exporter.py
"""

import os
import sys
from pathlib import Path
from langfuse_exporter import load_env_config, initialize_langfuse_client, get_date_range


def test_env_loading():
    """Test loading environment configuration."""
    print("Testing environment configuration loading...")
    
    try:
        script_dir = Path(__file__).parent
        env_file = script_dir / '.env'
        config = load_env_config(str(env_file))
        
        print("✓ Environment configuration loaded successfully")
        print(f"  - Host: {config['host']}")
        print(f"  - Public Key: {config['public_key'][:20]}...")
        print(f"  - Secret Key: {config['secret_key'][:20]}...")
        
        return config
        
    except Exception as e:
        print(f"✗ Failed to load environment configuration: {e}")
        return None


def test_langfuse_connection(config):
    """Test Langfuse client initialization and connection."""
    print("\nTesting Langfuse connection...")
    
    try:
        client = initialize_langfuse_client(config)
        print("✓ Langfuse client initialized successfully")
        
        # Try to make a simple API call to test connection
        # Note: This might fail if there are no traces, but it will test connectivity
        try:
            traces = client.get_traces(limit=1)
            print("✓ Successfully connected to Langfuse API")
            return client
        except Exception as api_error:
            print(f"⚠ Client initialized but API call failed: {api_error}")
            print("  This might be normal if there are no traces in your Langfuse instance")
            return client
            
    except Exception as e:
        print(f"✗ Failed to initialize Langfuse client: {e}")
        return None


def test_date_range():
    """Test date range calculation."""
    print("\nTesting date range calculation...")
    
    try:
        start_date, end_date = get_date_range(7)
        print(f"✓ Date range calculated successfully")
        print(f"  - Start: {start_date.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"  - End: {end_date.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"  - Duration: 7 days")
        
    except Exception as e:
        print(f"✗ Failed to calculate date range: {e}")


def test_output_directory():
    """Test output directory creation."""
    print("\nTesting output directory...")
    
    try:
        script_dir = Path(__file__).parent
        output_dir = script_dir / 'llm_data'
        
        if output_dir.exists():
            print("✓ Output directory exists")
        else:
            output_dir.mkdir(parents=True, exist_ok=True)
            print("✓ Output directory created")
            
        print(f"  - Path: {output_dir}")
        
    except Exception as e:
        print(f"✗ Failed to create output directory: {e}")


def main():
    """Run all tests."""
    print("Langfuse Exporter Test Suite")
    print("=" * 40)
    
    # Test environment loading
    config = test_env_loading()
    if not config:
        print("\n✗ Cannot proceed without valid configuration")
        sys.exit(1)
    
    # Test Langfuse connection
    client = test_langfuse_connection(config)
    
    # Test date range calculation
    test_date_range()
    
    # Test output directory
    test_output_directory()
    
    print("\n" + "=" * 40)
    if client:
        print("✓ All basic tests passed! The exporter should work correctly.")
        print("\nTo run the actual export, use:")
        print("python langfuse_exporter.py <days>")
    else:
        print("⚠ Some tests failed. Please check your configuration.")


if __name__ == "__main__":
    main()
