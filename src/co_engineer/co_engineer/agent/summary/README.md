# Web Content Summarizer with A2A Protocol

This module implements a web content summarization agent exposed through the A2A (Agent-to-Agent) protocol.

## How It Works

This agent utilizes AutoGen and the MCP fetch tool to retrieve and summarize web content. The A2A protocol enables standardized interaction with the agent, allowing clients to send URLs and receive concise summaries as text artifacts.

```mermaid
sequenceDiagram
    participant Client as A2A Client
    participant Server as A2A Server
    participant Agent as Summary Agent
    participant Fetch as MCP Fetch Tool
    participant Web as Web Content

    Client->>Server: Send task with URL
    Server->>Agent: Forward URL to summary agent
    Agent->>Fetch: Request content from URL
    Fetch->>Web: HTTP request
    Web->>Fetch: Return HTML/text content
    Fetch->>Agent: Return fetched content
    Agent->>Agent: Generate concise summary
    Agent->>Server: Store summary and return ID
    Server->>Client: Respond with text artifact
```

## Key Components

- **AutoGen Agent**: Specialized agent for web content summarization
- **A2A Server**: Provides standardized protocol for interacting with the agent
- **MCP Fetch Tool**: Retrieves content from web URLs
- **Task Manager**: Handles A2A protocol requests and manages task state

## Prerequisites

- Python 3.8 or higher
- AutoGen library
- MCP server fetch tool
- Required dependencies (see below)

## Setup & Running

1. Navigate to the agent directory:

    ```bash
    cd x/src/co_engineer
    ```

2. Set up the Python environment:

    ```bash
    uv python pin 3.12
    uv venv
    source .venv/bin/activate
    ```

2. Run the agent with desired options:

    ```bash
    # Basic run
    uv run co_engineer/agent/summary/summary_a2a.py

    # On custom host/port
    uv run co_engineer/agent/summary/summary_a2a.py --host 0.0.0.0 --port 10000
    ```

3. In a separate terminal, run the A2A client:

    ```bash
    cd x/src/co_engineer
    source .venv/bin/activate
    uv run co_engineer/agent/a2a/hosts/cli --agent [url-of-your-a2a-server]
    ```
    for example --agent http://localhost:10000.

    Submit a net task:

    ```bash
    What do you want to send to the agent? (:q or quit to exit)
    Summary the web content of: https://en.wikipedia.org/wiki/PowerEdge
    ```
    The agent will respond with a summary of the web content.
    ![image](./assets/image.png)


## Features & Capabilities

**Features:**

- Web content summarization from any valid URL
- Concise, well-structured summaries of main points
- A2A protocol compliance for standardized agent interaction
- Support for text input/output modalities
- Robust error handling

**Limitations:**

- No streaming support (returns complete summaries only)
- No push notification support
- Text-only input and output (no multimedia)

## Implementation Details

The implementation consists of two main classes:

1. **SummaryAgent**: Core agent that fetches and summarizes web content
   - Uses AutoGen's AssistantAgent with specialized system message
   - Integrates with MCP fetch tool for content retrieval
   - Processes and summarizes content into concise text

2. **AgentTaskManager**: Handles A2A protocol interactions
   - Validates input/output modality compatibility
   - Manages task state and artifacts
   - Invokes the summary agent with the provided URL

The agent is exposed through an A2A server that handles JSON-RPC requests according to the A2A protocol specification.

## Learn More

- [A2A Protocol Documentation](https://google.github.io/A2A/#/documentation)
- [AutoGen Documentation](https://microsoft.github.io/autogen/)
- [MCP Fetch Tool Documentation](https://github.com/microsoft/mcp-server-fetch)