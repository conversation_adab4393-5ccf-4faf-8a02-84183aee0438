#!/usr/bin/env python3
"""
Tests for the summary agent.
"""

import asyncio
import unittest
from unittest.mock import AsyncMock, patch

from autogen_agentchat.messages import TextMessage
from autogen_agentchat.base._task import TaskResult

from summary_agent import create_summary_agent, summarize_url


class TestSummaryAgent(unittest.TestCase):
    """Test cases for the summary agent."""

    @patch("summary_agent.create_summary_agent")
    async def test_summarize_url(self, mock_create_agent):
        """Test that summarize_url correctly extracts the summary from the agent's response."""
        # Create a mock agent
        mock_agent = AsyncMock()
        mock_create_agent.return_value = mock_agent

        # Create a mock run result with a text message
        mock_message = TextMessage(source="summarizer", content="This is a test summary.")
        mock_result = TaskResult(messages=[mock_message])
        mock_agent.run.return_value = mock_result

        # Call the function
        url = "https://example.com"
        result = await summarize_url(url)

        # Verify the result
        self.assertEqual(result, "This is a test summary.")
        mock_agent.run.assert_called_once()

    @patch("summary_agent.create_summary_agent")
    async def test_summarize_url_failure(self, mock_create_agent):
        """Test that summarize_url handles failures gracefully."""
        # Create a mock agent
        mock_agent = AsyncMock()
        mock_create_agent.return_value = mock_agent

        # Create a mock run result with no messages
        mock_result = TaskResult(messages=[])
        mock_agent.run.return_value = mock_result

        # Call the function
        url = "https://example.com"
        result = await summarize_url(url)

        # Verify the result
        self.assertEqual(result, "Failed to generate summary.")
        mock_agent.run.assert_called_once()


async def run_tests():
    """Run the tests."""
    # We need to use a different approach for async tests
    test_cases = [
        TestSummaryAgent("test_summarize_url"),
        TestSummaryAgent("test_summarize_url_failure")
    ]

    for test_case in test_cases:
        await getattr(test_case, test_case._testMethodName)()

    print("All tests passed!")


if __name__ == "__main__":
    # Run the tests using asyncio
    asyncio.run(run_tests())
