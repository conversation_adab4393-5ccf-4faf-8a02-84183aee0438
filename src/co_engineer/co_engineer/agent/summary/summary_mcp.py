#!/usr/bin/env python3
"""
Summary Agent - A tool that fetches web content and summarizes it using Autogen.

This script creates an agent that uses the MCP fetch tool to retrieve content from
a URL and then summarizes it.
"""

import asyncio
import logging
import warnings

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.messages import TextMessage
from co_engineer.models import model_client_qwq
from autogen_ext.tools.mcp import StdioServerParams, mcp_server_tools
from autogen_core import CancellationToken

# Configure logging to only show errors
logging.basicConfig(level=logging.ERROR)

# Suppress UserWarning messages
warnings.filterwarnings("ignore", category=UserWarning)

async def create_summary_agent() -> AssistantAgent:
    """
    Create an agent that can fetch and summarize web content.
    
    Args:
        model_name: The name of the OpenAI model to use
        
    Returns:
        An AssistantAgent configured with the fetch tool
    """
    # Get the fetch tool from mcp-server-fetch
    fetch_mcp_server = StdioServerParams(command="uvx", args=["mcp-server-fetch"])
    tools = await mcp_server_tools(fetch_mcp_server)
    
    # Create an agent that can use the fetch tool
    model_client = model_client_qwq
    agent = AssistantAgent(
        name="summarizer",
        model_client=model_client,
        tools=tools,  # type: ignore
        system_message=(
            "You are a helpful assistant that summarizes web content. "
            "When given a URL, you will fetch its content and provide a concise, "
            "well-structured summary of the main points. "
            "Your summaries should be informative, accurate, and easy to understand."
        ),
        reflect_on_tool_use=True
    )
    
    return agent


async def summarize_url(url: str) -> str:
    """
    Fetch and summarize the content of a URL.
    
    Args:
        url: The URL to fetch and summarize
        model_name: The name of the OpenAI model to use
        
    Returns:
        A string containing the summary of the URL content
    """
    agent = await create_summary_agent()
    
    # Run the agent to fetch and summarize the URL
    result = await agent.run(
        task=f"Fetch and provide a comprehensive summary of the content at {url}",
        cancellation_token=CancellationToken()
    )
    
    # Extract the summary from the last message
    if result and result.messages:
        last_message = result.messages[-1]
        if isinstance(last_message, TextMessage):
            return last_message.content
    
    return "Failed to generate summary."


async def main():
    """Main function to demonstrate the summary agent."""
    # Example URL to summarize
    url = "https://en.wikipedia.org/wiki/PowerEdge"
    
    print(f"Summarizing content from: {url}")
    summary = await summarize_url(url)
    print("\nSummary:")
    print(summary)



if __name__ == "__main__":
    asyncio.run(main())
