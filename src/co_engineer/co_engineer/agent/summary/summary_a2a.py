#!/usr/bin/env python3
"""
Summary Agent - A tool that fetches web content and summarizes it using Autogen.

This script creates an agent that uses the MCP fetch tool to retrieve content from
a URL and then summarizes it.
"""

from typing import Union
import click
import logging

from typing import Any, AsyncIterable, Dict
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.messages import TextMessage
from co_engineer.models import model_client_qwq
from autogen_ext.tools.mcp import StdioServerParams, mcp_server_tools
from autogen_core import CancellationToken

from co_engineer.agent.a2a.common.server import A2AServer
from co_engineer.agent.a2a.common.types import Agent<PERSON>ard, AgentCapabilities, AgentSkill
from co_engineer.agent.a2a.common.server.task_manager import InMemoryTaskManager
from co_engineer.agent.a2a.common.types import (
    SendTaskRequest,
    TaskSendParams,
    Message,
    TaskStatus,
    Artifact,
    TextPart,
    TaskState,
    Task,
    SendTaskResponse,
    JSONRPCResponse,
    SendTaskStreamingRequest,
    SendTaskStreamingResponse,
)
import co_engineer.agent.a2a.common.server.utils as utils


# Configure logging to only show errors
logging.basicConfig(level=logging.ERROR)
logger = logging.getLogger(__name__)

class SummaryAgent:
    """
    Summary Agent - A tool that fetches web content and summarizes it using Autogen.

    This class creates an agent that uses the MCP fetch tool to retrieve content from
    a URL and then summarizes it.
    """
    SUPPORTED_CONTENT_TYPES = ["text", "text/plain"]
    
    def __init__(self):
        self.agent = self._build_agent()

    def _build_agent(self) -> AssistantAgent:
        """
        Create an agent that can fetch and summarize web content.
        
        Args:
            model_name: The name of the OpenAI model to use
            
        Returns:
            An AssistantAgent configured with the fetch tool
        """
        
        # Create an agent that can use the fetch tool
        model_client = model_client_qwq
        agent = AssistantAgent(
            name="summarizer",
            model_client=model_client,
            system_message=(
                "You are a helpful assistant that summarizes web content. "
                "When given a URL, you will fetch its content and provide a concise, "
                "well-structured summary of the main points. "
                "Your summaries should be informative, accurate, and easy to understand."
            ),
            reflect_on_tool_use=True
        )
        
        return agent

    
    async def invoke(self, url) -> str:
        """
        Fetch and summarize the content of a URL.
        
        Args:
            url: The URL to fetch and summarize
            
        Returns:
            A string containing the summary of the URL content
        """
        
        fetch_mcp_server = StdioServerParams(command="uvx", args=["mcp-server-fetch"])
        tools = await mcp_server_tools(fetch_mcp_server)
        self.agent.tools = tools
        
        # Run the agent to fetch and summarize the URL
        result = await self.agent.run(
            task=f"Fetch and provide a comprehensive summary of the content at {url}",
            cancellation_token=CancellationToken()
        )
        
        # Extract the summary from the last message
        if result and result.messages:
            last_message = result.messages[-1]
            if isinstance(last_message, TextMessage):
                return last_message.content
        
        return "Failed to generate summary."
    
    async def stream(self, query: str) -> AsyncIterable[Dict[str, Any]]:
        """Streaming is not supported by AutoGen."""
        raise NotImplementedError("Streaming is not supported by AutoGen.")


class AgentTaskManager(InMemoryTaskManager):

    def __init__(self):
        super().__init__()
    
    async def _stream_generator(
        self, request: SendTaskRequest
    ) -> AsyncIterable[SendTaskResponse]:
        raise NotImplementedError("Not implemented")
    
    def _validate_request(
        self, request: Union[SendTaskRequest]
    ) -> None:
        task_send_params: TaskSendParams = request.params
        if not utils.are_modalities_compatible(
            task_send_params.acceptedOutputModes, SummaryAgent.SUPPORTED_CONTENT_TYPES
        ):
            logger.warning(
                "Unsupported output mode. Received %s, Support %s",
                task_send_params.acceptedOutputModes,
                SummaryAgent.SUPPORTED_CONTENT_TYPES,
            )
            return utils.new_incompatible_types_error(request.id)
        
    async def on_send_task(
        self, request: SendTaskRequest
    ) -> SendTaskResponse | AsyncIterable[SendTaskResponse]:
        ## only support text output at the moment
        if not utils.are_modalities_compatible(
            request.params.acceptedOutputModes,
            SummaryAgent.SUPPORTED_CONTENT_TYPES,
        ):
            logger.warning(
                "Unsupported output mode. Received %s, Support %s",
                request.params.acceptedOutputModes,
                SummaryAgent.SUPPORTED_CONTENT_TYPES,
            )
            return utils.new_incompatible_types_error(request.id)

        task_send_params: TaskSendParams = request.params
        await self.upsert_task(task_send_params)

        return await self._invoke(request)
    
    async def on_send_task_subscribe(
        self, request: SendTaskStreamingRequest
    ) -> AsyncIterable[SendTaskStreamingResponse] | JSONRPCResponse:
        error = self._validate_request(request)
        if error:
            return error
        await self.upsert_task(request.params)
        return self._stream_generator(request)
    
    async def _update_store(
        self, task_id: str, status: TaskStatus, artifacts: list[Artifact]
    ) -> Task:
        async with self.lock:
            try:
                task = self.tasks[task_id]
            except KeyError:
                logger.error(f"Task {task_id} not found for updating the task")
                raise ValueError(f"Task {task_id} not found")
            task.status = status
            #if status.message is not None:
            #    self.task_messages[task_id].append(status.message)
            if artifacts is not None:
                if task.artifacts is None:
                    task.artifacts = []
                task.artifacts.extend(artifacts)
            return task
        
    async def _invoke(self, request: SendTaskRequest) -> SendTaskResponse:
        task_send_params: TaskSendParams = request.params
        query = self._get_user_query(task_send_params)
        try:
            agent = SummaryAgent()
            result = await agent.invoke(query)
        except Exception as e:
            logger.error(f"Error invoking agent: {e}")
            raise ValueError(f"Error invoking agent: {e}")
        parts = [{"type": "text", "text": result}]
        task_state = TaskState.INPUT_REQUIRED if "MISSING_INFO:" in result else TaskState.COMPLETED
        task = await self._update_store(
            task_send_params.id,
            TaskStatus(
                state=task_state, message=Message(role="agent", parts=parts)
            ),
            [Artifact(parts=parts)],
        )
        return SendTaskResponse(id=request.id, result=task)
    
    def _get_user_query(self, task_send_params: TaskSendParams) -> str:
        part = task_send_params.message.parts[0]
        if not isinstance(part, TextPart):
            raise ValueError("Only text parts are supported")
        return part.text


@click.command()
@click.option("--host", "host", default="localhost")
@click.option("--port", "port", default=10000)
def main(host, port):
        
    """Starts the Summary Agent server."""
    try:
        capabilities = AgentCapabilities(streaming=False, pushNotifications=False)
        skill = AgentSkill(
            id="Summary URL",
            name="Web Content Summarizer",
            description="Helps with summarizing web content",
            tags=["Web Content Summary"],
            examples=["https://en.wikipedia.org/wiki/PowerEdge"],
        )
        agent_card = AgentCard(
            name="Web content summarizer agent",
            description="Helps with summarizing web content",
            url=f"http://{host}:{port}/",
            version="1.0.0",
            defaultInputModes=SummaryAgent.SUPPORTED_CONTENT_TYPES,
            defaultOutputModes=SummaryAgent.SUPPORTED_CONTENT_TYPES,
            capabilities=capabilities,
            skills=[skill],
        )
        
        server = A2AServer(
            agent_card=agent_card,
            task_manager=AgentTaskManager(),
            host=host,
            port=port,
        )

        logger.info(f"Starting server on {host}:{port}")
        server.start()
    except Exception as e:
        logger.error(f"An error occurred during server startup: {e}")
        exit(1)

if __name__ == "__main__":
    main()
