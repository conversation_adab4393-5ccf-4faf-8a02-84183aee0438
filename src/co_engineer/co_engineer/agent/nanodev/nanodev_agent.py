import importlib
import yaml
from collections.abc import Callable
from smolagents import (
    ToolCallingAgent,
    Tool,
    ChatMessage,
    PromptTemplates
)

class NanodevAgent(ToolCallingAgent):
    def __init__(
        self,
        tools: list[Tool],
        model: Callable[[list[dict[str, str]]], ChatMessage],
        prompt_templates: PromptTemplates | None = None,
        planning_interval: int | None = None,
        **kwargs,
    ):
        prompt_templates = prompt_templates or yaml.safe_load(
            importlib.resources.files("co_engineer..prompts").joinpath("toolcalling_agent.yaml").read_text()
        )
        super().__init__(
            tools=tools,
            model=model,
            prompt_templates=prompt_templates,
            planning_interval=planning_interval,
            **kwargs,
        )