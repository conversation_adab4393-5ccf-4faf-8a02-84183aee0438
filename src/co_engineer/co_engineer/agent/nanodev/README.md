# Nanodev

## MCP Server
0. Config env file:
```bash
cp env_example.env .env
```

1. Install mcp-filesystem-server package: 
```bash
go install github.com/mark3labs/mcp-filesystem-server@latest
echo 'export PATH="$HOME/go/bin:$PATH"' | sudo tee -a /etc/profile
source /etc/profile
```

2. Update ENV file .env like GITHUB_PERSONAL_ACCESS_TOKEN

3. Create working directory:
```bash
mkdir -p /home/<USER>/sandbox
```