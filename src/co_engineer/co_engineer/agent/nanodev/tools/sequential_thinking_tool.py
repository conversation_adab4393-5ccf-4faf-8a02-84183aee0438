"""Sequential Thinking Tool for NanoDev.

This tool helps the NanoDev agent break down complex problems, analyze issues step-by-step, 
and ensure a thorough approach to problem-solving.
"""

import json
import logging
from typing import Any, Dict, List, Optional, TypedDict

# Configure logging
logger = logging.getLogger(__name__)


class ThoughtData(TypedDict, total=False):
    """Type definition for thought data."""
    thought: str
    thoughtNumber: int
    totalThoughts: int
    isRevision: Optional[bool]
    revisesThought: Optional[int]
    branchFromThought: Optional[int]
    branchId: Optional[str]
    needsMoreThoughts: Optional[bool]
    nextThoughtNeeded: bool


class SequentialThinkingTool:
    """A tool for sequential thinking that helps break down complex problems.

    This tool helps analyze problems through a flexible thinking process that can adapt and evolve.
    Each thought can build on, question, or revise previous insights as understanding deepens.
    """

    def __init__(self, verbose: bool = False):
        """Initialize the sequential thinking tool."""
        self.thought_history: List[ThoughtData] = []
        self.branches: Dict[str, List[ThoughtData]] = {}
        self.verbose = verbose

    def _format_thought(
        self,
        thought: str,
        thought_number: int,
        total_thoughts: int,
        is_revision: bool = False,
        revises_thought: Optional[int] = None,
        branch_from_thought: Optional[int] = None,
        branch_id: Optional[str] = None,
    ) -> str:
        """Format a thought for display.

        Args:
            thought: The thought content
            thought_number: The thought number
            total_thoughts: The total number of thoughts
            is_revision: Whether this thought revises a previous one
            revises_thought: Which thought is being revised
            branch_from_thought: Which thought this branches from
            branch_id: Branch identifier

        Returns:
            Formatted thought string
        """
        if is_revision:
            prefix = "🔄 Revision"
            context = f" (revising thought {revises_thought})"
        elif branch_from_thought:
            prefix = "🌿 Branch"
            context = f" (from thought {branch_from_thought}, ID: {branch_id})"
        else:
            prefix = "💭 Thought"
            context = ""

        header = f"{prefix} {thought_number}/{total_thoughts}{context}"
        border_length = max(len(header), len(thought)) + 4
        border = "─" * border_length

        return f"""
┌{border}┐
│ {header.ljust(border_length)} │
├{border}┤
│ {thought.ljust(border_length)} │
└{border}┘"""

    def __call__(self, **kwargs):
        """Run the sequential thinking tool.

        Args:
            kwargs: The input data for the tool

        Returns:
            Tool output
        """
        try:
            # Extract and validate input
            validated_input = self._validate_input(kwargs)
            
            # Store the thought in history
            self._store_thought(validated_input)
            
            # Format and display the thought
            formatted_thought = self._format_thought(
                thought=validated_input["thought"],
                thought_number=validated_input["thoughtNumber"],
                total_thoughts=validated_input["totalThoughts"],
                is_revision=validated_input.get("isRevision", False),
                revises_thought=validated_input.get("revisesThought"),
                branch_from_thought=validated_input.get("branchFromThought"),
                branch_id=validated_input.get("branchId"),
            )
            
            if self.verbose:
                print(formatted_thought)
            
            # Prepare response
            response = {
                "thoughtNumber": validated_input["thoughtNumber"],
                "totalThoughts": validated_input["totalThoughts"],
                "nextThoughtNeeded": validated_input["nextThoughtNeeded"],
                "branches": list(self.branches.keys()),
                "thoughtHistoryLength": len(self.thought_history),
            }
            
            return json.dumps(response, indent=2)
        except Exception as e:
            error_response = {"error": str(e), "status": "failed"}
            return json.dumps(error_response, indent=2)

    def _validate_input(self, input_data: Dict[str, Any]) -> ThoughtData:
        """Validate the input data.

        Args:
            input_data: The input data to validate

        Returns:
            Validated input data
        """
        # Check required fields
        required_fields = ["thought", "thoughtNumber", "totalThoughts", "nextThoughtNeeded"]
        for field in required_fields:
            if field not in input_data:
                raise ValueError(f"Missing required field: {field}")
        
        # Convert to ThoughtData
        thought_data: ThoughtData = {
            "thought": input_data["thought"],
            "thoughtNumber": input_data["thoughtNumber"],
            "totalThoughts": input_data["totalThoughts"],
            "nextThoughtNeeded": input_data["nextThoughtNeeded"],
        }
        
        # Add optional fields if present
        optional_fields = ["isRevision", "revisesThought", "branchFromThought", "branchId", "needsMoreThoughts"]
        for field in optional_fields:
            if field in input_data:
                thought_data[field] = input_data[field]  # type: ignore
        
        return thought_data

    def _store_thought(self, thought_data: ThoughtData) -> None:
        """Store a thought in the history.

        Args:
            thought_data: The thought data to store
        """
        # Handle branch storage if applicable
        branch_id = thought_data.get("branchId")
        if branch_id:
            if branch_id not in self.branches:
                self.branches[branch_id] = []
            self.branches[branch_id].append(thought_data)
        
        # Store in main history
        self.thought_history.append(thought_data)
