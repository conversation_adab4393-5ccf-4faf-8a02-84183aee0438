"""Bash Tool for NanoDev.

This tool allows the NanoDev agent to execute bash commands.
"""

import subprocess
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional

# Configure logging
logger = logging.getLogger(__name__)

class BashTool:
    """A tool for executing bash commands."""

    def __init__(
        self,
        workspace_root: Optional[Path] = None,
        timeout: int = 60,
        banned_commands: Optional[List[str]] = None,
    ):
        """Initialize the BashTool.

        Args:
            workspace_root: Root directory of the workspace
            timeout: Command execution timeout in seconds
            banned_commands: List of banned command strings
        """
        self.workspace_root = workspace_root
        self.timeout = timeout
        self.banned_commands = banned_commands or [
            "git init",
            "git commit",
            "git add",
            "rm -rf /",
            "sudo rm",
        ]

    def _is_command_allowed(self, command: str) -> bool:
        """Check if a command is allowed to execute.

        Args:
            command: The command to check

        Returns:
            True if the command is allowed, False otherwise
        """
        for banned in self.banned_commands:
            if banned in command:
                return False
        return True

    def __call__(self, **kwargs):
        """Execute a bash command and return its output.

        Args:
            kwargs: Dictionary containing the command to execute

        Returns:
            Command output
        """
        command = kwargs.get("command", "")
        if not command:
            return "Error: No command provided"

        # Check if command is allowed
        if not self._is_command_allowed(command):
            return f"Error: Command '{command}' contains banned operations"

        try:
            # Execute the command
            process = subprocess.run(
                command,
                shell=True,
                capture_output=True,
                text=True,
                timeout=self.timeout,
                cwd=self.workspace_root,
            )
            
            # Prepare the output
            output = ""
            if process.stdout:
                output += process.stdout
            if process.stderr:
                if output:
                    output += "\n"
                output += f"STDERR: {process.stderr}"
            
            # Add return code if non-zero
            if process.returncode != 0:
                output += f"\nCommand exited with return code {process.returncode}"
            
            return output or "Command executed successfully (no output)"
        
        except subprocess.TimeoutExpired:
            return f"Error: Command timed out after {self.timeout} seconds"
        except Exception as e:
            return f"Error executing command: {str(e)}"


def create_bash_tool(
    workspace_root: Optional[Path] = None,
    timeout: int = 60,
    banned_commands: Optional[List[str]] = None,
) -> BashTool:
    """Create a bash tool for executing bash commands.

    Args:
        workspace_root: Root directory of the workspace
        timeout: Command execution timeout in seconds
        banned_commands: List of banned command strings

    Returns:
        BashTool instance
    """
    return BashTool(
        workspace_root=workspace_root,
        timeout=timeout,
        banned_commands=banned_commands,
    )
