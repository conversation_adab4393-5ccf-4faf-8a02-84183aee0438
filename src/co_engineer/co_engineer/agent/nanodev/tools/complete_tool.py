"""Complete Tool for NanoDev.

This tool allows the NanoDev agent to indicate task completion.
"""

class CompleteTool:
    """A tool for indicating task completion."""

    def __init__(self):
        """Initialize the CompleteTool."""
        self.answer = ""
        self.should_stop = False

    def reset(self):
        """Reset the tool state."""
        self.answer = ""
        self.should_stop = False

    def __call__(self, **kwargs):
        """Execute the complete tool.

        Args:
            kwargs: Tool parameters

        Returns:
            Tool output
        """
        answer = kwargs.get("answer", "")
        if not answer:
            return "Error: No answer provided"

        self.answer = answer
        self.should_stop = True
        return "Task completed"


def create_complete_tool() -> CompleteTool:
    """Create a complete tool.

    Returns:
        CompleteTool instance
    """
    return CompleteTool()
