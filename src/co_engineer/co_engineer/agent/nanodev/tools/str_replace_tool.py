"""String Replace Tool for NanoDev.

This tool allows the NanoDev agent to view and edit files.
"""

import os
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple, Union

# Configure logging
import logging
logger = logging.getLogger(__name__)

class StrReplaceEditorTool:
    """A tool for viewing and editing files."""

    def __init__(
        self,
        workspace_root: Optional[Path] = None,
        max_file_size: int = 1024 * 1024,  # 1MB
    ):
        """Initialize the StrReplaceEditorTool.

        Args:
            workspace_root: Root directory of the workspace
            max_file_size: Maximum file size to read/write
        """
        self.workspace_root = workspace_root
        self.max_file_size = max_file_size
        self._file_history: Dict[Path, List[str]] = {}

    def _resolve_path(self, path: str) -> Path:
        """Resolve a path relative to the workspace root.

        Args:
            path: The path to resolve

        Returns:
            Resolved path
        """
        if self.workspace_root:
            return self.workspace_root / path
        return Path(path)

    def _validate_path(self, path: Path) -> None:
        """Validate that a path is safe to access.

        Args:
            path: The path to validate

        Raises:
            ValueError: If the path is not safe
        """
        if not path.exists() and not path.parent.exists():
            raise ValueError(f"Path does not exist and parent directory doesn't exist: {path}")

    def _read_file(self, path: Path) -> str:
        """Read a file and return its contents.

        Args:
            path: The path to read

        Returns:
            File contents

        Raises:
            ValueError: If the file is too large
        """
        if path.exists():
            if path.stat().st_size > self.max_file_size:
                raise ValueError(f"File too large: {path}")
            return path.read_text()
        return ""

    def _write_file(self, path: Path, content: str) -> None:
        """Write content to a file.

        Args:
            path: The path to write to
            content: The content to write
        """
        # Ensure the directory exists
        path.parent.mkdir(parents=True, exist_ok=True)
        
        # Save the old content for history if the file exists
        if path.exists():
            old_content = self._read_file(path)
            if path not in self._file_history:
                self._file_history[path] = []
            self._file_history[path].append(old_content)
        
        # Write the new content
        path.write_text(content)

    def __call__(self, **kwargs):
        """Execute the str_replace_editor tool.

        Args:
            kwargs: Tool parameters

        Returns:
            Tool output
        """
        command = kwargs.get("command")
        path_str = kwargs.get("path")
        
        if not command:
            return "Error: No command provided"
        if not path_str:
            return "Error: No path provided"
        
        try:
            path = self._resolve_path(path_str)
            
            if command == "view":
                # View file content
                if not path.exists():
                    return f"Error: File does not exist: {path}"
                
                content = self._read_file(path)
                view_range = kwargs.get("view_range")
                
                if view_range:
                    lines = content.split("\n")
                    start = max(0, view_range[0] - 1)
                    end = view_range[1] if view_range[1] != -1 else len(lines)
                    content = "\n".join(lines[start:end])
                
                return content
            
            elif command == "create":
                # Create a new file
                file_content = kwargs.get("file_content")
                if file_content is None:
                    return "Error: No file_content provided"
                
                self._write_file(path, file_content)
                return f"File created successfully at: {path}"
            
            elif command == "str_replace":
                # Replace content in a file
                old_str = kwargs.get("old_str")
                new_str = kwargs.get("new_str", "")
                
                if old_str is None:
                    return "Error: No old_str provided"
                
                if not path.exists():
                    return f"Error: File does not exist: {path}"
                
                content = self._read_file(path)
                occurrences = content.count(old_str)
                
                if occurrences == 0:
                    return f"Error: No occurrences of old_str found in {path}"
                elif occurrences > 1:
                    return f"Error: Multiple occurrences ({occurrences}) of old_str found in {path}"
                
                new_content = content.replace(old_str, new_str)
                self._write_file(path, new_content)
                
                return f"Successfully replaced content in {path}"
            
            elif command == "insert":
                # Insert content at a specific line
                insert_line = kwargs.get("insert_line")
                new_str = kwargs.get("new_str")
                
                if insert_line is None:
                    return "Error: No insert_line provided"
                if new_str is None:
                    return "Error: No new_str provided"
                
                content = self._read_file(path) if path.exists() else ""
                lines = content.split("\n")
                
                # Insert at the specified line
                if insert_line == 0:
                    lines.insert(0, new_str)
                elif insert_line > len(lines):
                    lines.append(new_str)
                else:
                    lines.insert(insert_line, new_str)
                
                new_content = "\n".join(lines)
                self._write_file(path, new_content)
                
                return f"Successfully inserted content at line {insert_line} in {path}"
            
            else:
                return f"Error: Unknown command: {command}"
        
        except Exception as e:
            return f"Error: {str(e)}"


def create_str_replace_editor_tool(
    workspace_root: Optional[Path] = None,
    max_file_size: int = 1024 * 1024,  # 1MB
) -> StrReplaceEditorTool:
    """Create a str_replace_editor tool.

    Args:
        workspace_root: Root directory of the workspace
        max_file_size: Maximum file size to read/write

    Returns:
        StrReplaceEditorTool instance
    """
    return StrReplaceEditorTool(
        workspace_root=workspace_root,
        max_file_size=max_file_size,
    )
