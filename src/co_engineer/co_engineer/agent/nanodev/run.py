import importlib
import yaml
import argparse
import os
import threading
import asyncio
import traceback
from pathlib import Path
from mcp import StdioServerParameters
from dotenv import load_dotenv

# from dotenv import load_dotenv

from smolagents import (
    ToolCallingAgent,
    OpenAIServerModel,
    ToolCollection,
    tool
)

from co_engineer.agent.nanodev.tools import (
    SequentialThinkingTool,
    create_bash_tool,
    create_str_replace_editor_tool,
    create_complete_tool
)


# Load environment variables from .env file
load_dotenv()

# load_dotenv(override=True)
append_answer_lock = threading.Lock()

# def parse_args():
#     parser = argparse.ArgumentParser()
#     parser.add_argument(
#         "task", type=str, help="for example: 'create nano to print out helloworld?'"
#     )
#     return parser.parse_args()

# Initialize variables to hold the initialized components
model = None
mcp_fs_params = None
mcp_cli_params = None
mcp_github_params = None
knowledge_based = None
prompt_templates = None

# Async initialization functions with exception handling
async def init_model():
    global model
    try:
        model = OpenAIServerModel(
            model_id="Qwen/Qwen3-235B-A22B",
            api_base="http://100.80.20.5:4000/v1",
            api_key="API_KEY",
            temperature=0.2,  # Lower temperature for more deterministic outputs
        )
        print("Model initialized successfully")
    except Exception as e:
        print(f"Error initializing model: {e}")
        traceback.print_exc()


async def init_mcp_fs_params():
    global mcp_fs_params
    try:
        mcp_fs_params = StdioServerParameters(
            command="mcp-filesystem-server",
            args=["/home/<USER>/sandbox/POC6"],
            env={
                **os.environ
            },
        )
        print("MCP filesystem parameters initialized successfully")
    except Exception as e:
        print(f"Error initializing MCP filesystem parameters: {e}")
        traceback.print_exc()


async def init_mcp_cli_params():
    global mcp_cli_params
    try:
        mcp_cli_params = StdioServerParameters(
            command="uvx",
            args=["cli-mcp-server"],
            env={
                "ALLOWED_DIR": "/home/<USER>/sandbox/POC6",
                "ALLOWED_COMMANDS": "all",
                "ALLOWED_FLAGS": "all",
                "MAX_COMMAND_LENGTH": "1024",
                "COMMAND_TIMEOUT": "300",
                "ALLOW_SHELL_OPERATORS": "true",
                **os.environ,
            },
        )
        print("MCP CLI parameters initialized successfully")
    except Exception as e:
        print(f"Error initializing MCP CLI parameters: {e}")
        traceback.print_exc()

# async def init_mcp_github_params():
#     global mcp_github_params
#     try:
#         mcp_github_params = StdioServerParameters(
#             command="docker",
#             args=["run", "-i", "--rm",
#                     "-e", "GITHUB_PERSONAL_ACCESS_TOKEN",
#                     "deep-nexus.fln.delllabs.net:8085/x-mcp/mcp-github"
#                 ],
#             env={
#                 **os.environ
#             }
#         )
#         print("MCP GitHub parameters initialized successfully")
#     except Exception as e:
#         print(f"Error initializing MCP GitHub parameters: {e}")
#         traceback.print_exc()

async def init_knowledge_based():
    global knowledge_based
    try:
        knowledge_based = StdioServerParameters(
            command="uv",
            args=[ "run",
                "co_engineer/agent/knowledge_based/main.py"
                ]
        )
        print("Knowledge base parameters initialized successfully")
    except Exception as e:
        print(f"Error initializing knowledge base parameters: {e}")
        traceback.print_exc()

async def init_prompt_templates():
    global prompt_templates
    try:
        prompt_templates = yaml.safe_load(
            importlib.resources.files("co_engineer.agent.nanodev.prompts").joinpath("nanodev_agent.yaml").read_text()
        )
        print("Prompt templates loaded successfully")
    except Exception as e:
        print(f"Error loading prompt templates: {e}")
        traceback.print_exc()

# Main initialization function that runs all initializations concurrently
async def initialize_all():
    tasks = [
        init_model(),
        init_mcp_fs_params(),
        init_mcp_cli_params(),
        # init_mcp_github_params(),
        init_knowledge_based(),
        init_prompt_templates()
    ]
    await asyncio.gather(*tasks)
    print("All initializations completed")

# Run the initialization
def initialize():
    asyncio.run(initialize_all())


def run_task(task):
    # Make sure all components are initialized
    if None in [model, mcp_fs_params, mcp_cli_params, knowledge_based, prompt_templates]:
        print("Some components are not initialized. Running initialization...")
        initialize()

    try:
        # Create custom tools from sequential_thinking_coder
        workspace_root = Path("/home/<USER>/sandbox/POC6")

        # Create sequential thinking tool
        @tool
        def sequential_thinking(
            thought: str,
            thoughtNumber: int,
            totalThoughts: int,
            nextThoughtNeeded: bool,
            isRevision: bool = False,
            revisesThought: int = None,
            branchFromThought: int = None,
            branchId: str = None,
            needsMoreThoughts: bool = None
        ) -> str:
            """A detailed tool for dynamic and reflective problem-solving through thoughts.
            This tool helps analyze problems through a flexible thinking process that can adapt and evolve.
            Each thought can build on, question, or revise previous insights as understanding deepens.

            Args:
                thought: Your current thinking step
                thoughtNumber: Current number in sequence
                totalThoughts: Current estimate of thoughts needed
                nextThoughtNeeded: True if you need more thinking
                isRevision: A boolean indicating if this thought revises previous thinking
                revisesThought: If is_revision is true, which thought number is being reconsidered
                branchFromThought: Which thought this branches from
                branchId: Branch identifier
                needsMoreThoughts: If more thoughts are needed
            """
            sequential_thinking_instance = SequentialThinkingTool(verbose=True)
            return sequential_thinking_instance(
                thought=thought,
                thoughtNumber=thoughtNumber,
                totalThoughts=totalThoughts,
                nextThoughtNeeded=nextThoughtNeeded,
                isRevision=isRevision,
                revisesThought=revisesThought,
                branchFromThought=branchFromThought,
                branchId=branchId,
                needsMoreThoughts=needsMoreThoughts
            )

        # Create bash tool
        @tool
        def bash(command: str) -> str:
            """Run commands in a bash shell.
            You don't have access to the internet via this tool.
            You do have access to common linux and python packages via apt and pip.
            State is persistent across command calls.

            Args:
                command: The command to execute
            """
            bash_instance = create_bash_tool(workspace_root=workspace_root)
            return bash_instance(command=command)

        # Create str_replace_editor tool
        @tool
        def str_replace_editor(
            command: str,
            path: str,
            file_content: str = None,
            view_range: list = None,
            old_str: str = None,
            new_str: str = None,
            insert_line: int = None
        ) -> str:
            """Custom editing tool for viewing, creating and editing files.
            Use 'view' command to read files, 'create' to create new files, 'str_replace' to edit existing files,
            and 'insert' to insert content at specific lines.

            Args:
                command: The command to run (view, create, str_replace, insert)
                path: The path to the file
                file_content: The content of the file (for create command)
                view_range: The range of lines to view (for view command)
                old_str: The string to replace (for str_replace command)
                new_str: The new string (for str_replace and insert commands)
                insert_line: The line to insert at (for insert command)
            """
            str_replace_instance = create_str_replace_editor_tool(workspace_root=workspace_root)
            return str_replace_instance(
                command=command,
                path=path,
                file_content=file_content,
                view_range=view_range,
                old_str=old_str,
                new_str=new_str,
                insert_line=insert_line
            )

        # Create complete tool
        @tool
        def complete(answer: str) -> str:
            """Call this tool when you are done with the task, and supply your answer or summary.

            Args:
                answer: The answer to the question, or final summary of actions taken
            """
            complete_instance = create_complete_tool()
            return complete_instance(answer=answer)

        with ToolCollection.from_mcp([mcp_fs_params, mcp_cli_params, knowledge_based], trust_remote_code=True) as tool_collection:
            # Combine MCP tools with our custom tools
            all_tools = [sequential_thinking, bash, str_replace_editor, complete]

            nanodev_agent = ToolCallingAgent(
                model=model,
                prompt_templates=prompt_templates,
                tools=all_tools,
                max_steps=120,
                verbosity_level=3,
                planning_interval=2,
                add_base_tools=False,
            )
            return nanodev_agent.run(task=task)
    except Exception as e:
        print(f"Error running task: {e}")
        traceback.print_exc()
        return None


if __name__ == "__main__":
    # Initialize all components asynchronously
    print("Starting initialization...")
    initialize()

    # Run the task after initialization is complete
    task = "please help to write a python scripts that use redfish to reboot a dell poweredge server. note when sending request, please disable tls verify, the BMC_URL=https://************ BMC_USER=root BMC_PASSWORD=calvin. Instead of environment variables, can we use CLI arguments to pass the parameters?"
    result = run_task(task)
    print(f"Result: {result}")
