from r2r import R2RClient
from mcp.server.fastmcp import FastMCP

def id_to_shorthand(id: str) -> str:
    return str(id)[:7]


def format_search_results_for_llm(
    results,
) -> str:
    """
    Instead of resetting 'source_counter' to 1, we:
     - For each chunk / graph / web / doc in `results`,
     - Find the aggregator index from the collector,
     - Print 'Source [X]:' with that aggregator index.
    """
    lines = []

    # We'll build a quick helper to locate aggregator indices for each object:
    # Or you can rely on the fact that we've added them to the collector
    # in the same order. But let's do a "lookup aggregator index" approach:

    # 1) Chunk search
    if results.chunk_search_results:
        lines.append("Vector Search Results:")
        for c in results.chunk_search_results:
            lines.append(f"Source ID [{id_to_shorthand(c.id)}]:")
            lines.append(c.text or "")  # or c.text[:200] to truncate

    # 2) Graph search
    if results.graph_search_results:
        lines.append("Graph Search Results:")
        for g in results.graph_search_results:
            lines.append(f"Source ID [{id_to_shorthand(g.id)}]:")
            if hasattr(g.content, "summary"):
                lines.append(f"Community Name: {g.content.name}")
                lines.append(f"ID: {g.content.id}")
                lines.append(f"Summary: {g.content.summary}")
                # etc. ...
            elif hasattr(g.content, "name") and hasattr(
                g.content, "description"
            ):
                lines.append(f"Entity Name: {g.content.name}")
                lines.append(f"Description: {g.content.description}")
            elif (
                hasattr(g.content, "subject")
                and hasattr(g.content, "predicate")
                and hasattr(g.content, "object")
            ):
                lines.append(
                    f"Relationship: {g.content.subject}-{g.content.predicate}-{g.content.object}"
                )
            # Add metadata if needed

    # 3) Web search
    if results.web_search_results:
        lines.append("Web Search Results:")
        for w in results.web_search_results:
            lines.append(f"Source ID [{id_to_shorthand(w.id)}]:")
            lines.append(f"Title: {w.title}")
            lines.append(f"Link: {w.link}")
            lines.append(f"Snippet: {w.snippet}")

    # 4) Local context docs
    if results.document_search_results:
        lines.append("Local Context Documents:")
        for doc_result in results.document_search_results:
            doc_title = doc_result.title or "Untitled Document"
            doc_id = doc_result.id
            summary = doc_result.summary

            lines.append(f"Full Document ID: {doc_id}")
            lines.append(f"Shortened Document ID: {id_to_shorthand(doc_id)}")
            lines.append(f"Document Title: {doc_title}")
            if summary:
                lines.append(f"Summary: {summary}")

            if doc_result.chunks:
                # Then each chunk inside:
                for chunk in doc_result.chunks:
                    lines.append(
                        f"\nChunk ID {id_to_shorthand(chunk['id'])}:\n{chunk['text']}"
                    )

    result = "\n".join(lines)
    return result


# Pass lifespan to server
mcp = FastMCP("R2R Retrieval System")


# RAG query tool
@mcp.tool()
async def search(query: str) -> str:
    """
    Performs a comprehensive search across the knowledge base to retrieve best practices, guidelines, and reference materials.

    This tool searches through multiple sources including vector databases, graph knowledge bases, web resources, and local documents
    to provide you with the most relevant best practices and implementation patterns. Use this when you need detailed information
    about specific topics, technical implementations, or industry standards.

    Args:
        query: The question to answer using the knowledge base - be specific about what best practices or guidelines you're looking for

    Returns:
        A detailed response containing best practices, implementation patterns, and reference materials from various sources in the knowledge base,
        including source IDs for traceability and verification. Results may include code examples, architectural patterns, security guidelines,
        performance optimization techniques, and other industry best practices.
    """
    client = R2RClient("http://***********:7272")

    # Call the RAG endpoint
    search_response = client.retrieval.search(
        query=query,
    )
    return format_search_results_for_llm(search_response.results)


# # RAG query tool
# @mcp.tool()
# async def rag(query: str) -> str:
#     """
#     Perform a Retrieval-Augmented Generation query to access comprehensive best practices and expert knowledge.

#     This tool leverages advanced AI to generate detailed responses based on a vast knowledge base of industry best practices,
#     design patterns, implementation guidelines, and expert recommendations. It synthesizes information from multiple authoritative
#     sources to provide you with well-reasoned, practical advice tailored to your specific query.

#     Use this tool when you need:
#     - Comprehensive best practices for software development, architecture, or system design
#     - Expert recommendations on implementation approaches
#     - Industry standards and guidelines for specific technologies
#     - Comparative analysis of different methodologies or techniques
#     - Practical solutions to complex technical challenges

#     Args:
#         query: The question to answer using the knowledge base - be specific about what best practices or expert knowledge you're seeking

#     Returns:
#         A comprehensive, AI-generated response that synthesizes best practices, expert knowledge, and implementation guidelines
#         from the knowledge base. The response will provide actionable insights, practical recommendations, and references to
#         established patterns and methodologies that address your specific query.
#     """
#     client = R2RClient("http://***********:7272")

#     # Call the RAG endpoint
#     rag_response = client.retrieval.rag(
#         query=query,
#     )

#     return rag_response.results.generated_answer  # type: ignore


# Run the server if executed directly
if __name__ == "__main__":
    mcp.run()
