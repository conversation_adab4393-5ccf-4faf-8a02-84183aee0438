#cloud-config
autoinstall:
  version: 1
  update: yes
  keyboard: {layout: us, toggle: null, variant: ''}
  locale: en_US.UTF-8
  identity:
    hostname: projectx-vm
    password: "$6$rounds=4096$saltsaltlettuce$Lp/FV.2oOgew7GbM6Nr8KMGMBn7iFM0x9ZwLqtx9Y4QJmKvfcnS.2zx4MKmymCPQGpHS7gqYOiqWjvdCIV2uN."
    username: mystic
  ssh:
    allow-pw: true
    install-server: true
  network:
    version: 2
    ethernets:
      # 主要网络接口 - 配置默认网关
      eth0:
        match:
          macaddress: "52:54:00:12:34:56"
        dhcp4: false
        addresses:
          - *************/24
        gateway4: ***********  # 系统默认网关
        nameservers:
          addresses: [*******, *******]

      # 次要网络接口 - 只配置特定路由，不设置默认网关
      eth1:
        match:
          macaddress: "52:54:00:12:34:57"
        dhcp4: false
        addresses:
          - ********00/24
        routes:
          - to: 10.0.0.0/16
            via: ********

  user-data: # Cloud-init starts here
    disable_root: false
    users:
      - name: mystic
        gecos: VM adminsitrator
        groups: users, admin, docker, sudo
        sudo: ALL=(ALL) NOPASSWD:ALL
        shell: /bin/bash
        lock_passwd: false
        passwd: "$6$rounds=4096$saltsaltlettuce$Lp/FV.2oOgew7GbM6Nr8KMGMBn7iFM0x9ZwLqtx9Y4QJmKvfcnS.2zx4MKmymCPQGpHS7gqYOiqWjvdCIV2uN."
    # 网络配置完成后运行的命令
    runcmd:
      - echo "Static network configuration applied successfully" > /var/log/network-config.log
      - ip addr show >> /var/log/network-config.log
      - 
