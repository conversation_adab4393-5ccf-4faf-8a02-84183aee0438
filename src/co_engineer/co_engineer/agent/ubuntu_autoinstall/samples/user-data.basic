#cloud-config
autoinstall:
  version: 1
  update: yes
  keyboard: {layout: us, toggle: null, variant: ''}
  locale: en_US.UTF-8
  identity:
    hostname: projectx-vm
    password: "$6$rounds=4096$saltsaltlettuce$Lp/FV.2oOgew7GbM6Nr8KMGMBn7iFM0x9ZwLqtx9Y4QJmKvfcnS.2zx4MKmymCPQGpHS7gqYOiqWjvdCIV2uN."
    username: mystic
  ssh:
    allow-pw: true
    install-server: true
  network:
    version: 2
    ethernets:
      default:
        dhcp4: true
  user-data: # Cloud-init starts here
    disable_root: false
    users:
      - name: mystic
        gecos: VM adminsitrator
        groups: users, admin, docker, sudo
        sudo: ALL=(ALL) NOPASSWD:ALL
        shell: /bin/bash
        lock_passwd: false
        passwd: "$6$rounds=4096$saltsaltlettuce$Lp/FV.2oOgew7GbM6Nr8KMGMBn7iFM0x9ZwLqtx9Y4QJmKvfcnS.2zx4MKmymCPQGpHS7gqYOiqWjvdCIV2uN."
