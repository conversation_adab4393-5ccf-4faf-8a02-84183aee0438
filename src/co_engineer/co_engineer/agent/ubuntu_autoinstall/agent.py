"""
Ubuntu Autoinstall Agent - Creates Ubuntu autoinstall ISOs using autogen framework.

This agent helps users create customized Ubuntu autoinstall ISOs by:
1. Creating user-data files with proper configuration
2. Generating password hashes for user accounts
3. Using Docker to create bootable ISOs with the autoinstall configuration
"""

import os
import asyncio
import logging
import subprocess
import tempfile
import yaml
import crypt
import random
import string
from typing import Dict, List, Optional, Any, Union

from autogen_agentchat.agents import AssistantAgent
from autogen_core import CancellationToken
from autogen_ext.models.openai import OpenAIChatCompletionClient

from co_engineer.models import model_client_qwq, model_client_deepseek_v3_0324

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# System message for the agent
SYSTEM_MESSAGE = """
**Goal:** Generate a customized Ubuntu Autoinstall ISO file based *solely* on user-provided configuration details.

**Input:** User provides configuration parameters (e.g., desired Ubuntu version, username, network settings, packages, etc.).

**Core Constraint:** **ALL** operational steps required to achieve the goal (from processing input to generating the final ISO) **MUST** be executed using the available tool calls. You **MUST NOT** perform actions or steps directly yourself, nor should you ask the user for confirmation *before* calling a tool when you have sufficient information.

**Workflow (Executed via Tool Calls):**

1.  **Input Validation & Preparation (Internal Logic Leading to Tool Call):**
    * Receive user input containing configuration details.
    * **Crucially:** Evaluate if the provided input contains *all* necessary information to proceed with the user's request (e.g., if static networking is requested, check for IP, Netmask, Gateway, DNS).
    * If required information is missing, **STOP** the process immediately and inform the user precisely which information is needed. **This is the ONLY allowed non-tool interaction during the process flow.**
    * If information is sufficient, prepare the configuration data structure required by the tool calls.

2.  **Create user-data file (Tool Call):**
    * Use the dedicated tool to generate the `user-data` configuration file based on the validated and prepared user input.

3.  **Generate ISO (Tool Call):**
    * Use the dedicated tool to create the final autoinstall ISO image, incorporating the `user-data` file generated in the previous step. This tool is responsible for handling the underlying process (like using Docker).

**Supported Configurations (Passed as parameters to Tool Calls):**

* `ubuntu_codename`: (default: `noble`)
* `username`: (default: `mystic`)
* `password`: (default: `mystic`)
* `hostname`: (default: `projectx-vm`)
* `ssh_keys`: (optional list of keys)
* `packages`: (optional list of packages to install)
* `network_config`: (DHCP or static)
    * For static, REQUIRES: `ip_address`, `netmask`, `gateway`, `dns_servers` (list).
* `custom_config`: (optional dictionary for other cloud-init configurations)

**Strict Order of Operations:**

1.  Receive and **internally validate** user input for completeness. If incomplete, request missing info from user and stop.
2.  If input is complete, **IMMEDIATELY call the tool** to create the `user-data` file.
3.  Upon successful user-data creation (tool output indicates success), **IMMEDIATELY call the tool** to generate the ISO, using the result of step 2.

**Output:** The result of the final tool call (e.g., a success message, a file path, or an error from the tool).
"""

async def generate_password_hash(password: str) -> str:
    """
    Generate a password hash suitable for Ubuntu autoinstall.

    Args:
        password: The plain text password to hash

    Returns:
        A salted SHA-512 password hash
    """
    # Generate a random salt
    salt = ''.join(random.choice(string.ascii_letters + string.digits) for _ in range(16))

    # Generate the password hash using SHA-512 with 4096 rounds
    password_hash = crypt.crypt(password, f'$6$rounds=4096${salt}$')

    logger.info("Password hash generated successfully")
    return password_hash

async def create_user_data_file(
    ubuntu_codename: str = "noble",
    username: str = "mystic",
    password: str = "mystic",
    hostname: str = "projectx-vm",
    ssh_keys: Optional[List[str]] = None,
    packages: Optional[List[str]] = None,
    network_config: str = "dhcp",
    ip_address: Optional[str] = None,
    netmask: Optional[str] = None,
    gateway: Optional[str] = None,
    dns_servers: Optional[List[str]] = None,
    mac_address: Optional[str] = None,
    output_dir: Optional[str] = None
) -> Dict[str, str]:
    """
    Create a user-data file for Ubuntu autoinstall.

    Args:
        ubuntu_codename: Ubuntu release codename
        username: Username for the primary user
        password: Password for the primary user
        hostname: Hostname for the system
        ssh_keys: List of SSH public keys to add
        packages: List of packages to install
        network_config: Network configuration type ('dhcp' or 'static')
        ip_address: Static IP address (required if network_config is 'static')
        netmask: Network mask in CIDR format (required if network_config is 'static')
        gateway: Default gateway (required if network_config is 'static')
        dns_servers: List of DNS servers (required if network_config is 'static')
        mac_address: MAC address for network interface (optional for static config)
        output_dir: Directory to save the user-data file

    Returns:
        Dictionary with paths to the created user-data files
    """
    # Generate password hash
    password_hash = await generate_password_hash(password)

    # Create basic autoinstall configuration
    autoinstall_config = {
        "version": 1,
        "update": "yes",
        "keyboard": {"layout": "us", "toggle": None, "variant": ""},
        "locale": "en_US.UTF-8",
        "identity": {
            "hostname": hostname,
            "password": password_hash,
            "username": username
        },
        "ssh": {
            "allow-pw": True,
            "install-server": True
        }
    }

    # Configure network
    if network_config == "dhcp":
        # No additional configuration needed for DHCP
        pass
    elif network_config == "static":
        if not all([ip_address, netmask, gateway, dns_servers]):
            raise ValueError("Static network configuration requires IP address, netmask, gateway, and DNS servers")

        network_interface = "eth0"
        network_match = {}

        if mac_address:
            network_match["macaddress"] = mac_address

        autoinstall_config["network"] = {
            "version": 2,
            "ethernets": {
                network_interface: {
                    "dhcp4": False,
                    "addresses": [f"{ip_address}/{netmask}"],
                    "gateway4": gateway,
                    "nameservers": {
                        "addresses": dns_servers
                    }
                }
            }
        }

        if mac_address:
            autoinstall_config["network"]["ethernets"][network_interface]["match"] = network_match

    # Add packages if specified
    if packages:
        autoinstall_config["packages"] = packages

    # Configure user-data section
    user_data_section = {
        "disable_root": False,
        "users": [
            {
                "name": username,
                "gecos": "VM administrator",
                "groups": "users, admin, docker, sudo",
                "sudo": "ALL=(ALL) NOPASSWD:ALL",
                "shell": "/bin/bash",
                "lock_passwd": False,
                "passwd": password_hash
            }
        ]
    }

    # Add SSH keys if provided
    if ssh_keys:
        user_data_section["users"][0]["ssh_authorized_keys"] = ssh_keys

    # Add user-data section to autoinstall config
    autoinstall_config["user-data"] = user_data_section

    # Create output directory if not specified
    if not output_dir:
        output_dir = tempfile.mkdtemp(prefix="ubuntu_autoinstall_")
    else:
        os.makedirs(output_dir, exist_ok=True)

    # Create user-data file
    user_data_path = os.path.join(output_dir, "user-data")
    with open(user_data_path, "w") as f:
        f.write("#cloud-config\n")
        f.write("autoinstall:\n")

        # Write each section with proper indentation
        f.write("  version: 1\n")
        f.write("  update: yes\n")
        f.write("  keyboard: {layout: us, toggle: null, variant: ''}\n")
        f.write("  locale: en_US.UTF-8\n")

        # Identity section
        f.write("  identity:\n")
        f.write(f"    hostname: {hostname}\n")
        f.write(f"    password: \"{password_hash}\"\n")
        f.write(f"    username: {username}\n")

        # SSH section
        f.write("  ssh:\n")
        f.write("    allow-pw: true\n")
        f.write("    install-server: true\n")

        if network_config == "dhcp":
            pass
        elif network_config == "static":
            # Network section
            f.write("  network:\n")
            f.write("    version: 2\n")
            f.write("    ethernets:\n")
            f.write("  network:\n")
            f.write("    version: 2\n")
            f.write("    ethernets:\n")
            f.write(f"      eth0:\n")
            if mac_address:
                f.write("        match:\n")
                f.write(f"          macaddress: \"{mac_address}\"\n")
            f.write("        dhcp4: false\n")
            f.write(f"        addresses:\n")
            f.write(f"          - {ip_address}/{netmask}\n")
            f.write(f"        gateway4: {gateway}\n")
            f.write("        nameservers:\n")
            f.write("          addresses:\n")
            for dns in dns_servers:
                f.write(f"            - {dns}\n")

        # Packages section
        if packages:
            f.write("  packages:\n")
            for pkg in packages:
                f.write(f"    - {pkg}\n")

        # User-data section
        f.write("  user-data: # Cloud-init starts here\n")
        f.write("    disable_root: false\n")
        f.write("    users:\n")
        f.write(f"      - name: {username}\n")
        f.write("        gecos: VM administrator\n")
        f.write("        groups: users, admin, docker, sudo\n")
        f.write("        sudo: ALL=(ALL) NOPASSWD:ALL\n")
        f.write("        shell: /bin/bash\n")
        f.write("        lock_passwd: false\n")
        f.write(f"        passwd: \"{password_hash}\"\n")

        # SSH keys if provided
        if ssh_keys:
            f.write("        ssh_authorized_keys:\n")
            for key in ssh_keys:
                f.write(f"          - {key}\n")


    logger.info(f"Created user-data file at {user_data_path}")

    return {
        "user_data": user_data_path,
        "output_dir": output_dir
    }

async def create_ubuntu_autoinstall_iso(
    user_data_path: str,
    ubuntu_codename: str = "noble",
    output_iso: Optional[str] = None,
    output_dir: Optional[str] = None
) -> str:
    """
    Create an Ubuntu autoinstall ISO using Docker.

    Args:
        user_data_path: Path to the user-data file
        ubuntu_codename: Ubuntu release codename
        output_iso: Name of the output ISO file
        output_dir: Directory to save the ISO file

    Returns:
        Path to the created ISO file
    """
    # Create output directory if not specified
    if not output_dir:
        output_dir = os.getcwd()
    else:
        os.makedirs(output_dir, exist_ok=True)

    # Set default output ISO name if not specified
    if not output_iso:
        output_iso = f"ubuntu-autoinstall-{ubuntu_codename}.iso"

    # Get absolute paths
    user_data_dir = os.path.dirname(os.path.abspath(user_data_path))
    user_data_filename = os.path.basename(user_data_path)
    output_iso_path = os.path.join(output_dir, output_iso)

    logger.info(f"User data dir: {user_data_dir}")

    # Run Docker command to create ISO
    docker_cmd = [
        "docker", "run", "--rm",
        "--volume", f"{user_data_dir}:/data",
        "deserializeme/pxeless",
        "--all-in-one",
        "--user-data", f"{user_data_filename}",
        "--code-name", ubuntu_codename,
        "--use-release-iso",
        "--destination", f"/data/{output_iso}"
    ]

    try:
        logger.info("Creating Ubuntu autoinstall ISO using Docker...")
        process = await asyncio.create_subprocess_exec(
            *docker_cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )

        stdout, stderr = await process.communicate()

        if process.returncode != 0:
            logger.error(f"Error creating ISO: {stderr.decode()}")
            raise RuntimeError(f"Failed to create ISO: {stderr.decode()}")

        logger.info(f"Created Ubuntu autoinstall ISO at {output_iso_path}")
        return output_iso_path

    except Exception as e:
        logger.error(f"Error running Docker command: {e}")
        raise

async def create_ubuntu_autoinstall_all_in_one(
    ubuntu_codename: str = "noble",
    username: str = "mystic",
    password: str = "mystic",
    hostname: str = "projectx-vm",
    ssh_keys: Optional[List[str]] = None,
    packages: Optional[List[str]] = None,
    network_config: str = "dhcp",
    ip_address: Optional[str] = None,
    netmask: Optional[str] = None,
    gateway: Optional[str] = None,
    dns_servers: Optional[List[str]] = None,
    mac_address: Optional[str] = None,
    output_iso: Optional[str] = None,
    output_dir: Optional[str] = None
) -> Dict[str, str]:
    """
    Create an Ubuntu autoinstall ISO with all configurations in one step.

    Args:
        ubuntu_codename: Ubuntu release codename
        username: Username for the primary user
        password: Password for the primary user
        hostname: Hostname for the system
        ssh_keys: List of SSH public keys to add
        packages: List of packages to install
        network_config: Network configuration type ('dhcp' or 'static')
        ip_address: Static IP address (required if network_config is 'static')
        netmask: Network mask in CIDR format (required if network_config is 'static')
        gateway: Default gateway (required if network_config is 'static')
        dns_servers: List of DNS servers (required if network_config is 'static')
        mac_address: MAC address for network interface (optional for static config)
        output_iso: Name of the output ISO file
        output_dir: Directory to save the ISO file

    Returns:
        Dictionary with paths to the created files
    """
    # Create user-data files
    config_files = await create_user_data_file(
        ubuntu_codename=ubuntu_codename,
        username=username,
        password=password,
        hostname=hostname,
        ssh_keys=ssh_keys,
        packages=packages,
        network_config=network_config,
        ip_address=ip_address,
        netmask=netmask,
        gateway=gateway,
        dns_servers=dns_servers,
        mac_address=mac_address,
        output_dir=output_dir
    )

    # Create ISO
    iso_path = await create_ubuntu_autoinstall_iso(
        user_data_path=config_files["user_data"],
        ubuntu_codename=ubuntu_codename,
        output_iso=output_iso,
        output_dir=output_dir
    )

    return {
        "user_data": config_files["user_data"],
        "iso_path": iso_path
    }

# Create the agent
async def create_agent(model_client: Optional[OpenAIChatCompletionClient] = None) -> AssistantAgent:
    """
    Create and configure the Ubuntu Autoinstall ISO Generator agent.

    Args:
        model_client: ModelClient instance for the agent

    Returns:
        Configured AssistantAgent
    """
    # If no model client is provided, create one with temperature=0
    if model_client is None:

        # Create a copy of the model client with temperature=0
        model_client = model_client_qwq

    # Create the agent with tools
    agent = AssistantAgent(
        name="ubuntu_autoinstall_agent",
        model_client=model_client,
        system_message=SYSTEM_MESSAGE,
        tools=[
            # generate_password_hash,
            # create_user_data_file,
            # create_ubuntu_autoinstall_iso,
            create_ubuntu_autoinstall_all_in_one
        ],
        reflect_on_tool_use=True,
        model_client_stream=False,
    )

    return agent

async def main():
    """
    Main function to run the Ubuntu Autoinstall ISO Generator agent.
    """
    agent = await create_agent()

    result = await agent.run(
        task="Create an Ubuntu autoinstall ISO with default settings.",
        cancellation_token=CancellationToken()
    )

    print("\nAgent execution completed.")
    print(f"Result: {result}")

if __name__ == "__main__":
    asyncio.run(main())
