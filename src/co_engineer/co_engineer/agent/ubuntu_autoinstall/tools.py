"""
Helper tools for the Ubuntu Autoinstall ISO Generator agent.
"""

import os
import subprocess
import tempfile
import logging
import crypt
import random
import string
from typing import Dict, List, Optional, Any, Union

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_docker_installed() -> bool:
    """
    Check if Docker is installed on the system.
    
    Returns:
        True if Docker is installed, False otherwise
    """
    try:
        subprocess.run(["docker", "--version"], check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        return True
    except (subprocess.SubprocessError, FileNotFoundError):
        return False

def check_docker_image_exists(image_name: str) -> bool:
    """
    Check if a Docker image exists locally.
    
    Args:
        image_name: Name of the Docker image to check
        
    Returns:
        True if the image exists, False otherwise
    """
    try:
        result = subprocess.run(
            ["docker", "image", "ls", image_name, "--format", "{{.Repository}}"],
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        return bool(result.stdout.strip())
    except subprocess.SubprocessError:
        return False

def pull_docker_image(image_name: str) -> bool:
    """
    Pull a Docker image from Docker Hub.
    
    Args:
        image_name: Name of the Docker image to pull
        
    Returns:
        True if successful, False otherwise
    """
    try:
        subprocess.run(["docker", "pull", image_name], check=True)
        return True
    except subprocess.SubprocessError:
        return False

def generate_password_hash_sync(password: str) -> str:
    """
    Generate a password hash suitable for Ubuntu autoinstall (synchronous version).
    
    Args:
        password: The plain text password to hash
        
    Returns:
        A salted SHA-512 password hash
    """
    # Generate a random salt
    salt = ''.join(random.choice(string.ascii_letters + string.digits) for _ in range(16))
    
    # Generate the password hash using SHA-512 with 4096 rounds
    password_hash = crypt.crypt(password, f'$6$rounds=4096${salt}$')
    
    logger.info("Password hash generated successfully")
    return password_hash

def parse_cidr(cidr: str) -> tuple:
    """
    Parse a CIDR notation string into IP address and prefix length.
    
    Args:
        cidr: CIDR notation string (e.g., "***********/24")
        
    Returns:
        Tuple of (ip_address, prefix_length)
    """
    if "/" in cidr:
        ip, prefix = cidr.split("/")
        return ip, prefix
    else:
        return cidr, None

def validate_static_network_config(
    ip_address: str,
    netmask: str,
    gateway: str,
    dns_servers: List[str]
) -> bool:
    """
    Validate static network configuration parameters.
    
    Args:
        ip_address: Static IP address
        netmask: Network mask in CIDR format
        gateway: Default gateway
        dns_servers: List of DNS servers
        
    Returns:
        True if valid, False otherwise
    """
    # Basic validation - could be expanded with more sophisticated checks
    if not all([ip_address, netmask, gateway, dns_servers]):
        return False
    
    # Check if netmask is a valid CIDR prefix
    try:
        netmask_int = int(netmask)
        if netmask_int < 0 or netmask_int > 32:
            return False
    except ValueError:
        return False
    
    # Check if at least one DNS server is provided
    if not dns_servers or len(dns_servers) == 0:
        return False
    
    return True

def ensure_docker_ready(image_name: str = "deserializeme/pxeless") -> bool:
    """
    Ensure Docker is installed and the required image is available.
    
    Args:
        image_name: Name of the Docker image to check/pull
        
    Returns:
        True if Docker is ready, False otherwise
    """
    # Check if Docker is installed
    if not check_docker_installed():
        logger.error("Docker is not installed. Please install Docker to use this tool.")
        return False
    
    # Check if the image exists, pull it if not
    if not check_docker_image_exists(image_name):
        logger.info(f"Docker image {image_name} not found locally. Pulling from Docker Hub...")
        if not pull_docker_image(image_name):
            logger.error(f"Failed to pull Docker image {image_name}. Please check your internet connection.")
            return False
    
    return True
