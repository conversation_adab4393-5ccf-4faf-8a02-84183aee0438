# Ubuntu Autoinstall ISO Generator Agent

This package provides an autogen agent for creating Ubuntu autoinstall ISOs. The agent helps users create customized Ubuntu autoinstall ISOs by generating user-data files and using Docker to create bootable ISOs.

## Features

- Generate password hashes for user accounts
- Create user-data files with various configuration options
- Support for both DHCP and static network configurations
- Package installation configuration
- SSH key configuration
- Create bootable Ubuntu autoinstall ISOs using Docker

## Installation

This package is part of the `co_engineer` project. To use it, make sure you have the required dependencies:

```bash
cd x/src/co_engineer
uv venv
source .venv/bin/activate
```

You also need Docker installed on your system to create the ISOs.

## Usage

### A2A Server

The package provides an Agent-to-Agent (A2A) server for creating Ubuntu autoinstall ISOs:

```bash
# Start the server on localhost:10001 (default)
uv run co_engineer/agent/ubuntu_autoinstall/a2a_server.py

# Start the server on a custom host and port
uv run co_engineer/agent/ubuntu_autoinstall/a2a_server.py --host 0.0.0.0 --port 10001
```

#### Connecting to the A2A Server

In a separate terminal, run the A2A client:

```bash
cd x/src/co_engineer
source .venv/bin/activate
uv run co_engineer/agent/a2a/hosts/cli --agent http://localhost:10001
```

#### Example Requests

Once connected to the A2A server, you can send requests like:

```
What do you want to send to the agent? (:q or quit to exit)
Create an Ubuntu autoinstall ISO with hostname 'my-server', username 'admin', and password 'secure123'
```

```
What do you want to send to the agent? (:q or quit to exit)
Generate an Ubuntu noble autoinstall ISO with static IP configuration: IP *************, netmask 24, gateway ***********, DNS servers ******* and *******
```

### Command Line Interface

The package provides a command-line interface for creating Ubuntu autoinstall ISOs:

#### Interactive Mode

```bash
python -m co_engineer.agent.ubuntu_autoinstall.cli interactive
```

This mode allows you to chat with the agent and ask it to create autoinstall ISOs with specific configurations.

#### Direct Mode

```bash
cd x/src/co_engineer
uv venv
source .venv/bin/activate
uv run co_engineer/agent/ubuntu_autoinstall/cli.py direct \
  --ubuntu-codename noble \
  --hostname my-server \
  --username admin \
  --password secure123 \
  --packages vim,git,curl \
  --network-config dhcp \
  --output-iso my-ubuntu-autoinstall.iso
```

For static network configuration:

```bash
cd x/src/co_engineer
uv venv
source .venv/bin/activate
uv run co_engineer/agent/ubuntu_autoinstall/cli.py direct \
  --ubuntu-codename noble \
  --hostname my-server \
  --username admin \
  --password secure123 \
  --packages vim,git,curl \
  --network-config static \
  --ip-address ***********23 \
  --netmask 24 \
  --gateway *********** \
  --dns-servers ***********,*********** \
  --output-iso my-ubuntu-autoinstall-static.iso
```

### Python API

You can also use the agent programmatically:

```python
import asyncio
from co_engineer.agent.ubuntu_autoinstall import create_agent, create_ubuntu_autoinstall_all_in_one

async def main():
    # Using the agent
    agent = await create_agent()
    result = await agent.run(
        task="Create an Ubuntu autoinstall ISO with hostname 'my-server', username 'admin', and password 'secure123'."
    )

    # Or using the direct function
    result = await create_ubuntu_autoinstall_all_in_one(
        ubuntu_codename="noble",
        username="admin",
        password="secure123",
        hostname="my-server",
        packages=["vim", "git", "curl"],
        network_config="dhcp",
        output_iso="my-ubuntu-autoinstall.iso"
    )

    print(f"Ubuntu autoinstall ISO created at: {result['iso_path']}")

if __name__ == "__main__":
    asyncio.run(main())
```

## Configuration Options

The agent supports the following configuration options:

- **Ubuntu codename**: The Ubuntu release codename (e.g., "noble", "jammy")
- **Username**: Username for the primary user
- **Password**: Password for the primary user
- **Hostname**: Hostname for the system
- **SSH keys**: List of SSH public keys to add
- **Packages**: List of packages to install
- **Network configuration**: DHCP or static
  - For static configuration: IP address, netmask, gateway, DNS servers, and optional MAC address

## Example

See the included example script:

```bash
python -m co_engineer.agent.ubuntu_autoinstall.example
```

## Docker Requirements

The agent uses the `deserializeme/pxeless` Docker image to create the autoinstall ISOs. Make sure Docker is installed and running on your system.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
