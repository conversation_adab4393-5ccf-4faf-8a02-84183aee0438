"""
Command-line interface for the Ubuntu Autoinstall ISO Generator agent.
"""

import os
import sys
import asyncio
import argparse
import logging
from typing import List, Optional

from autogen_core import CancellationToken
from autogen_agentchat.ui import Console

from co_engineer.agent.ubuntu_autoinstall.agent import create_agent

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def interactive_mode():
    """
    Run the agent in interactive mode, allowing the user to chat with the agent.
    """
    agent = await create_agent()

    print("=== Ubuntu Autoinstall ISO Generator Agent ===")
    print("Type 'exit' or 'quit' to end the conversation.")
    print("Example: Create an Ubuntu autoinstall ISO with hostname 'my-server', username 'admin', and password 'secure123'")
    print("=" * 50)

    while True:
        user_input = input("\nYou: ")
        if user_input.lower() in ["exit", "quit"]:
            break

        await Console(
            agent.run_stream(
                task=user_input,
                cancellation_token=CancellationToken()
            )
        )

async def direct_mode(args):
    """
    Run the agent in direct mode, creating an ISO with the specified parameters.

    Args:
        args: Command-line arguments
    """
    # Parse SSH keys
    ssh_keys = None
    if args.ssh_keys:
        ssh_keys = [key.strip() for key in args.ssh_keys.split(",")]

    # Parse packages
    packages = None
    if args.packages:
        packages = [pkg.strip() for pkg in args.packages.split(",")]

    # Parse DNS servers
    dns_servers = None
    if args.dns_servers:
        dns_servers = [dns.strip() for dns in args.dns_servers.split(",")]

    # Set output directory
    output_dir = args.output_dir if args.output_dir else os.getcwd()

    # Create ISO with all-in-one function
    # Create the agent
    agent = await create_agent()

    # Construct task description with all parameters
    task_params = {
        "ubuntu_codename": args.ubuntu_codename,
        "username": args.username,
        "password": args.password,
        "hostname": args.hostname,
        "ssh_keys": ssh_keys,
        "packages": packages,
        "network_config": args.network_config,
        "output_iso": args.output_iso,
        "output_dir": output_dir
    }

    # Add static network parameters if applicable
    if args.network_config == "static":
        task_params.update({
            "ip_address": args.ip_address,
            "netmask": args.netmask,
            "gateway": args.gateway,
            "dns_servers": dns_servers,
            "mac_address": args.mac_address
        })

    # Create task description
    method = "all-in-one" if args.all_in_one else "standard"
    task_description = f"Create an Ubuntu {args.ubuntu_codename} autoinstall ISO using the {method} method with the following parameters: {task_params}"

    print(f"Running agent to create Ubuntu {args.ubuntu_codename} autoinstall ISO...")


    # Run the agent with the task
    result = await agent.run(
        task=task_description,
        cancellation_token=CancellationToken()
    )

    # The agent will handle the ISO creation internally based on the task description
    print(f"Ubuntu autoinstall ISO creation task completed.")

    # Print the result from the agent if available
    if result and hasattr(result, 'messages') and result.messages:
        last_message = result.messages[-1]
        if hasattr(last_message, 'content'):
            print(f"\nAgent result: {last_message.content}")

def main():
    """
    Main function to parse command-line arguments and run the appropriate mode.
    """
    parser = argparse.ArgumentParser(description="Ubuntu Autoinstall ISO Generator")
    subparsers = parser.add_subparsers(dest="mode", help="Mode of operation")

    # Interactive mode (default)
    subparsers.add_parser("interactive", help="Interactive mode with the agent")

    # Direct mode
    direct_parser = subparsers.add_parser("direct", help="Create an autoinstall ISO directly")
    direct_parser.add_argument("--ubuntu-codename", default="noble", help="Ubuntu codename (e.g., 'noble', 'jammy')")
    direct_parser.add_argument("--hostname", default="projectx-vm", help="Hostname for the new system")
    direct_parser.add_argument("--username", default="mystic", help="Username for the primary user")
    direct_parser.add_argument("--password", default="mystic", help="Password for the primary user")
    direct_parser.add_argument("--ssh-keys", help="Comma-separated list of SSH public keys")
    direct_parser.add_argument("--packages", help="Comma-separated list of packages to install")
    direct_parser.add_argument("--network-config", default="dhcp", choices=["dhcp", "static"], help="Network configuration type")
    direct_parser.add_argument("--ip-address", help="Static IP address (required if network-config is 'static')")
    direct_parser.add_argument("--netmask", help="Network mask in CIDR format (required if network-config is 'static')")
    direct_parser.add_argument("--gateway", help="Default gateway (required if network-config is 'static')")
    direct_parser.add_argument("--dns-servers", help="Comma-separated list of DNS servers (required if network-config is 'static')")
    direct_parser.add_argument("--mac-address", help="MAC address for network interface (optional for static config)")
    direct_parser.add_argument("--output-dir", help="Directory to save the configuration files and ISO")
    direct_parser.add_argument("--output-iso", default="ubuntu-autoinstall.iso", help="Filename for the output ISO")
    direct_parser.add_argument("--all-in-one", action="store_true", help="Use the all-in-one function to create everything in one step")

    args = parser.parse_args()

    # Default to interactive mode if no mode specified
    if not args.mode:
        args.mode = "interactive"

    # Validate static network configuration
    if args.mode == "direct" and args.network_config == "static":
        if not all([args.ip_address, args.netmask, args.gateway, args.dns_servers]):
            parser.error("Static network configuration requires --ip-address, --netmask, --gateway, and --dns-servers")

    # Run the selected mode
    if args.mode == "interactive":
        asyncio.run(interactive_mode())
    elif args.mode == "direct":
        asyncio.run(direct_mode(args))

if __name__ == "__main__":
    main()
