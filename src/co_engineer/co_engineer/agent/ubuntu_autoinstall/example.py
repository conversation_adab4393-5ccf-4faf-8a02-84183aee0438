"""
Example usage of the Ubuntu Autoinstall ISO Generator agent.
"""

import os
import asyncio
import logging
from typing import Dict, List, Optional

from autogen_core import CancellationToken

from co_engineer.agent.ubuntu_autoinstall.agent import (
    create_agent,
    create_user_data_file,
    create_ubuntu_autoinstall_iso,
    create_ubuntu_autoinstall_all_in_one
)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def example_with_agent():
    """
    Example using the agent to create an Ubuntu autoinstall ISO.
    """
    # Create the agent
    agent = await create_agent()
    
    # Define the task for the agent
    task = """
    Create an Ubuntu autoinstall ISO with the following specifications:
    - Ubuntu codename: noble
    - Hostname: ubuntu-server
    - Username: admin
    - Password: password123
    - Install the following packages: vim, git, curl
    - Use DHCP for network configuration
    - Save the ISO to the current directory as 'my-ubuntu-autoinstall.iso'
    """
    
    # Run the agent
    print("Running the Ubuntu Autoinstall ISO Generator agent...")
    result = await agent.run(task=task, cancellation_token=CancellationToken())
    
    print("\nAgent execution completed.")
    print(f"Result: {result}")

async def example_with_direct_functions():
    """
    Example using the direct functions to create an Ubuntu autoinstall ISO.
    """
    print("Creating Ubuntu autoinstall ISO using direct functions...")
    
    # Step 1: Create user-data and meta-data files
    print("\nStep 1: Creating user-data and meta-data files...")
    config_files = await create_user_data_file(
        ubuntu_codename="noble",
        username="admin",
        password="password123",
        hostname="ubuntu-server",
        packages=["vim", "git", "curl"],
        network_config="dhcp",
        output_dir=os.getcwd()
    )
    
    print(f"User data file created at: {config_files['user_data']}")
    print(f"Meta data file created at: {config_files['meta_data']}")
    
    # Step 2: Create the ISO
    print("\nStep 2: Creating the ISO...")
    iso_path = await create_ubuntu_autoinstall_iso(
        user_data_path=config_files["user_data"],
        meta_data_path=config_files["meta_data"],
        ubuntu_codename="noble",
        output_iso="my-ubuntu-autoinstall-direct.iso",
        output_dir=os.getcwd()
    )
    
    print(f"Ubuntu autoinstall ISO created at: {iso_path}")

async def example_with_all_in_one():
    """
    Example using the all-in-one function to create an Ubuntu autoinstall ISO.
    """
    print("Creating Ubuntu autoinstall ISO using all-in-one function...")
    
    result = await create_ubuntu_autoinstall_all_in_one(
        ubuntu_codename="noble",
        username="admin",
        password="password123",
        hostname="ubuntu-server",
        packages=["vim", "git", "curl"],
        network_config="dhcp",
        output_iso="my-ubuntu-autoinstall-all-in-one.iso",
        output_dir=os.getcwd()
    )
    
    print(f"User data file: {result['user_data']}")
    print(f"Meta data file: {result['meta_data']}")
    print(f"Ubuntu autoinstall ISO: {result['iso_path']}")

async def example_with_static_network():
    """
    Example creating an Ubuntu autoinstall ISO with static network configuration.
    """
    print("Creating Ubuntu autoinstall ISO with static network configuration...")
    
    result = await create_ubuntu_autoinstall_all_in_one(
        ubuntu_codename="noble",
        username="admin",
        password="password123",
        hostname="ubuntu-server",
        packages=["vim", "git", "curl"],
        network_config="static",
        ip_address="*************",
        netmask="24",
        gateway="***********",
        dns_servers=["*******", "*******"],
        mac_address="52:54:00:12:34:56",  # Optional
        output_iso="my-ubuntu-autoinstall-static.iso",
        output_dir=os.getcwd()
    )
    
    print(f"User data file: {result['user_data']}")
    print(f"Meta data file: {result['meta_data']}")
    print(f"Ubuntu autoinstall ISO: {result['iso_path']}")

async def main():
    """
    Main function to run the examples.
    """
    # Uncomment the example you want to run
    await example_with_agent()
    # await example_with_direct_functions()
    # await example_with_all_in_one()
    # await example_with_static_network()

if __name__ == "__main__":
    asyncio.run(main())
