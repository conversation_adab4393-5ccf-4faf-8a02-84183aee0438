#!/usr/bin/env python3
"""
Ubuntu Autoinstall A2A Server - Creates an A2A server for the Ubuntu Autoinstall agent.

This script creates an A2A server that allows users to create customized Ubuntu autoinstall ISOs
through the Agent-to-Agent (A2A) protocol.
"""

from typing import Union, Any, AsyncIterable, Dict
import click
import logging

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.messages import TextMessage
from autogen_core import CancellationToken

from co_engineer.agent.a2a.common.server import A2AServer
from co_engineer.agent.a2a.common.types import Agent<PERSON>ard, AgentCapabilities, AgentSkill
from co_engineer.agent.a2a.common.server.task_manager import InMemoryTaskManager
from co_engineer.agent.a2a.common.types import (
    SendTaskRequest,
    TaskSendParams,
    Message,
    TaskStatus,
    Artifact,
    TextPart,
    TaskState,
    Task,
    SendTaskResponse,
    JSONRPCResponse,
    SendTaskStreamingRequest,
    SendTaskStreamingResponse,
)
import co_engineer.agent.a2a.common.server.utils as utils

from co_engineer.agent.ubuntu_autoinstall.agent import create_agent

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class UbuntuAutoinstallAgent:
    """
    Ubuntu Autoinstall Agent - A tool that creates customized Ubuntu autoinstall ISOs.

    This class creates an agent that helps users create customized Ubuntu autoinstall ISOs
    with various configuration options.
    """
    SUPPORTED_CONTENT_TYPES = ["text", "text/plain"]

    def __init__(self):
        self.agent = None  # Will be initialized in invoke

    async def _build_agent(self) -> AssistantAgent:
        """
        Create an agent that can create Ubuntu autoinstall ISOs.

        Returns:
            An AssistantAgent configured with the necessary tools
        """
        return await create_agent()

    async def invoke(self, task_description: str) -> str:
        """
        Create a customized Ubuntu autoinstall ISO based on the task description.

        Args:
            task_description: The task description containing ISO configuration parameters

        Returns:
            A string containing the result of the ISO creation process
        """
        # Initialize the agent if not already done
        if self.agent is None:
            self.agent = await self._build_agent()

        # Run the agent to create the ISO
        result = await self.agent.run(
            task=task_description,
            cancellation_token=CancellationToken()
        )

        # Extract the result from the last message
        if result and result.messages:
            last_message = result.messages[-1]
            if isinstance(last_message, TextMessage):
                return last_message.content

        return "Failed to create Ubuntu autoinstall ISO."

    async def stream(self, _: str) -> AsyncIterable[Dict[str, Any]]:
        """Streaming is not supported by AutoGen."""
        raise NotImplementedError("Streaming is not supported by AutoGen.")


class AgentTaskManager(InMemoryTaskManager):

    def __init__(self):
        super().__init__()

    async def _stream_generator(
        self, _: SendTaskRequest
    ) -> AsyncIterable[SendTaskResponse]:
        raise NotImplementedError("Not implemented")

    def _validate_request(
        self, request: Union[SendTaskRequest]
    ) -> None:
        task_send_params: TaskSendParams = request.params
        if not utils.are_modalities_compatible(
            task_send_params.acceptedOutputModes, UbuntuAutoinstallAgent.SUPPORTED_CONTENT_TYPES
        ):
            logger.warning(
                "Unsupported output mode. Received %s, Support %s",
                task_send_params.acceptedOutputModes,
                UbuntuAutoinstallAgent.SUPPORTED_CONTENT_TYPES,
            )
            return utils.new_incompatible_types_error(request.id)

    async def on_send_task(
        self, request: SendTaskRequest
    ) -> SendTaskResponse | AsyncIterable[SendTaskResponse]:
        ## only support text output at the moment
        if not utils.are_modalities_compatible(
            request.params.acceptedOutputModes,
            UbuntuAutoinstallAgent.SUPPORTED_CONTENT_TYPES,
        ):
            logger.warning(
                "Unsupported output mode. Received %s, Support %s",
                request.params.acceptedOutputModes,
                UbuntuAutoinstallAgent.SUPPORTED_CONTENT_TYPES,
            )
            return utils.new_incompatible_types_error(request.id)

        task_send_params: TaskSendParams = request.params
        await self.upsert_task(task_send_params)

        return await self._invoke(request)

    async def on_send_task_subscribe(
        self, request: SendTaskStreamingRequest
    ) -> AsyncIterable[SendTaskStreamingResponse] | JSONRPCResponse:
        error = self._validate_request(request)
        if error:
            return error
        await self.upsert_task(request.params)
        return self._stream_generator(request)

    async def _update_store(
        self, task_id: str, status: TaskStatus, artifacts: list[Artifact]
    ) -> Task:
        async with self.lock:
            try:
                task = self.tasks[task_id]
            except KeyError:
                logger.error(f"Task {task_id} not found for updating the task")
                raise ValueError(f"Task {task_id} not found")
            task.status = status
            if artifacts is not None:
                if task.artifacts is None:
                    task.artifacts = []
                task.artifacts.extend(artifacts)
            return task

    async def _invoke(self, request: SendTaskRequest) -> SendTaskResponse:
        task_send_params: TaskSendParams = request.params
        query = self._get_user_query(task_send_params)
        try:
            agent = UbuntuAutoinstallAgent()
            result = await agent.invoke(query)
            logger.info(f"invoke result: {result}")
        except Exception as e:
            logger.error(f"Error invoking agent: {e}")
            raise ValueError(f"Error invoking agent: {e}")
        parts = [{"type": "text", "text": result}]
        task_state = TaskState.INPUT_REQUIRED if "MISSING_INFO:" in result else TaskState.COMPLETED
        task = await self._update_store(
            task_send_params.id,
            TaskStatus(
                state=task_state, message=Message(role="agent", parts=parts)
            ),
            [Artifact(parts=parts)],
        )
        return SendTaskResponse(id=request.id, result=task)

    def _get_user_query(self, task_send_params: TaskSendParams) -> str:
        part = task_send_params.message.parts[0]
        if not isinstance(part, TextPart):
            raise ValueError("Only text parts are supported")
        return part.text


@click.command()
@click.option("--host", "host", default="localhost")
@click.option("--port", "port", default=10001)
def main(host, port):

    """Starts the Ubuntu Autoinstall ISO Generator A2A server."""
    try:
        capabilities = AgentCapabilities(streaming=False, pushNotifications=False)
        skill = AgentSkill(
            id="UbuntuAutoinstall",
            name="Ubuntu Autoinstall ISO Generator",
            description="Creates customized Ubuntu autoinstall ISOs",
            tags=["Ubuntu", "ISO", "Autoinstall", "VM"],
            examples=[
                "Create an Ubuntu autoinstall ISO with hostname 'my-server', username 'admin', and password 'secure123'",
                "Generate an Ubuntu noble autoinstall ISO with static IP configuration",
                "Create an Ubuntu autoinstall ISO with SSH keys and additional packages"
            ],
        )
        agent_card = AgentCard(
            name="Ubuntu Autoinstall ISO Generator",
            description="Creates customized Ubuntu autoinstall ISOs for automated VM installations",
            url=f"http://{host}:{port}/",
            version="1.0.0",
            defaultInputModes=UbuntuAutoinstallAgent.SUPPORTED_CONTENT_TYPES,
            defaultOutputModes=UbuntuAutoinstallAgent.SUPPORTED_CONTENT_TYPES,
            capabilities=capabilities,
            skills=[skill],
        )

        server = A2AServer(
            agent_card=agent_card,
            task_manager=AgentTaskManager(),
            host=host,
            port=port,
        )

        logger.info(f"Starting Ubuntu Autoinstall A2A server on {host}:{port}")
        server.start()
    except Exception as e:
        logger.error(f"An error occurred during server startup: {e}")
        exit(1)

if __name__ == "__main__":
    main()