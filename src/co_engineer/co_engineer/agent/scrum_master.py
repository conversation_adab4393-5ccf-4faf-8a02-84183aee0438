
from autogen_agentchat.agents import Assistant<PERSON><PERSON>
from co_engineer.models import model_client_deepseek_r1


system_prompt = 

_developer_agent = AssistantAgent(
    name="developer_agent",
    model_client=model_client_deepseek_r1,
    system_message=system_prompt,
    # tools=[run_cli, read_file, write_file],
    # ext_tools=[tool_run_cli, tool_read_file, tool_write_file],
    reflect_on_tool_use=True,
    model_client_stream=True,
)