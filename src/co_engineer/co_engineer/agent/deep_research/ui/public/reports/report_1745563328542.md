# **Comprehensive Guide: Creating an Ubuntu Autoinstall ISO**  

This report provides a detailed, step-by-step methodology for generating an **Ubuntu autoinstall ISO** (formerly known as "preseed" but now using **cloud-init** and **Subiquity** for Ubuntu 20.04+).  

---

## **1. Understanding Ubuntu Autoinstall (22.04+)**  
Autoinstall replaces the legacy `preseed` method and relies on:  
- **Subiquity** (Ubuntu’s modern installer)  
- **cloud-init** (for automated provisioning)  
- **YAML-based configuration** (`user-data` and `meta-data`)  

### **Key Differences from Preseed**  
✅ **Structured YAML** (easier validation)  
✅ **Tighter cloud-init integration** (works for cloud and bare metal)  
✅ **Supports late-stage customization** (via `autoinstall` directives)  

---

## **2. Prerequisites**  
- **Ubuntu 22.04/24.04 ISO** (Desktop or Server)  
- **`cloud-localds`** (for cloud-init data injection)  
- **`xorriso`** (for ISO modification)  
- **A Linux environment** (Ubuntu recommended)  

Install dependencies:  
```bash
sudo apt update && sudo apt install -y cloud-image-utils xorriso isolinux
```

---

## **3. Step-by-Step ISO Creation**  

### **A. Extract the Original ISO**  
```bash
mkdir -p ~/autoinstall-iso/iso-extract  
sudo mount -o loop ubuntu-22.04-live-server-amd64.iso ~/autoinstall-iso/iso-extract  
rsync -av ~/autoinstall-iso/iso-extract/ ~/autoinstall-iso/new-iso/  
sudo umount ~/autoinstall-iso/iso-extract  
```

### **B. Create `user-data` (Autoinstall Config)**  
Save as `~/autoinstall-iso/user-data`:  
```yaml
#cloud-config
autoinstall:
  version: 1
  identity:
    hostname: ubuntu-auto
    username: admin
    password: "$6$<hashed-password>"
  ssh:
    install-server: true
    allow-pw: true
  storage:
    layout:
      name: lvm
  late-commands:
    - echo "Installation complete!" > /target/etc/motd
```
> **Note:** Generate a hashed password with:  
> `openssl passwd -6 "yourpassword"`  

### **C. Create `meta-data` (Optional)**  
Save as `~/autoinstall-iso/meta-data`:  
```yaml
instance-id: ubuntu-auto
local-hostname: ubuntu-auto
```

### **D. Inject Cloud-Init into ISO**  
```bash
cd ~/autoinstall-iso  
cloud-localds user-data.img user-data meta-data  
mv user-data.img new-iso/  
```

### **E. Modify Boot Parameters**  
Edit `new-iso/boot/grub/grub.cfg` and append:  
```text
autoinstall ds=nocloud-net;s=/cdrom/
```
(For **legacy BIOS**, also edit `isolinux/txt.cfg`.)  

### **F. Repack the ISO**  
```bash
xorriso -as mkisofs -r -V "Ubuntu Autoinstall" \
  -o ~/autoinstall-ubuntu.iso \
  -J -l -b isolinux/isolinux.bin -c isolinux/boot.cat \
  -no-emul-boot -boot-load-size 4 -boot-info-table \
  -eltorito-alt-boot -e boot/grub/efi.img -no-emul-boot \
  -isohybrid-gpt-basdat \
  ~/autoinstall-iso/new-iso
```

---

## **4. Advanced Customizations**  

### **A. Kernel Arguments**  
Add to `grub.cfg`:  
```text
console=ttyS0,115200n8  # For serial console  
autoinstall ds=nocloud-net;s=/cdrom/ quiet
```

### **B. Post-Install Scripts**  
Use `late-commands` to run scripts:  
```yaml
late-commands:
  - curtin in-target --target /target -- bash -c "apt install -y docker.io"
```

### **C. Network Configuration**  
```yaml
network:
  version: 2
  ethernets:
    eth0:
      dhcp4: true
```

---

## **5. Testing & Deployment**  
- **QEMU Test**:  
  ```bash
  qemu-system-x86_64 -m 4G -cdrom ~/autoinstall-ubuntu.iso -boot d
  ```  
- **USB Boot**:  
  ```bash
  sudo dd if=~/autoinstall-ubuntu.iso of=/dev/sdX bs=4M status=progress
  ```  

---

## **6. Troubleshooting**  
| **Issue** | **Solution** |  
|-----------|-------------|  
| Installer ignores `user-data` | Ensure `ds=nocloud-net;s=/cdrom/` is in boot args |  
| Password not working | Regenerate hash with `openssl passwd -6` |  
| Network not configured | Verify `network:` in `user-data` |  

---

## **7. Alternative Methods**  
### **A. Using `autoinstall-generator` (Ubuntu Pro)**
```bash
sudo apt install autoinstall-generator
autoinstall-generator --output ~/autoinstall-config.yaml
```
### **B. Packer (Infrastructure-as-Code)**
```hcl
source "qemu" "ubuntu-auto" {
  iso_url          = "ubuntu-22.04-live-server-amd64.iso"
  iso_checksum     = "sha256:..."
  shutdown_command = "echo 'autoinstall' | sudo -S shutdown -P now"
  boot_command = [
    "<esc><wait>autoinstall ds=nocloud-net;s=/cdrom/ quiet<enter>"
  ]
}
```

---

## **8. Future Considerations**  
- **Debian’s `preseed` vs. Ubuntu `autoinstall`** (if cross-compatibility needed)  
- **Secure Boot & TPM Integration** (for fully automated encrypted installs)  
- **GitOps for Autoinstall** (store configs in Git, deploy via PXE)  

---

### **Final Notes**  
This method works for **Ubuntu 22.04+**. For older versions, consider `preseed`.  
Let me know if you need **custom kernel args, RAID setups, or multi-disk partitioning**.  

Would you like a **PXE boot** or **VMware/vSphere** deployment variant?