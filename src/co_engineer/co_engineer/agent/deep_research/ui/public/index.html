<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>POC Research</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Left sidebar for research progress -->
            <div class="col-md-4 sidebar">
                <div class="sidebar-header">
                    <h2>POC Research</h2>
                    <p class="text-muted">Multi-Agent Research System</p>
                </div>

                <div class="input-section">
                    <form id="research-form">
                        <div class="mb-3">
                            <label for="topic" class="form-label">Research Topic</label>
                            <textarea id="topic" class="form-control" rows="3" placeholder="Enter your research topic here..."></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="depth" class="form-label">Depth</label>
                                    <select id="depth" class="form-select">
                                        <option value="1">1 - Quick</option>
                                        <option value="2" selected>2 - Standard</option>
                                        <option value="3">3 - Deep</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="breadth" class="form-label">Breadth</label>
                                    <select id="breadth" class="form-select">
                                        <option value="2">2 - Focused</option>
                                        <option value="3" selected>3 - Balanced</option>
                                        <option value="4">4 - Broad</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary w-100" id="start-research">Start Research</button>
                    </form>
                </div>

                <div class="progress-section mt-4">
                    <h4>Research Progress</h4>
                    <div id="progress-container">
                        <div class="progress-status">Waiting to start...</div>
                    </div>
                </div>

                <div id="feedback-section" class="feedback-section mt-4" style="display: none;">
                    <h4>Adjust Research Results</h4>
                    <p class="text-muted">Provide feedback to refine the research results</p>
                    <form id="feedback-form">
                        <div class="mb-3">
                            <textarea id="feedback-input" class="form-control" rows="3" placeholder="Suggest adjustments to the research results..."></textarea>
                        </div>
                        <div class="d-flex gap-2">
                            <button type="submit" id="submit-feedback" class="btn btn-primary flex-grow-1">Apply Feedback</button>
                            <button type="button" id="add-doc-btn" class="btn btn-outline-secondary" title="Upload a markdown document to enhance the research">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-file-earmark-text me-1" viewBox="0 0 16 16">
                                    <path d="M5.5 7a.5.5 0 0 0 0 1h5a.5.5 0 0 0 0-1h-5zM5 9.5a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5zm0 2a.5.5 0 0 1 .5-.5h2a.5.5 0 0 1 0 1h-2a.5.5 0 0 1-.5-.5z"/>
                                    <path d="M9.5 0H4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V4.5L9.5 0zm0 1v2A1.5 1.5 0 0 0 11 4.5h2V14a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h5.5z"/>
                                </svg>
                                Add Doc
                            </button>
                        </div>
                        <input type="file" id="doc-upload" accept=".md" style="display: none;">
                    </form>
                </div>
            </div>

            <!-- Main content area for report -->
            <div class="col-md-8 main-content">
                <div class="report-header">
                    <h3>Research Report</h3>
                    <div class="report-actions">
                        <button id="edit-report" class="btn btn-sm btn-outline-primary" disabled>Edit</button>
                        <button id="copy-report" class="btn btn-sm btn-outline-secondary" disabled>Copy</button>
                        <button id="download-report" class="btn btn-sm btn-outline-secondary" disabled>Download</button>
                    </div>
                </div>
                <div id="report-container">
                    <div class="placeholder-message">
                        <div class="text-center">
                            <img src="https://cdn.jsdelivr.net/gh/twitter/twemoji@14.0.2/assets/svg/1f50d.svg" alt="Research" width="64" height="64">
                            <h4 class="mt-3">Start a new research</h4>
                            <p class="text-muted">Enter a topic and click "Start Research" to begin.</p>
                        </div>
                    </div>
                </div>
                <div id="editor-container" style="display: none;">
                    <div class="editor-header">
                        <div class="editor-title">Editing Report</div>
                        <button id="done-editing" class="btn btn-success">Done</button>
                    </div>
                    <textarea id="report-editor" class="form-control code-editor" rows="20"></textarea>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked@4.0.0/marked.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>

    <!-- Load highlight.js with common languages (with fallback) -->
    <script>
        // Function to load highlight.js from different CDNs with fallback
        function loadHighlightJS() {
            // Try to load from primary CDN (cdnjs)
            var script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js';
            script.onerror = function() {
                console.warn('Primary CDN failed, trying fallback...');
                // Try to load from fallback CDN (jsdelivr)
                var fallbackScript = document.createElement('script');
                fallbackScript.src = 'https://cdn.jsdelivr.net/npm/highlight.js@11.7.0/highlight.min.js';
                fallbackScript.onerror = function() {
                    console.warn('Fallback CDN also failed. Code highlighting will be disabled.');
                };
                fallbackScript.onload = initHighlightJS;
                document.head.appendChild(fallbackScript);
            };
            script.onload = initHighlightJS;
            document.head.appendChild(script);
        }

        // Function to initialize highlight.js once loaded
        function initHighlightJS() {
            if (typeof hljs !== 'undefined') {
                console.log('highlight.js loaded successfully.');
                // Initialize highlight.js
                hljs.configure({
                    ignoreUnescapedHTML: true
                });

                // Add CSS for highlight.js
                var link = document.createElement('link');
                link.rel = 'stylesheet';
                link.href = 'https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/github.min.css';
                document.head.appendChild(link);
            } else {
                console.warn('highlight.js failed to load despite successful script loading.');
            }
        }

        // Start loading process
        loadHighlightJS();
    </script>

    <script src="/socket.io/socket.io.js"></script>
    <script src="script.js"></script>
</body>
</html>
