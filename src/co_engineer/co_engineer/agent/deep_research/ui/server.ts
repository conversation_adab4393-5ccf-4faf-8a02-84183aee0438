import express, { Request, Response } from 'express';
import http from 'http';
import { Server as SocketIOServer } from 'socket.io';
import path from 'path';
import multer from 'multer';
import fs from 'fs';

import { deepResearch, selectAgentAndGenerateReport } from '../main';
import 'dotenv/config';

// Define a custom interface for Request with file
interface MulterRequest extends Request {
  file?: Express.Multer.File;
}

interface EventEmitter {
  emit(event: string, data: any): void;
}

const app = express();
const server = http.createServer(app);
const io = new SocketIOServer(server);

// Set up multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (_req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
    // Accept only markdown files
    if (file.mimetype === 'text/markdown' || file.originalname.endsWith('.md')) {
      cb(null, true);
    } else {
      cb(null, false);
    }
  }
});

app.use(express.static(path.join(__dirname, 'public')));
app.use(express.json());

io.on('connection', (socket) => {
  console.log('Client connected');

  socket.on('startResearch', async (data) => {
    const { topic, depth, breadth } = data;

    console.log('=== New Research Request ===');
    console.log('Topic:', topic);
    console.log('Depth:', depth || 2);
    console.log('Breadth:', breadth || 3);
    console.log('Time:', new Date().toISOString());
    console.log('============================');

    try {
      console.log('Creating event emitter for progress tracking...');
      const eventEmitter: EventEmitter = {
        emit: (event: string, data: any) => {
          console.log(`Event emitted: ${event}`, data);
          if (event === 'searchStarted') {
            socket.emit('searchStarted', { query: data.query });
          } else if (event === 'searchCompleted') {
            socket.emit('searchCompleted', {
              query: data.query,
              url: data.url,
              relevant: data.relevant
            });
          } else if (event === 'learningExtracted') {
            socket.emit('learningExtracted', {
              learning: data.learning,
              followUpQuestions: data.followUpQuestions
            });
          }
        }
      };

      console.log('Sending researchStarted event to client...');
      socket.emit('researchStarted', { topic });

      console.log('Starting deepResearch with parameters:', {
        topic,
        depth: depth || 2,
        breadth: breadth || 3
      });
      console.log('Research start time:', new Date().toISOString());

      const research = await deepResearch(
        topic,
        depth || 2,
        breadth || 3,
        eventEmitter
      );

      console.log('Research completed successfully');
      console.log('Research end time:', new Date().toISOString());

      console.log('Generating report...');
      socket.emit('generatingReport', {});

      try {
        console.log('Selecting appropriate agent based on query type...');
        console.log('Research data summary:', {
          query: research.query,
          queriesCount: research.queries.length,
          searchResultsCount: research.searchResults.length,
          learningsCount: research.learnings.length
        });

        // Use the agent selector to choose between step-by-step guide and general report
        const report = await selectAgentAndGenerateReport(research);
        console.log('Report generated successfully, length:', report.length);

        // Generate a filename for download purposes only (not saving to disk)
        const filename = `deep_research_report_${Date.now()}.md`;

        console.log('Sending researchCompleted event to client...');
        socket.emit('researchCompleted', {
          report,
          filename: filename,
          research: research // Include the research data for potential feedback
        });
        console.log('Research process completed successfully.');
      } catch (error) {
        console.error('Error in report generation or saving:', error);
        socket.emit('researchError', {
          error: error instanceof Error ? error.message : 'An error occurred during report generation'
        });
      }

    } catch (error) {
      console.error('Research error:', error);
      socket.emit('researchError', {
        error: error instanceof Error ? error.message : 'An error occurred during research'
      });
    }
  });

  socket.on('disconnect', () => {
    console.log('Client disconnected');
  });

  // Handle user feedback to adjust research results
  socket.on('applyFeedback', async (data) => {
    const { feedback, research } = data;

    console.log('=== Received Feedback Request ===');
    console.log('Feedback:', feedback);
    console.log('Time:', new Date().toISOString());
    console.log('============================');

    try {
      if (!research) {
        throw new Error('No research data provided with feedback');
      }

      // Add the user feedback to the research data
      const enhancedResearch = {
        ...research,
        userFeedback: feedback
      };

      console.log('Generating adjusted report based on feedback...');
      socket.emit('generatingReport', {});

      // Generate a new report with the feedback incorporated, using the appropriate agent
      const adjustedReport = await selectAgentAndGenerateReport(enhancedResearch);

      console.log('Adjusted report generated successfully, length:', adjustedReport.length);

      // Send the adjusted report back to the client
      socket.emit('feedbackApplied', {
        report: adjustedReport
      });

      console.log('Feedback application completed successfully.');

    } catch (error) {
      console.error('Error applying feedback:', error);
      socket.emit('researchError', {
        error: error instanceof Error ? error.message : 'An error occurred while applying feedback'
      });
    }
  });

  // Handle document uploads for enhancing research
  socket.on('processDocument', async (data) => {
    const { documentContent, documentName, research } = data;

    console.log('=== Received Document Processing Request ===');
    console.log('Document Name:', documentName);
    console.log('Document Length:', documentContent.length, 'characters');
    console.log('Time:', new Date().toISOString());
    console.log('============================');

    try {
      if (!research) {
        throw new Error('No research data provided with document');
      }

      if (!documentContent) {
        throw new Error('No document content provided');
      }

      // Add the document content to the research data
      const enhancedResearch = {
        ...research,
        additionalDocuments: [
          ...(research.additionalDocuments || []),
          {
            name: documentName,
            content: documentContent,
            timestamp: new Date().toISOString()
          }
        ]
      };

      console.log('Generating adjusted report with document content...');
      socket.emit('generatingReport', {});

      // Generate a new report with the document incorporated, using the appropriate agent
      const adjustedReport = await selectAgentAndGenerateReport(enhancedResearch);

      console.log('Adjusted report generated successfully, length:', adjustedReport.length);

      // Send the adjusted report back to the client
      socket.emit('documentProcessed', {
        report: adjustedReport,
        documentName
      });

      console.log('Document processing completed successfully.');

    } catch (error) {
      console.error('Error processing document:', error);
      socket.emit('researchError', {
        error: error instanceof Error ? error.message : 'An error occurred while processing the document'
      });
    }
  });
});

const currentDir = process.cwd();

app.get('/', (_, res) => {
  res.sendFile(path.join(currentDir, 'public', 'index.html'));
});

// Handle document uploads
app.post('/upload-document', upload.single('document'), async (req: MulterRequest, res: Response) => {
  if (!req.file) {
    return res.status(400).json({ error: 'No document uploaded or invalid file type' });
  }

  try {
    console.log('=== Document Upload Request ===');
    console.log('Filename:', req.file.originalname);
    console.log('Size:', req.file.size, 'bytes');
    console.log('Time:', new Date().toISOString());
    console.log('============================');

    // Convert buffer to string
    const documentContent = req.file.buffer.toString('utf-8');

    // Send the document content back to the client
    res.status(200).json({
      success: true,
      filename: req.file.originalname,
      content: documentContent
    });
  } catch (error) {
    console.error('Error processing uploaded document:', error);
    res.status(500).json({
      error: error instanceof Error ? error.message : 'An error occurred while processing the document'
    });
  }
});

const PORT = process.env.PORT || 3000;
const HOST = process.env.HOST || '0.0.0.0';
server.listen(PORT, () => {
  console.log('\n=== Deep Research - Project X Server Started ===');
  console.log(`Server running on port: ${PORT}`);
  console.log(`URL: http://${HOST}:${PORT}`);
  console.log(`Time: ${new Date().toISOString()}`);
  console.log(`Node.js version: ${process.version}`);
  console.log(`Working directory: ${process.cwd()}`);
  console.log('=======================================\n');
  console.log('Waiting for client connections...');
});
