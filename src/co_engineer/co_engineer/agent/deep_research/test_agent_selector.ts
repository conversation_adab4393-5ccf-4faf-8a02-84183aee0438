import { deepResearch, selectAgentAndGenerateReport, isHowToQuestion } from './main';
import fs from 'fs';

// Test cases for different types of queries
const testQueries = [
  {
    query: 'How to install Ubuntu on a bare metal server',
    expected: 'how-to',
    description: 'Clear how-to question'
  },
  {
    query: 'What is machine learning and how does it work?',
    expected: 'general',
    description: 'General information question'
  },
  {
    query: 'Steps to configure a Kubernetes cluster',
    expected: 'how-to',
    description: 'Procedural question without "how to"'
  },
  {
    query: 'Compare REST and GraphQL APIs',
    expected: 'general',
    description: 'Comparison question'
  }
];

// Test the isHowToQuestion function
const testIsHowToQuestion = async () => {
  console.log('=== Testing isHowToQuestion function ===');
  
  for (const test of testQueries) {
    console.log(`\nTesting: "${test.query}" (${test.description})`);
    try {
      const result = await isHowToQuestion(test.query);
      const expected = test.expected === 'how-to';
      console.log(`Result: ${result ? 'how-to' : 'general'}`);
      console.log(`Expected: ${expected ? 'how-to' : 'general'}`);
      console.log(`Test ${result === expected ? 'PASSED' : 'FAILED'}`);
    } catch (error) {
      console.error(`Error testing query "${test.query}":`, error);
    }
  }
};

// Test the full research and report generation pipeline
const testFullPipeline = async () => {
  console.log('\n=== Testing full research and report generation pipeline ===');
  
  // Choose one how-to question and one general question for full testing
  const howToQuery = testQueries[0].query;
  const generalQuery = testQueries[1].query;
  
  // Test how-to question
  console.log(`\nTesting full pipeline with how-to question: "${howToQuery}"`);
  try {
    const research = await deepResearch(howToQuery, 1, 1); // Reduced depth and breadth for faster testing
    console.log('Research completed!');
    
    const report = await selectAgentAndGenerateReport(research);
    console.log('Report generated successfully!');
    
    // Save the report to a file
    const filename = 'how_to_report.md';
    fs.writeFileSync(filename, report);
    console.log(`Report saved to ${filename}`);
  } catch (error) {
    console.error('Error in how-to pipeline:', error);
  }
  
  // Test general question
  console.log(`\nTesting full pipeline with general question: "${generalQuery}"`);
  try {
    const research = await deepResearch(generalQuery, 1, 1); // Reduced depth and breadth for faster testing
    console.log('Research completed!');
    
    const report = await selectAgentAndGenerateReport(research);
    console.log('Report generated successfully!');
    
    // Save the report to a file
    const filename = 'general_report.md';
    fs.writeFileSync(filename, report);
    console.log(`Report saved to ${filename}`);
  } catch (error) {
    console.error('Error in general pipeline:', error);
  }
};

// Main function
const main = async () => {
  // Test the isHowToQuestion function
  await testIsHowToQuestion();
  
  // Uncomment to test the full pipeline (takes longer)
  // await testFullPipeline();
  
  console.log('\nAll tests completed!');
};

// Run the tests
main().catch(error => {
  console.error('Error in test script:', error);
});
