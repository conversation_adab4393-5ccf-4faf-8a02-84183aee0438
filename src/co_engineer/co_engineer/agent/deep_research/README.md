# Deep Research Multi-Agent System

A state-of-the-art collaborative multi-agent research system that performs exhaustive web searches and generates expert-level comprehensive reports on any topic. This advanced system orchestrates a team of specialized intelligent agents working in concert, leveraging Google's Gemini AI or OpenAI models, Vercel AI SDK, and the Exa search library to conduct recursive, multi-level research with dynamic follow-up questions.

The system's distributed agent architecture enables unprecedented research depth and breadth, with each specialized agent focusing on a specific aspect of the research process while maintaining shared context. This approach allows for parallel processing, sophisticated reasoning, and the ability to explore complex topics with human-like curiosity and thoroughness.

## How It Works

The multi-agent system coordinates a sophisticated research workflow through specialized agents:

1. **Query Generation**: The query formulation agent generates multiple strategic search queries based on the initial research topic
2. **Web Search**: For each query, the web search agent retrieves high-quality content using the Exa search library
3. **Result Evaluation**: The evaluation agent assesses search results for relevance, authority, and uniqueness
4. **Learning Extraction**: The insight extraction agent identifies key concepts, facts, and implications from relevant sources
5. **Follow-up Questions**: Based on extracted insights, the system generates intelligent follow-up questions to explore knowledge gaps
6. **Recursive Exploration**: The orchestration agent manages recursive exploration of follow-up questions at decreasing depth levels
7. **Report Generation**: Finally, the report generation agent synthesizes all research data into a comprehensive, well-structured report

### Agent Collaboration Flow

The multi-agent system's collaborative research process follows this coordination pattern:

```mermaid
graph TD
    A[main] --> B[deepResearch]
    B --> C[generateSearchQueries]
    B --> D[searchAndProcess]
    D --> E[searchWeb]
    D --> F[evaluate]
    B --> G[generateLearnings]
    B --> B1[deepResearch - recursive]
    A --> H[generateReport]

    C -->|returns queries| B
    E -->|returns search results| D
    F -->|evaluates relevance| D
    D -->|returns relevant results| B
    G -->|extracts learnings & follow-up questions| B
    B -->|accumulated research| A
    H -->|final report| A
```

**Agent Collaboration Description:**

- The **Coordinator** initiates the research process and manages the final report generation
- The **Orchestration Agent** (`deepResearch`) coordinates the entire multi-agent workflow recursively
- The **Query Formulation Agent** (`generateSearchQueries`) creates strategic search queries from the research prompt
- The **Evaluation Agent** (`searchAndProcess`) manages web search execution and assesses result quality
- The **Web Search Agent** (`searchWeb`) interfaces with the Exa library to retrieve high-quality content
- The **Insight Extraction Agent** (`generateLearnings`) identifies key concepts and generates follow-up questions
- The **Report Generation Agent** (`generateReport`) synthesizes all collected research into a cohesive document

## Architecture

The agent is built using:
- **AI Models**:
  - **Google Gemini AI**: Provides language model capabilities (gemini-2.0-flash-001)
  - **OpenAI-compatible API**: Supports both official OpenAI models and compatible alternatives:
    - Default tool model: QwQ-32B (for search and analysis)
    - Default summary model: DeepSeek-V3-0324 (for report generation)
- **Vercel AI SDK**: Provides the framework for AI tool integration and model switching
- **Exa Search Library**: Enables high-quality web search with content extraction
- **TypeScript**: The agent is implemented in TypeScript for type safety and modern JavaScript features

### Data Flow

The following diagram illustrates how data flows through the research process:

```mermaid
flowchart TD
    subgraph Input
        A[Research Topic]
    end

    subgraph Research Process
        B[deepResearch]
        C[generateSearchQueries]
        D[searchAndProcess]
        E[searchWeb]
        F[generateLearnings]
        G[Recursive deepResearch]
    end

    subgraph Data Storage
        H[(accumulatedResearch)]
        H1[queries]
        H2[searchResults]
        H3[learnings]
        H4[completedQueries]
    end

    subgraph Output
        I[generateReport]
        J[Final Markdown Report]
    end

    A --> B
    B --> C
    C --> H1
    H1 --> D
    D --> E
    E --> D
    D --> H2
    H2 --> F
    F --> H3
    H3 --> G
    G --> B
    H --> I
    I --> J

    %% Data relationships
    H -.-> H1
    H -.-> H2
    H -.-> H3
    H -.-> H4
```

This research agent uses a recursive approach to explore topics in depth. The `accumulatedResearch` object serves as a central data store that collects all queries, search results, and learnings throughout the research process. The recursive nature of `deepResearch` allows the agent to follow trails of inquiry based on follow-up questions generated from initial findings.

## Usage

### Command Line Usage

```typescript
// Basic usage
const research = await deepResearch(
  "How to create an OpenShift 4 cluster on bare metal servers using Redfish and agent-based installer"
);

// Generate a report from the research
const report = await generateReport(research);

// Save the report to a file
fs.writeFileSync('report.md', report);
```

### Web UI

The system includes a web-based user interface for easier interaction. The UI provides:

- Form for entering research topics
- Real-time progress tracking
- Markdown rendering of the final report
- Copy and download options for reports

To use the web UI:

1. Navigate to the UI directory:
   ```bash
   cd ui
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Build and start the server:
   ```bash
   npm run build && npm start
   ```

4. Open your browser to `http://localhost:3000`

For more details, see the [UI README](ui/README.md).

### Configuration Options

You can customize the research depth and breadth:

```typescript
// Custom depth and breadth
const research = await deepResearch(
  "Your research topic here",
  3,  // depth - how many levels of follow-up questions to explore
  4   // breadth - how many search queries to generate at each level
);
```

## Environment Setup

1. Create a `.env` file with your API keys and configuration (you can copy from `.env.example`):
   ```
   # Required for web search
   EXA_API_KEY=your_exa_api_key_here

   # For Google models
   GOOGLE_GENERATIVE_AI_API_KEY=<YOUR_API_KEY>

   # For OpenAI-compatible models
   OPENAI_API_BASE_URL=http://***********:4000  # Example URL for OpenAI-compatible API
   OPENAI_API_KEY=dummy                         # API key for OpenAI or compatible service

   # Model selection
   USE_OPENAI=true                              # Set to 'true' to use OpenAI-compatible models, 'false' for Google models

   # OpenAI-compatible model names
   OPENAI_MODEL_TOOL=QwQ-32B                    # Model for search queries and analysis
   OPENAI_MODEL_SUMMARY=DeepSeek-V3-0324        # Model for report generation
   ```

   > **Note**: The example configuration in `.env.example` uses OpenAI-compatible API endpoints with models like QwQ-32B and DeepSeek-V3-0324. You can replace these with actual OpenAI models like `gpt-4o` if using the official OpenAI API.

2. Install dependencies:
   ```bash
   npm install @ai-sdk/google @ai-sdk/openai ai dotenv zod exa-js
   ```

### Model Selection

The agent supports both Google Gemini and OpenAI-compatible models:

- When `USE_OPENAI=false`: Uses Google's `gemini-2.0-flash-001` model for all operations
- When `USE_OPENAI=true` (default in .env.example): Uses OpenAI-compatible models specified by:
  - `OPENAI_MODEL_TOOL`: Model used for search queries and analysis (default in .env.example: `QwQ-32B`)
  - `OPENAI_MODEL_SUMMARY`: Model used for report generation (default in .env.example: `DeepSeek-V3-0324`)

You can switch between model providers by changing the `USE_OPENAI` environment variable, and customize which models to use for different tasks without modifying the code. The agent is designed to work with both official OpenAI models and compatible alternatives through the same interface.

3. Run the agent:
   ```bash
   npm run build && npm run start
   ```
   For example:
   ```bash
   # npm run build &&npm run start

    > ts@1.0.0 build
    > tsc


    > ts@1.0.0 start
    > node dist/main.js

    Searching the web for: Unattended Ubuntu installation bare metal server Redfish
    Found: https://foresterorg.github.io/
    Evaluation completed: relevant
    Processing search result: https://foresterorg.github.io/
    Searching the web for: Forester unattended Ubuntu installation Redfish SecureBoot
    Searching the web for: Redfish automated Ubuntu deployment bare metal
    Found: https://deploy.equinix.com/blog/redfish-and-the-future-of-bare-metal-server-automation/
    Evaluation completed: relevant
    Processing search result: https://deploy.equinix.com/blog/redfish-and-the-future-of-bare-metal-server-automation/
    Searching the web for: Redfish Ansible Terraform Ubuntu bare metal deployment
    Found: https://github.com/nickhardiman/summit_OD1226
    Evaluation completed: relevant
    Processing search result: https://github.com/nickhardiman/summit_OD1226
    Research completed!
    Generating report...
    Report generated! report.md
    ```

## Example Research Topics

The agent has been tested with topics such as:
- "How to create an Ubuntu autoinstall ISO"
- "How to install unattended Ubuntu to a bare metal server using Redfish"
- "How to create an OpenShift 4 cluster on bare metal servers using Redfish and agent-based installer"

## Example Reports

The [`example_reports`](example_reports/) directory contains several comprehensive research reports generated by the deep_research agent. These reports demonstrate the agent's ability to gather information, analyze it, and present it in a structured, detailed format.

### Available Reports

1. **[`report_ubuntu_autoinstall.md`](example_reports/report_ubuntu_autoinstall.md)**
   - **Topic:** Creating a customized Ubuntu autoinstall ISO
   - **Focus:** Detailed procedure for creating automated installation media, desktop environment customization, and troubleshooting
   - **Key Sections:** Core steps, detailed procedures, customizing desktop environments, addressing common issues
   - **Length:** ~250 lines, ~11,750 bytes

2. **[`report_unattended_ubuntu.md`](example_reports/report_unattended_ubuntu.md)**
   - **Topic:** Unattended Ubuntu installation on bare metal servers using Redfish
   - **Focus:** Redfish API usage, automation tools, and autoinstall configuration
   - **Key Sections:** Key components and technologies, unattended installation process, security considerations
   - **Length:** ~190 lines, ~11,680 bytes

3. **[`report_agent_based_installer.md`](example_reports/report_agent_based_installer.md)**
   - **Topic:** Unattended Ubuntu installation on bare metal servers via RedFish API
   - **Focus:** Technical implementation with code examples for RedFish automation
   - **Key Sections:** Step-by-step implementation, RedFish automation workflow, vendor-specific notes
   - **Length:** ~150 lines, ~5,350 bytes

4. **[`report_unattended_ubuntu_deepseek.md`](example_reports/report_unattended_ubuntu_deepseek.md)**
   - **Topic:** Unattended Ubuntu installation on bare metal via Redfish with static IP configuration
   - **Focus:** Comprehensive guide with NMState for network configuration and virtual media
   - **Key Sections:** Detailed steps, troubleshooting, alternatives and considerations
   - **Length:** ~280 lines, ~16,240 bytes

### Using the Example Reports

These reports serve as:
1. **Demonstrations** of the deep_research agent's capabilities
2. **Templates** for how to structure your own research requests
3. **Reference material** for the technical topics they cover

To generate similar reports, run the agent with a specific research topic as shown in the [main.ts](main.ts) file and the Usage section above. You can modify the research topic in the `deepResearch()` function call to generate reports on different subjects.

## Output

The agent generates a comprehensive Markdown report that includes:
- Detailed findings from the research
- Organized sections with key information
- Citations to sources
- Technical details appropriate for expert users
- Potential solutions and approaches
- Contrarian ideas and alternative perspectives

## System Requirements

- Node.js 16+
- TypeScript 4.5+
- Valid Exa API key
- Internet connection for web searches

## Limitations

- The quality of research depends on the availability of relevant web content
- The agent is designed for technical research and may not perform as well for subjective topics
- Web search results may occasionally contain outdated information

## Future Improvements

- Integration with additional search providers
- Support for PDF and academic paper analysis
- Improved source evaluation and fact-checking
- Interactive mode for real-time research guidance

## Code Structure

The deep_research system is composed of several specialized agents, each with a specific responsibility:

| Agent | Description |
|----------|-------------|
| `deepResearch` | Core orchestration agent that manages the recursive research process |
| `generateSearchQueries` | Query formulation agent that creates targeted search queries based on the research topic |
| `searchWeb` | Web search agent that performs searches using the Exa library |
| `searchAndProcess` | Evaluation agent that manages search execution and assesses result relevance |
| `generateLearnings` | Insight extraction agent that identifies key learnings and generates follow-up questions |
| `generateReport` | Report generation agent that synthesizes all collected data into a comprehensive document |

The agent system uses a shared memory structure called `accumulatedResearch` to store and exchange research data between agents:

```typescript
type Research = {
  query: string | undefined       // The original research topic
  queries: string[]               // Generated search queries from the query formulation agent
  searchResults: SearchResult[]   // Relevant results from the web search agent
  learnings: Learning[]           // Insights and questions from the extraction agent
  completedQueries: string[]      // Tracking of processed queries
}
```

This shared memory approach allows the agents to maintain context throughout the recursive research process, build upon each other's work, and collectively generate a comprehensive final report.
