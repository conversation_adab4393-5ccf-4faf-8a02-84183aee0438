/* Deep Research Extension Styles */

body {
  font-family: var(--vscode-font-family);
  background-color: var(--vscode-editor-background);
  color: var(--vscode-editor-foreground);
  padding: 0;
  margin: 0;
}

.container-fluid {
  height: 100vh;
  padding: 0;
}

.row {
  height: 100%;
  margin: 0;
}

/* Sidebar styles */
.sidebar {
  background-color: var(--vscode-sideBar-background);
  border-right: 1px solid var(--vscode-panel-border);
  padding: 20px;
  height: 100vh;
  overflow-y: auto;
}

.sidebar-header {
  padding-bottom: 15px;
  border-bottom: 1px solid var(--vscode-panel-border);
  margin-bottom: 20px;
}

.sidebar-header h2 {
  margin-bottom: 5px;
  color: var(--vscode-sideBarTitle-foreground);
}

.sidebar-header p {
  font-size: 0.85rem;
  line-height: 1.4;
  margin-bottom: 0;
  word-wrap: break-word;
  color: var(--vscode-descriptionForeground);
}

.progress-section {
  margin-top: 30px;
}

.progress-item {
  margin-bottom: 15px;
  padding: 12px;
  border-radius: 6px;
  background-color: var(--vscode-editor-background);
  border-left: 4px solid var(--vscode-button-secondaryBackground);
}

.progress-item.search {
  border-left-color: var(--vscode-button-background);
}

.progress-item.result {
  border-left-color: var(--vscode-testing-iconPassed);
}

.progress-item.learning {
  border-left-color: var(--vscode-notificationsWarningIcon-foreground);
}

.progress-item.error {
  border-left-color: var(--vscode-notificationsErrorIcon-foreground);
}

.progress-item h5 {
  margin-bottom: 5px;
  font-size: 0.9rem;
  font-weight: 600;
}

.progress-item p {
  margin-bottom: 0;
  font-size: 0.85rem;
  color: var(--vscode-descriptionForeground);
}

.progress-item .badge {
  font-size: 0.7rem;
  padding: 3px 6px;
}

/* Main content styles */
.main-content {
  padding: 20px;
  height: 100vh;
  overflow-y: auto;
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--vscode-panel-border);
  margin-bottom: 20px;
}

#report-container {
  background-color: var(--vscode-editor-background);
  border-radius: 6px;
  padding: 25px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  min-height: calc(100vh - 100px);
}

.placeholder-message {
  display: flex;
  height: 100%;
  min-height: 400px;
  align-items: center;
  justify-content: center;
  color: var(--vscode-descriptionForeground);
}

/* Markdown content styling */
.markdown-content {
  line-height: 1.6;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3 {
  margin-top: 1.5em;
  margin-bottom: 0.75em;
  color: var(--vscode-editor-foreground);
}

.markdown-content h1:first-child,
.markdown-content h2:first-child,
.markdown-content h3:first-child {
  margin-top: 0;
}

.markdown-content p {
  margin-bottom: 1em;
  color: var(--vscode-editor-foreground);
}

.markdown-content pre {
  background-color: var(--vscode-textCodeBlock-background);
  padding: 15px;
  border-radius: 6px;
  overflow-x: auto;
}

.markdown-content code {
  font-family: var(--vscode-editor-font-family);
  font-size: 0.9em;
  color: var(--vscode-textPreformat-foreground);
}

.markdown-content blockquote {
  border-left: 4px solid var(--vscode-panel-border);
  padding-left: 15px;
  color: var(--vscode-descriptionForeground);
  margin-left: 0;
}

.markdown-content table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1em;
}

.markdown-content th,
.markdown-content td {
  border: 1px solid var(--vscode-panel-border);
  padding: 8px 12px;
}

.markdown-content th {
  background-color: var(--vscode-editor-background);
}

/* Loading animation */
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(128, 128, 128, 0.3);
  border-radius: 50%;
  border-top-color: var(--vscode-button-background);
  animation: spin 1s ease-in-out infinite;
  margin-right: 10px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Form controls */
.form-control, .form-select {
  background-color: var(--vscode-input-background);
  color: var(--vscode-input-foreground);
  border-color: var(--vscode-input-border);
}

.form-control:focus, .form-select:focus {
  border-color: var(--vscode-focusBorder);
  box-shadow: 0 0 0 0.25rem var(--vscode-focusBorder);
}

/* Buttons */
.btn-primary {
  background-color: var(--vscode-button-background);
  border-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
}

.btn-primary:hover {
  background-color: var(--vscode-button-hoverBackground);
  border-color: var(--vscode-button-hoverBackground);
}

.btn-outline-secondary {
  color: var(--vscode-button-secondaryForeground);
  border-color: var(--vscode-button-secondaryBackground);
}

.btn-outline-secondary:hover {
  background-color: var(--vscode-button-secondaryHoverBackground);
  color: var(--vscode-button-secondaryForeground);
}
