// Deep Research Extension WebView <PERSON>t

(function() {
    const vscode = acquireVsCodeApi();

    // DOM elements
    const researchForm = document.getElementById('research-form');
    const topicInput = document.getElementById('topic');
    const depthSelect = document.getElementById('depth');
    const breadthSelect = document.getElementById('breadth');
    const startResearchBtn = document.getElementById('start-research');
    const progressContainer = document.getElementById('progress-container');
    const reportContainer = document.getElementById('report-container');
    const copyReportBtn = document.getElementById('copy-report');
    const saveReportBtn = document.getElementById('save-report');

    // Current research state
    let currentReport = '';
    let currentReportFilename = '';
    let isResearching = false;

    // Initialize marked.js with mermaid support
    function configureMarked() {
        marked.setOptions({
            breaks: true,
            gfm: true,
            headerIds: true,
            mangle: false,
            highlight: function(code, lang) {
                // Special handling for mermaid diagrams
                if (lang === 'mermaid') {
                    return `<div class="mermaid">${code}</div>`;
                }
                return code;
            }
        });
        console.log('Marked.js configured successfully with Mermaid support');
    }

    // Initialize Mermaid
    function initMermaid() {
        if (typeof mermaid !== 'undefined') {
            try {
                mermaid.initialize({
                    startOnLoad: false,
                    theme: 'default',
                    securityLevel: 'loose'
                });
                console.log('Mermaid initialized successfully');
            } catch (e) {
                console.warn('Error initializing Mermaid:', e);
            }
        } else {
            console.warn('Mermaid library not loaded');
        }
    }

    // Initial configuration
    configureMarked();
    initMermaid();

    // Handle form submission
    researchForm.addEventListener('submit', (e) => {
        e.preventDefault();

        const topic = topicInput.value.trim();
        if (!topic) {
            vscode.postMessage({
                command: 'showError',
                text: 'Please enter a research topic'
            });
            return;
        }

        if (isResearching) {
            if (!confirm('Research is already in progress. Start a new one?')) {
                return;
            }
        }

        startResearch(topic, parseInt(depthSelect.value), parseInt(breadthSelect.value));
    });

    // Copy report button
    copyReportBtn.addEventListener('click', () => {
        if (currentReport) {
            vscode.postMessage({
                command: 'copyToClipboard',
                text: currentReport
            });

            const originalText = copyReportBtn.textContent;
            copyReportBtn.textContent = 'Copied!';
            setTimeout(() => {
                copyReportBtn.textContent = originalText;
            }, 2000);
        }
    });

    // Save report button
    saveReportBtn.addEventListener('click', () => {
        if (currentReport) {
            vscode.postMessage({
                command: 'saveReport',
                report: currentReport,
                filename: currentReportFilename
            });
        }
    });

    // Start research function
    function startResearch(topic, depth, breadth) {
        // Reset UI
        isResearching = true;
        currentReport = '';
        currentReportFilename = '';
        progressContainer.innerHTML = '<div class="progress-status">Starting research...</div>';
        reportContainer.innerHTML = `
            <div class="placeholder-message">
                <div class="text-center">
                    <div class="loading"></div>
                    <h4 class="mt-3">Researching...</h4>
                    <p class="text-muted">This may take a few minutes depending on the depth and breadth settings.</p>
                </div>
            </div>
        `;
        copyReportBtn.disabled = true;
        saveReportBtn.disabled = true;
        startResearchBtn.disabled = true;

        // Send research request to extension
        vscode.postMessage({
            command: 'startResearch',
            topic,
            depth,
            breadth
        });
    }

    // Handle messages from the extension
    window.addEventListener('message', event => {
        const message = event.data;

        switch (message.command) {
            case 'researchStarted':
                addProgressItem('info', 'Research Started', `Topic: ${message.data.topic}`);
                break;

            case 'searchStarted':
                addProgressItem('search', 'Searching', message.data.query);
                break;

            case 'searchCompleted':
                // Create badge based on relevance
                const badge = message.data.relevant ?
                    '<span class="badge bg-success">Relevant</span>' :
                    '<span class="badge bg-secondary">Not relevant</span>';

                addProgressItem('result', 'Search Result',
                    `${badge} <a href="${message.data.url}" target="_blank">${truncateUrl(message.data.url)}</a>`);
                break;

            case 'learningExtracted':
                addProgressItem('learning', 'Learning Extracted',
                    `${truncateText(message.data.learning, 100)}`);
                break;

            case 'generatingReport':
                addProgressItem('info', 'Generating Report', 'Synthesizing research findings...');
                break;

            case 'researchCompleted':
                isResearching = false;
                startResearchBtn.disabled = false;
                currentReport = message.data.report;
                currentReportFilename = message.data.filename || `deep_research_report_${Date.now()}.md`;

                // Render the report
                reportContainer.innerHTML = '';

                if (!message.data.report) {
                    reportContainer.innerHTML = `
                        <div class="alert alert-warning">
                            <h4>Empty Report</h4>
                            <p>No report data was received.</p>
                        </div>
                    `;
                } else {
                    try {
                        // Parse the markdown
                        const parsedMarkdown = marked.parse(message.data.report);

                        // Render to DOM
                        reportContainer.innerHTML = `<div class="markdown-content">${parsedMarkdown}</div>`;

                        // Render Mermaid diagrams if available
                        if (typeof mermaid !== 'undefined') {
                            try {
                                console.log('Rendering Mermaid diagrams...');
                                mermaid.init(undefined, document.querySelectorAll('.mermaid'));
                                console.log('Mermaid diagrams rendered successfully');
                            } catch (e) {
                                console.warn('Error rendering Mermaid diagrams:', e);
                            }
                        }
                    } catch (error) {
                        reportContainer.innerHTML = `
                            <div class="alert alert-warning">
                                <h4>Markdown Rendering Error</h4>
                                <p>There was an error rendering the report. You can still save the raw markdown.</p>
                                <pre class="mt-3">${message.data.report.substring(0, 500)}...</pre>
                            </div>
                        `;
                    }
                }

                // Enable report actions
                copyReportBtn.disabled = false;
                saveReportBtn.disabled = false;

                addProgressItem('success', 'Research Completed', 'Report generated successfully');

                // Scroll to top of report
                reportContainer.scrollTop = 0;
                break;

            case 'researchError':
                isResearching = false;
                startResearchBtn.disabled = false;

                reportContainer.innerHTML = `
                    <div class="alert alert-danger">
                        <h4>Error</h4>
                        <p>${message.data.error}</p>
                    </div>
                `;

                addProgressItem('error', 'Research Error', message.data.error);
                break;
        }
    });

    // Helper function to add progress item
    function addProgressItem(type, title, content) {
        const item = document.createElement('div');
        item.className = `progress-item ${type}`;
        item.innerHTML = `
            <h5>${title}</h5>
            <p>${content}</p>
        `;

        // Add timestamp
        const timestamp = document.createElement('small');
        timestamp.className = 'text-muted d-block mt-1';
        timestamp.textContent = new Date().toLocaleTimeString();
        item.appendChild(timestamp);

        // Remove "Waiting to start..." message if it exists
        const waitingMsg = progressContainer.querySelector('.progress-status');
        if (waitingMsg) {
            progressContainer.removeChild(waitingMsg);
        }

        progressContainer.appendChild(item);
        progressContainer.scrollTop = progressContainer.scrollHeight;
    }

    // Helper function to truncate URL
    function truncateUrl(url) {
        try {
            const urlObj = new URL(url);
            return urlObj.hostname + (urlObj.pathname.length > 20 ?
                urlObj.pathname.substring(0, 20) + '...' : urlObj.pathname);
        } catch (e) {
            return url.length > 40 ? url.substring(0, 40) + '...' : url;
        }
    }

    // Helper function to truncate text
    function truncateText(text, maxLength) {
        return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
    }
}());
