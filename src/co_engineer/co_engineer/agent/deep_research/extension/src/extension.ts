import * as vscode from 'vscode';
import { DeepResearchPanel } from './deepResearchPanel';
import { ApiConfig } from './deepResearchAgent';

export function activate(context: vscode.ExtensionContext) {
  // Log to the console so we can see when the extension is activated
  console.log('Deep Research extension is now active!');

  try {
    // Register the command to start a new research
    const startResearchCommand = vscode.commands.registerCommand('deep-research.start', () => {
      // Show a notification that the command was triggered
      vscode.window.showInformationMessage('Starting Deep Research...');

      // Create or show the Deep Research panel
      DeepResearchPanel.createOrShow(context.extensionUri);
    });

    // Add our command to the list of disposables which are disposed when the extension is deactivated
    context.subscriptions.push(startResearchCommand);

    // Register an alternative command that's easier to find in the command palette
    const alternativeCommand = vscode.commands.registerCommand('extension.startDeepResearch', () => {
      vscode.window.showInformationMessage('Starting Deep Research...');
      DeepResearchPanel.createOrShow(context.extensionUri);
    });

    context.subscriptions.push(alternativeCommand);

    console.log('Deep Research commands registered successfully');
  } catch (error) {
    console.error('Error registering Deep Research commands:', error);
    vscode.window.showErrorMessage(`Failed to register Deep Research commands: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export function deactivate() {
  // Clean up resources when the extension is deactivated
  console.log('Deep Research extension is deactivated');
}
