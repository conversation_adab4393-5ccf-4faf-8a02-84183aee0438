import { google } from '@ai-sdk/google';
import { createOpenAI } from '@ai-sdk/openai';
import { generateObject, generateText, tool } from 'ai';
import { z } from 'zod';
import Exa from 'exa-js';
import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';

// API configuration interface
export interface ApiConfig {
  useOpenAI: boolean;
  openaiApiKey?: string;
  openaiBaseUrl?: string;
  exaApiKey?: string;
  openaiModelTool?: string;
  openaiModelSummary?: string;
  geminiApiKey?: string;
}

// Default OpenAI models
const DEFAULT_OPENAI_MODEL_TOOL = 'QwQ-32B';
const DEFAULT_OPENAI_MODEL_SUMMARY = 'DeepSeek-V3-0324';

// Initialize clients with default empty values
let mainModel: any;
let summaryModel: any;
let exa: Exa;

// Initialize API clients with provided configuration
export function initializeApiClients(config: ApiConfig): boolean {
  try {
    // Initialize Exa client
    if (!config.exaApiKey) {
      vscode.window.showErrorMessage('Exa API key is required for search functionality');
      return false;
    }
    exa = new Exa(config.exaApiKey);

    // Initialize AI models
    if (config.useOpenAI) {
      // Check if OpenAI API key is provided
      if (!config.openaiApiKey) {
        vscode.window.showErrorMessage('OpenAI API key is required when using OpenAI models');
        return false;
      }

      // Initialize OpenAI client
      const openai = createOpenAI({
        baseURL: config.openaiBaseUrl || 'https://api.openai.com/v1',
        apiKey: config.openaiApiKey,
        compatibility: 'compatible',
      });

      // Set models
      const modelTool = config.openaiModelTool || DEFAULT_OPENAI_MODEL_TOOL;
      const modelSummary = config.openaiModelSummary || DEFAULT_OPENAI_MODEL_SUMMARY;

      mainModel = openai(modelTool);
      summaryModel = openai(modelSummary);
    } else {
      // Use Google models
      // Check if Gemini API key is provided
      if (!config.geminiApiKey) {
        vscode.window.showErrorMessage('Gemini API key is required when using Google models');
        return false;
      }

      // Initialize Google client with API key
      // Note: The google function from @ai-sdk/google might not directly use the API key
      // This is a placeholder for how it might be implemented
      try {
        // For now, we'll just use the models without API key as the SDK might handle it differently
        mainModel = google('gemini-2.0-flash-001');
        summaryModel = google('gemini-2.0-flash-001');

        // Store the API key in case it's needed elsewhere
        process.env.GOOGLE_API_KEY = config.geminiApiKey;
      } catch (error) {
        console.error('Error initializing Google models:', error);
        vscode.window.showErrorMessage(`Failed to initialize Google models: ${error instanceof Error ? error.message : 'Unknown error'}`);
        return false;
      }
    }

    return true;
  } catch (error) {
    console.error('Error initializing API clients:', error);
    vscode.window.showErrorMessage(`Failed to initialize API clients: ${error instanceof Error ? error.message : 'Unknown error'}`);
    return false;
  }
}

// Define types
type SearchResult = {
  title: string;
  url: string;
  content: string;
};

type Learning = {
  learning: string;
  followUpQuestions: string[];
};

type Research = {
  query: string | undefined;
  queries: string[];
  searchResults: SearchResult[];
  learnings: Learning[];
  completedQueries: string[];
};

// Create a null event emitter for when no emitter is provided
const nullEventEmitter = {
  emit: () => {}
};

// Initialize the research object
let accumulatedResearch: Research = {
  query: undefined,
  queries: [],
  searchResults: [],
  learnings: [],
  completedQueries: []
};

// Define the search query generation function
const generateSearchQueries = async (prompt: string, numQueries: number = 3) => {
  try {
    const { object } = await generateObject({
      model: mainModel,
      prompt: `Generate ${numQueries} search queries for the following query: ${prompt}`,
      schema: z.object({
        queries: z.array(z.string()).min(1).max(5),
      }),
    });

    return object.queries;
  } catch (error) {
    console.error('Error generating search queries:', error);
    // Fallback to a simple query if generation fails
    return [prompt];
  }
};

// Define the search function
const searchAndProcess = async (
  query: string,
  existingResults: SearchResult[] = []
): Promise<SearchResult[]> => {
  try {
    // Search the web using Exa
    const { results } = await exa.searchAndContents(query, {
      numResults: 5,
      livecrawl: 'always',
    });

    // Process and filter the results
    const processedResults: SearchResult[] = [];

    if (!results || results.length === 0) {
      console.warn('No search results found for query:', query);
      return [];
    }

    // Map and validate each result
    for (const result of results) {
      // Skip if we already have this URL
      if (existingResults.some(r => r.url === result.url)) {
        continue;
      }

      // Create a search result object
      const searchResult: SearchResult = {
        title: result.title || 'Untitled',
        url: result.url,
        content: result.text || 'No content available',
      };

      processedResults.push(searchResult);
    }

    return processedResults;
  } catch (error) {
    console.error('Error searching:', error);
    return [];
  }
};

// Define the learning extraction function
const extractLearnings = async (searchResults: SearchResult[], query: string): Promise<Learning> => {
  try {
    // Combine the content from all search results
    const combinedContent = searchResults
      .map(result => `Title: ${result.title}\nURL: ${result.url}\nContent: ${result.content.substring(0, 1000)}`)
      .join('\n\n');

    // Generate learnings using the AI model
    const { object } = await generateObject({
      model: mainModel,
      prompt: `The user is researching "${query}". The following search results were deemed relevant.
      Generate a learning and follow-up questions from the following search results:

      <search_results>
      ${combinedContent}
      </search_results>
      `,
      schema: z.object({
        learning: z.string(),
        followUpQuestions: z.array(z.string()),
      }),
    });

    return object;
  } catch (error) {
    console.error('Error extracting learnings:', error);
    // Return a fallback learning if extraction fails
    return {
      learning: 'Unable to extract learnings from the search results.',
      followUpQuestions: ['What are the key aspects of this topic?']
    };
  }
};

// Define the report generation function
export const generateReport = async (research: Research): Promise<string> => {
  try {
    const { text } = await generateText({
      model: summaryModel,
      system: `You are an expert researcher. Today is ${new Date().toISOString()}. Follow these instructions when responding:
        - You may be asked to research subjects that is after your knowledge cutoff, assume the user is right when presented with news.
        - The user is a highly experienced analyst, no need to simplify it, be as detailed as possible and make sure your response is correct.
        - Be highly organized.
        - Suggest solutions that I didn't think about.
        - Be proactive and anticipate my needs.
        - Treat me as an expert in all subject matter.
        - Mistakes erode my trust, so be accurate and thorough.
        - Provide detailed explanations, I'm comfortable with lots of detail.
        - Value good arguments over authorities, the source is irrelevant.
        - Consider new technologies and contrarian ideas, not just the conventional wisdom.
        - You may use high levels of speculation or prediction, just flag it for me.
        - Use Markdown formatting.`,
      prompt: 'Generate a report based on the following research data:\n\n' +
        JSON.stringify(research, null, 2),
    });

    return text;
  } catch (error) {
    console.error('Error generating report:', error);
    return 'Error generating report. Please try again.';
  }
};

// Define the main research function
export const deepResearch = async (
  prompt: string,
  depth: number = 2,
  breadth: number = 2,
  eventEmitter: any = nullEventEmitter
) => {
  // Check if API clients are initialized
  if (!exa || !mainModel || !summaryModel) {
    throw new Error('API clients are not initialized. Please configure API keys first.');
  }

  // Reset the accumulated research for a new research session
  accumulatedResearch = {
    query: prompt,
    queries: [],
    searchResults: [],
    learnings: [],
    completedQueries: []
  };

  if (depth === 0) {
    return accumulatedResearch;
  }

  const queries = await generateSearchQueries(prompt, breadth);
  accumulatedResearch.queries = queries;

  for (const query of queries) {
    console.log(`Searching the web for: ${query}`);

    // Emit search started event
    eventEmitter.emit('searchStarted', { query });

    const searchResults = await searchAndProcess(
      query,
      accumulatedResearch.searchResults
    );

    accumulatedResearch.searchResults.push(...searchResults);
    accumulatedResearch.completedQueries.push(query);

    // Emit search completed events for each result
    for (const result of searchResults) {
      eventEmitter.emit('searchCompleted', {
        query,
        url: result.url,
        relevant: true
      });
    }

    if (searchResults.length > 0) {
      const learning = await extractLearnings(searchResults, query);
      accumulatedResearch.learnings.push(learning);

      // Emit learning extracted event
      eventEmitter.emit('learningExtracted', {
        learning: learning.learning,
        followUpQuestions: learning.followUpQuestions
      });

      // If depth > 1, recursively research follow-up questions
      if (depth > 1 && learning.followUpQuestions.length > 0) {
        // Select a subset of follow-up questions based on breadth
        const followUpQueriesToResearch = learning.followUpQuestions.slice(0, breadth);

        for (const followUpQuery of followUpQueriesToResearch) {
          const newQuery = `Overall research goal: ${prompt}
            Previous search queries: ${accumulatedResearch.completedQueries.join(', ')}
            Follow-up question: ${followUpQuery}`;

          // Recursive call with reduced depth
          await deepResearch(
            newQuery,
            depth - 1,
            Math.ceil(breadth / 2),
            eventEmitter
          );
        }
      }
    }
  }

  return accumulatedResearch;
};
