import * as vscode from 'vscode';
import { deepResearch, generateReport, initializeApiClients, ApiConfig } from './deepResearchAgent';

/**
 * Manages the Deep Research webview panel
 */
export class DeepResearchPanel {
  /**
   * Track the currently panel. Only allow a single panel to exist at a time.
   */
  public static currentPanel: DeepResearchPanel | undefined;

  public static readonly viewType = 'deepResearch';

  private readonly _panel: vscode.WebviewPanel;
  private readonly _extensionUri: vscode.Uri;
  private _disposables: vscode.Disposable[] = [];

  // Get API configuration from VSCode settings
  private static getApiConfig(): ApiConfig {
    const config = vscode.workspace.getConfiguration('deepResearch');
    return {
      useOpenAI: config.get<boolean>('useOpenAI') ?? true,
      openaiApiKey: config.get<string>('openaiApiKey') ?? '123',
      openaiBaseUrl: config.get<string>('openaiBaseUrl') ?? 'http://100.80.20.5:4000',
      openaiModelTool: config.get<string>('openaiModelTool') ?? 'QwQ-32B',
      openaiModelSummary: config.get<string>('openaiModelSummary') ?? 'DeepSeek-V3-0324',
      exaApiKey: config.get<string>('exaApiKey') ?? '',
      geminiApiKey: config.get<string>('geminiApiKey') ?? ''
    };
  }

  public static createOrShow(extensionUri: vscode.Uri) {
    const column = vscode.window.activeTextEditor
      ? vscode.window.activeTextEditor.viewColumn
      : undefined;

    // If we already have a panel, show it.
    if (DeepResearchPanel.currentPanel) {
      DeepResearchPanel.currentPanel._panel.reveal(column);
      return;
    }

    // Otherwise, create a new panel.
    const panel = vscode.window.createWebviewPanel(
      DeepResearchPanel.viewType,
      'Deep Research',
      column || vscode.ViewColumn.One,
      {
        // Enable javascript in the webview
        enableScripts: true,

        // And restrict the webview to only loading content from our extension's directory.
        localResourceRoots: [
          vscode.Uri.joinPath(extensionUri, 'media'),
          vscode.Uri.joinPath(extensionUri, 'out')
        ]
      }
    );

    DeepResearchPanel.currentPanel = new DeepResearchPanel(panel, extensionUri);
  }

  private constructor(panel: vscode.WebviewPanel, extensionUri: vscode.Uri) {
    this._panel = panel;
    this._extensionUri = extensionUri;

    // Set the webview's initial html content
    this._update();

    // Listen for when the panel is disposed
    // This happens when the user closes the panel or when the panel is closed programmatically
    this._panel.onDidDispose(() => this.dispose(), null, this._disposables);

    // Handle messages from the webview
    this._panel.webview.onDidReceiveMessage(
      async (message) => {
        switch (message.command) {
          case 'startResearch':
            await this._startResearch(
              message.topic,
              message.depth,
              message.breadth,
              {
                useOpenAI: message.useOpenAI,
                openaiApiKey: message.openaiApiKey,
                openaiBaseUrl: message.openaiBaseUrl,
                exaApiKey: message.exaApiKey,
                openaiModelTool: message.openaiModelTool,
                openaiModelSummary: message.openaiModelSummary,
                geminiApiKey: message.geminiApiKey
              }
            );
            return;
          case 'saveReport':
            await this._saveReport(message.report, message.filename);
            return;
        }
      },
      null,
      this._disposables
    );
  }

  public dispose() {
    DeepResearchPanel.currentPanel = undefined;

    // Clean up our resources
    this._panel.dispose();

    while (this._disposables.length) {
      const x = this._disposables.pop();
      if (x) {
        x.dispose();
      }
    }
  }

  private async _startResearch(topic: string, depth: number, breadth: number, userApiConfig: ApiConfig) {
    // Merge user provided config with settings config
    const settingsConfig = DeepResearchPanel.getApiConfig();
    const apiConfig: ApiConfig = {
      useOpenAI: userApiConfig.useOpenAI !== undefined ? userApiConfig.useOpenAI : settingsConfig.useOpenAI,
      openaiApiKey: userApiConfig.openaiApiKey || settingsConfig.openaiApiKey,
      openaiBaseUrl: userApiConfig.openaiBaseUrl || settingsConfig.openaiBaseUrl,
      openaiModelTool: userApiConfig.openaiModelTool || settingsConfig.openaiModelTool,
      openaiModelSummary: userApiConfig.openaiModelSummary || settingsConfig.openaiModelSummary,
      exaApiKey: userApiConfig.exaApiKey || settingsConfig.exaApiKey,
      geminiApiKey: userApiConfig.geminiApiKey || settingsConfig.geminiApiKey
    };
    // Create an event emitter to track progress
    const eventEmitter = {
      emit: (event: string, data: any) => {
        this._panel.webview.postMessage({
          command: event,
          data
        });
      }
    };

    try {
      // Notify the webview that research has started
      this._panel.webview.postMessage({
        command: 'researchStarted',
        data: { topic }
      });

      // Initialize API clients with provided configuration
      const initialized = initializeApiClients(apiConfig);
      if (!initialized) {
        throw new Error('Failed to initialize API clients. Please check your API keys.');
      }

      // Start the research
      const research = await deepResearch(
        topic,
        depth,
        breadth,
        eventEmitter
      );

      // Notify the webview that we're generating a report
      this._panel.webview.postMessage({
        command: 'generatingReport',
        data: {}
      });

      // Generate the report
      const report = await generateReport(research);

      // Generate a filename for the report
      const filename = `deep_research_report_${Date.now()}.md`;

      // Send the completed report to the webview
      this._panel.webview.postMessage({
        command: 'researchCompleted',
        data: {
          report,
          filename
        }
      });
    } catch (error) {
      // Handle any errors
      this._panel.webview.postMessage({
        command: 'researchError',
        data: {
          error: error instanceof Error ? error.message : 'An unknown error occurred'
        }
      });
    }
  }

  private async _saveReport(report: string, filename: string) {
    try {
      // Show a file save dialog
      const workspaceFolders = vscode.workspace.workspaceFolders;

      if (!workspaceFolders) {
        throw new Error('No workspace folder is open');
      }

      const defaultUri = vscode.Uri.joinPath(workspaceFolders[0].uri, filename);
      const fileUri = await vscode.window.showSaveDialog({
        defaultUri,
        filters: {
          'Markdown': ['md'],
          'All Files': ['*']
        }
      });

      if (fileUri) {
        // Write the report to the selected file
        await vscode.workspace.fs.writeFile(fileUri, Buffer.from(report, 'utf8'));
        vscode.window.showInformationMessage(`Report saved to ${fileUri.fsPath}`);
      }
    } catch (error) {
      vscode.window.showErrorMessage(`Failed to save report: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private _update() {
    const webview = this._panel.webview;
    this._panel.title = "Deep Research";
    this._panel.webview.html = this._getHtmlForWebview(webview);
  }

  private _getHtmlForWebview(webview: vscode.Webview) {
    // We don't need to load external scripts or styles as they're included inline in the HTML

    // Use a nonce to only allow specific scripts to be run
    const nonce = getNonce();

    // Get API configuration from settings
    const apiConfig = DeepResearchPanel.getApiConfig();

    // Note: We're using the _extensionUri in other parts of the code, so we keep it as a class member
    // even though it's not directly used in this method

    return `<!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource} https://cdn.jsdelivr.net; img-src ${webview.cspSource} https: data:; script-src 'nonce-${nonce}' https://cdn.jsdelivr.net; worker-src blob:;">
        <title>Deep Research</title>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
        <style>
            :root {
                --chatgpt-primary: #10a37f;
                --chatgpt-primary-hover: #0e8e6f;
                --chatgpt-sidebar: #202123;
                --chatgpt-sidebar-hover: #2a2b32;
                --chatgpt-content-bg: #343541;
                --chatgpt-content-light: #444654;
                --chatgpt-border: #4d4d4f;
                --chatgpt-text: #ececf1;
                --chatgpt-text-secondary: #c5c5d2;
                --chatgpt-text-tertiary: #8e8ea0;
            }

            body {
                font-family: var(--vscode-font-family, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif);
                background-color: var(--vscode-editor-background, var(--chatgpt-content-bg));
                color: var(--vscode-editor-foreground, var(--chatgpt-text));
                padding: 0;
                margin: 0;
                overflow: hidden;
            }

            .container-fluid {
                height: 100vh;
                padding: 0;
                overflow: hidden;
            }

            .row {
                height: 100%;
                margin: 0;
            }

            /* Sidebar styles - ChatGPT-like */
            .sidebar {
                background-color: var(--vscode-sideBar-background, var(--chatgpt-sidebar));
                border-right: 1px solid var(--vscode-panel-border, var(--chatgpt-border));
                padding: 0;
                height: 100vh;
                overflow-y: auto;
                display: flex;
                flex-direction: column;
            }

            .sidebar-header {
                padding: 16px;
                border-bottom: 1px solid var(--vscode-panel-border, var(--chatgpt-border));
                margin-bottom: 0;
                background-color: var(--vscode-sideBarSectionHeader-background, var(--chatgpt-sidebar));
            }

            .sidebar-header h2 {
                margin-bottom: 5px;
                color: var(--vscode-sideBarTitle-foreground, var(--chatgpt-text));
                font-size: 1.2rem;
                font-weight: 600;
            }

            .sidebar-header p {
                font-size: 0.8rem;
                line-height: 1.4;
                margin-bottom: 0;
                word-wrap: break-word;
                color: var(--vscode-descriptionForeground, var(--chatgpt-text-tertiary));
            }

            .input-section {
                padding: 16px;
                border-bottom: 1px solid var(--vscode-panel-border, var(--chatgpt-border));
            }

            .progress-section {
                padding: 16px;
                flex-grow: 1;
                overflow-y: auto;
            }

            .progress-section h4 {
                font-size: 0.9rem;
                color: var(--vscode-descriptionForeground, var(--chatgpt-text-tertiary));
                margin-bottom: 12px;
                font-weight: 600;
            }

            .progress-item {
                margin-bottom: 12px;
                padding: 10px 12px;
                border-radius: 6px;
                background-color: var(--vscode-editor-background, var(--chatgpt-content-light));
                border-left: 3px solid var(--chatgpt-primary);
                transition: all 0.2s ease;
            }

            .progress-item:hover {
                background-color: var(--vscode-list-hoverBackground, rgba(255, 255, 255, 0.05));
            }

            .progress-item.search {
                border-left-color: var(--chatgpt-primary);
            }

            .progress-item.result {
                border-left-color: #4caf50;
            }

            .progress-item.learning {
                border-left-color: #ff9800;
            }

            .progress-item.error {
                border-left-color: #f44336;
            }

            .progress-item h5 {
                margin-bottom: 4px;
                font-size: 0.85rem;
                font-weight: 600;
                color: var(--chatgpt-text);
            }

            .progress-item p {
                margin-bottom: 0;
                font-size: 0.8rem;
                color: var(--vscode-descriptionForeground, var(--chatgpt-text-secondary));
            }

            .progress-item .badge {
                font-size: 0.7rem;
                padding: 2px 6px;
                border-radius: 4px;
                font-weight: 500;
            }

            /* Main content styles - ChatGPT-like */
            .main-content {
                padding: 0;
                height: 100vh;
                overflow-y: hidden;
                display: flex;
                flex-direction: column;
                background-color: var(--vscode-editor-background, var(--chatgpt-content-bg));
            }

            .report-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 16px 20px;
                border-bottom: 1px solid var(--vscode-panel-border, var(--chatgpt-border));
                background-color: var(--vscode-editor-background, var(--chatgpt-content-bg));
                z-index: 10;
            }

            .report-header h3 {
                margin: 0;
                font-size: 1.1rem;
                font-weight: 600;
                color: var(--chatgpt-text);
            }

            .report-actions button {
                margin-left: 8px;
            }

            #report-container {
                flex-grow: 1;
                overflow-y: auto;
                padding: 20px;
                background-color: var(--vscode-editor-background, var(--chatgpt-content-bg));
            }

            .placeholder-message {
                display: flex;
                height: 100%;
                min-height: 300px;
                align-items: center;
                justify-content: center;
                color: var(--vscode-descriptionForeground, var(--chatgpt-text-tertiary));
                text-align: center;
            }

            /* Markdown content styling - ChatGPT-like */
            .markdown-content {
                line-height: 1.6;
                font-size: 0.95rem;
                color: var(--chatgpt-text);
                max-width: 800px;
                margin: 0 auto;
            }

            .markdown-content h1,
            .markdown-content h2,
            .markdown-content h3 {
                margin-top: 1.5em;
                margin-bottom: 0.75em;
                color: var(--vscode-editor-foreground, var(--chatgpt-text));
                font-weight: 600;
            }

            .markdown-content h1 {
                font-size: 1.5rem;
            }

            .markdown-content h2 {
                font-size: 1.3rem;
            }

            .markdown-content h3 {
                font-size: 1.1rem;
            }

            .markdown-content h1:first-child,
            .markdown-content h2:first-child,
            .markdown-content h3:first-child {
                margin-top: 0;
            }

            .markdown-content p {
                margin-bottom: 1em;
                color: var(--vscode-editor-foreground, var(--chatgpt-text));
            }

            .markdown-content pre {
                background-color: var(--vscode-textCodeBlock-background, #2d2d33);
                padding: 12px 16px;
                border-radius: 6px;
                overflow-x: auto;
                margin: 1em 0;
            }

            .markdown-content code {
                font-family: var(--vscode-editor-font-family, 'Menlo', 'Monaco', 'Courier New', monospace);
                font-size: 0.85em;
                color: var(--vscode-textPreformat-foreground, #e3e3e3);
                background-color: var(--vscode-textCodeBlock-background, #2d2d33);
                padding: 0.2em 0.4em;
                border-radius: 3px;
            }

            .markdown-content pre code {
                padding: 0;
                background-color: transparent;
            }

            .markdown-content blockquote {
                border-left: 4px solid var(--chatgpt-primary);
                padding: 0 0 0 16px;
                margin: 0 0 1em 0;
                color: var(--vscode-descriptionForeground, var(--chatgpt-text-secondary));
            }

            .markdown-content ul,
            .markdown-content ol {
                margin-bottom: 1em;
                padding-left: 2em;
            }

            .markdown-content li {
                margin-bottom: 0.5em;
            }

            .markdown-content table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 1em;
                font-size: 0.9em;
            }

            .markdown-content th,
            .markdown-content td {
                border: 1px solid var(--vscode-panel-border, var(--chatgpt-border));
                padding: 8px 12px;
                text-align: left;
            }

            .markdown-content th {
                background-color: var(--vscode-editor-background, #2d2d33);
                font-weight: 600;
            }

            /* Loading animation - ChatGPT-like */
            .loading {
                display: inline-block;
                width: 18px;
                height: 18px;
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 50%;
                border-top-color: var(--chatgpt-primary);
                animation: spin 1s ease-in-out infinite;
                margin-right: 10px;
            }

            @keyframes spin {
                to { transform: rotate(360deg); }
            }

            /* Form controls - ChatGPT-like */
            .form-control, .form-select {
                background-color: var(--vscode-input-background, var(--chatgpt-sidebar-hover));
                color: var(--vscode-input-foreground, var(--chatgpt-text));
                border: 1px solid var(--vscode-input-border, var(--chatgpt-border));
                border-radius: 6px;
                padding: 10px 12px;
                font-size: 0.9rem;
                transition: all 0.2s ease;
            }

            .form-control:focus, .form-select:focus {
                border-color: var(--chatgpt-primary);
                box-shadow: 0 0 0 2px rgba(16, 163, 127, 0.2);
                outline: none;
            }

            .form-text {
                font-size: 0.75rem;
                color: var(--vscode-descriptionForeground, var(--chatgpt-text-tertiary));
                margin-top: 4px;
            }

            /* Buttons - ChatGPT-like */
            .btn {
                font-size: 0.9rem;
                padding: 8px 12px;
                border-radius: 6px;
                font-weight: 500;
                transition: all 0.2s ease;
            }

            .btn-primary {
                background-color: var(--chatgpt-primary);
                border-color: var(--chatgpt-primary);
                color: white;
            }

            .btn-primary:hover, .btn-primary:focus {
                background-color: var(--chatgpt-primary-hover);
                border-color: var(--chatgpt-primary-hover);
                color: white;
            }

            .btn-outline-secondary {
                color: var(--chatgpt-text-secondary);
                border-color: var(--chatgpt-border);
                background-color: transparent;
            }

            .btn-outline-secondary:hover, .btn-outline-secondary:focus {
                background-color: rgba(255, 255, 255, 0.1);
                color: var(--chatgpt-text);
                border-color: var(--chatgpt-text-secondary);
            }

            /* Accordion - ChatGPT-like */
            .accordion {
                border-radius: 6px;
                overflow: hidden;
                border: 1px solid var(--vscode-panel-border, var(--chatgpt-border));
            }

            .accordion-item {
                background-color: var(--vscode-input-background, var(--chatgpt-sidebar-hover));
                border: none;
                border-bottom: 1px solid var(--vscode-panel-border, var(--chatgpt-border));
            }

            .accordion-item:last-child {
                border-bottom: none;
            }

            .accordion-header {
                margin: 0;
            }

            .accordion-button {
                padding: 12px 16px;
                font-size: 0.9rem;
                font-weight: 500;
                color: var(--chatgpt-text);
                background-color: var(--vscode-input-background, var(--chatgpt-sidebar-hover));
            }

            .accordion-button:not(.collapsed) {
                color: var(--chatgpt-text);
                background-color: var(--vscode-input-background, var(--chatgpt-sidebar-hover));
                box-shadow: none;
            }

            .accordion-button:focus {
                box-shadow: none;
                border-color: var(--chatgpt-primary);
            }

            .accordion-button::after {
                background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23ffffff'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
            }

            .accordion-body {
                padding: 16px;
                background-color: var(--vscode-input-background, var(--chatgpt-sidebar-hover));
            }

            /* Form check - ChatGPT-like */
            .form-check-input {
                background-color: var(--vscode-input-background, var(--chatgpt-sidebar-hover));
                border-color: var(--chatgpt-border);
            }

            .form-check-input:checked {
                background-color: var(--chatgpt-primary);
                border-color: var(--chatgpt-primary);
            }

            .form-check-label {
                font-size: 0.9rem;
                color: var(--chatgpt-text);
            }

            /* AI avatar styling */
            .ai-avatar {
                width: 30px;
                height: 30px;
                background-color: var(--chatgpt-primary);
                border-radius: 4px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-weight: bold;
            }

            /* Responsive adjustments */
            @media (max-width: 768px) {
                .sidebar {
                    height: auto;
                    max-height: 40vh;
                }

                .main-content {
                    height: 60vh;
                }
            }
        </style>
    </head>
    <body>
        <div class="container-fluid">
            <div class="row">
                <!-- Left sidebar for research progress -->
                <div class="col-md-4 sidebar">
                    <div class="sidebar-header">
                        <h2 class="mb-2">Deep Research</h2>
                        <p class="text-muted">Multi-Agent Research System - Powered by XE9680 with AMD MI300X</p>
                    </div>

                    <div class="input-section">
                        <form id="research-form">
                            <div class="mb-3">
                                <label for="topic" class="form-label">Research Topic</label>
                                <textarea id="topic" class="form-control" rows="3" placeholder="Enter your research topic here..."></textarea>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="depth" class="form-label">Depth</label>
                                        <select id="depth" class="form-select">
                                            <option value="1">1 - Quick</option>
                                            <option value="2" selected>2 - Standard</option>
                                            <option value="3">3 - Deep</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="breadth" class="form-label">Breadth</label>
                                        <select id="breadth" class="form-select">
                                            <option value="2">2 - Focused</option>
                                            <option value="3" selected>3 - Balanced</option>
                                            <option value="4">4 - Broad</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="accordion" id="apiConfigAccordion">
                                    <div class="accordion-item">
                                        <h2 class="accordion-header" id="apiConfigHeading">
                                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#apiConfigCollapse" aria-expanded="true" aria-controls="apiConfigCollapse">
                                                API Configuration
                                            </button>
                                        </h2>
                                        <div id="apiConfigCollapse" class="accordion-collapse collapse show" aria-labelledby="apiConfigHeading" data-bs-parent="#apiConfigAccordion">
                                            <div class="accordion-body">
                                                <div class="mb-3">
                                                    <div class="form-check form-switch">
                                                        <input class="form-check-input" type="checkbox" id="useOpenAI" checked>
                                                        <label class="form-check-label" for="useOpenAI">Use OpenAI Models</label>
                                                    </div>
                                                </div>

                                                <div class="mb-3">
                                                    <label for="exaApiKey" class="form-label">Exa API Key (Required)</label>
                                                    <input type="password" class="form-control" id="exaApiKey" placeholder="Enter Exa API key">
                                                    <div class="form-text">Required for web search functionality</div>
                                                </div>

                                                <div id="openaiConfig">
                                                    <div class="mb-3">
                                                        <label for="openaiApiKey" class="form-label">OpenAI API Key</label>
                                                        <input type="password" class="form-control" id="openaiApiKey" value="123" placeholder="Enter OpenAI API key">
                                                    </div>

                                                    <div class="mb-3">
                                                        <label for="openaiBaseUrl" class="form-label">OpenAI Base URL (Optional)</label>
                                                        <input type="text" class="form-control" id="openaiBaseUrl" value="http://100.80.20.5:4000" placeholder="https://api.openai.com/v1">
                                                        <div class="form-text">Leave empty to use the default OpenAI API endpoint</div>
                                                    </div>

                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <div class="mb-3">
                                                                <label for="openaiModelTool" class="form-label">Tool Model</label>
                                                                <input type="text" class="form-control" id="openaiModelTool" value="QwQ-32B" placeholder="Enter model name">
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="mb-3">
                                                                <label for="openaiModelSummary" class="form-label">Summary Model</label>
                                                                <input type="text" class="form-control" id="openaiModelSummary" value="DeepSeek-V3-0324" placeholder="Enter model name">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div id="googleConfig" style="display: none;">
                                                    <div class="mb-3">
                                                        <label for="geminiApiKey" class="form-label">Google Gemini API Key</label>
                                                        <input type="password" class="form-control" id="geminiApiKey" placeholder="Enter Gemini API key">
                                                        <div class="form-text">Required when using Google Gemini models</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary w-100" id="start-research">Start Research</button>
                        </form>
                    </div>

                    <div class="progress-section mt-4">
                        <h4>Research Progress</h4>
                        <div id="progress-container">
                            <div class="progress-status">Waiting to start...</div>
                        </div>
                    </div>
                </div>

                <!-- Main content area for report -->
                <div class="col-md-8 main-content">
                    <div class="report-header">
                        <h3>Research Report</h3>
                        <div class="report-actions">
                            <button id="copy-report" class="btn btn-sm btn-outline-secondary" disabled>Copy</button>
                            <button id="save-report" class="btn btn-sm btn-outline-secondary" disabled>Save to Workspace</button>
                        </div>
                    </div>
                    <div id="report-container">
                        <div class="placeholder-message">
                            <div class="text-center">
                                <div style="font-size: 3rem; margin-bottom: 1rem;">🔍</div>
                                <h4 class="mb-3">Multi-Agent Research System</h4>
                                <p class="text-muted mb-4">Enter a research topic in the sidebar and click "Start Research" to begin.</p>
                                <p class="text-muted" style="max-width: 500px;">This system uses multiple AI agents to search the web, extract relevant information, and generate a comprehensive research report on any topic.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script nonce="${nonce}" src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
        <script nonce="${nonce}" src="https://cdn.jsdelivr.net/npm/marked@4.0.0/marked.min.js"></script>
        <script nonce="${nonce}" src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
        <script nonce="${nonce}">
            (function() {
                const vscode = acquireVsCodeApi();

                // API configuration from VSCode settings
                const apiConfig = ${JSON.stringify(apiConfig)};

                // DOM elements
                const researchForm = document.getElementById('research-form');
                const topicInput = document.getElementById('topic');
                const depthSelect = document.getElementById('depth');
                const breadthSelect = document.getElementById('breadth');
                const startResearchBtn = document.getElementById('start-research');
                const progressContainer = document.getElementById('progress-container');
                const reportContainer = document.getElementById('report-container');
                const copyReportBtn = document.getElementById('copy-report');
                const saveReportBtn = document.getElementById('save-report');

                // API configuration elements
                const useOpenAICheckbox = document.getElementById('useOpenAI');
                const exaApiKeyInput = document.getElementById('exaApiKey');
                const openaiApiKeyInput = document.getElementById('openaiApiKey');
                const openaiBaseUrlInput = document.getElementById('openaiBaseUrl');
                const openaiModelToolInput = document.getElementById('openaiModelTool');
                const openaiModelSummaryInput = document.getElementById('openaiModelSummary');
                const openaiConfigSection = document.getElementById('openaiConfig');
                const googleConfigSection = document.getElementById('googleConfig');
                const geminiApiKeyInput = document.getElementById('geminiApiKey');

                // Current research state
                let currentReport = '';
                let currentReportFilename = '';
                let isResearching = false;

                // Initialize marked.js with mermaid support
                function configureMarked() {
                    marked.setOptions({
                        breaks: true,
                        gfm: true,
                        headerIds: true,
                        mangle: false,
                        highlight: function(code, lang) {
                            // Special handling for mermaid diagrams
                            if (lang === 'mermaid') {
                                return `<div class="mermaid">${code}</div>`;
                            }
                            return code;
                        }
                    });
                    console.log('Marked.js configured successfully with Mermaid support');
                }

                // Initialize Mermaid
                function initMermaid() {
                    if (typeof mermaid !== 'undefined') {
                        try {
                            mermaid.initialize({
                                startOnLoad: false,
                                theme: 'default',
                                securityLevel: 'loose'
                            });
                            console.log('Mermaid initialized successfully');
                        } catch (e) {
                            console.warn('Error initializing Mermaid:', e);
                        }
                    } else {
                        console.warn('Mermaid library not loaded');
                    }
                }

                // Initial configuration
                configureMarked();
                initMermaid();

                // Set initial values from configuration
                useOpenAICheckbox.checked = apiConfig.useOpenAI;
                exaApiKeyInput.value = apiConfig.exaApiKey || '';
                openaiApiKeyInput.value = apiConfig.openaiApiKey || '123';
                openaiBaseUrlInput.value = apiConfig.openaiBaseUrl || 'http://100.80.20.5:4000';
                openaiModelToolInput.value = apiConfig.openaiModelTool || 'QwQ-32B';
                openaiModelSummaryInput.value = apiConfig.openaiModelSummary || 'DeepSeek-V3-0324';
                geminiApiKeyInput.value = apiConfig.geminiApiKey || '';

                // Toggle configuration visibility based on checkbox
                useOpenAICheckbox.addEventListener('change', () => {
                    openaiConfigSection.style.display = useOpenAICheckbox.checked ? 'block' : 'none';
                    googleConfigSection.style.display = useOpenAICheckbox.checked ? 'none' : 'block';
                });

                // Initialize config visibility based on the setting
                openaiConfigSection.style.display = apiConfig.useOpenAI ? 'block' : 'none';
                googleConfigSection.style.display = apiConfig.useOpenAI ? 'none' : 'block';

                // Handle form submission
                researchForm.addEventListener('submit', (e) => {
                    e.preventDefault();

                    const topic = topicInput.value.trim();
                    if (!topic) {
                        vscode.postMessage({
                            command: 'showError',
                            text: 'Please enter a research topic'
                        });
                        return;
                    }

                    // Validate API keys
                    const exaApiKey = exaApiKeyInput.value.trim();
                    if (!exaApiKey) {
                        vscode.postMessage({
                            command: 'showError',
                            text: 'Exa API key is required for search functionality'
                        });
                        return;
                    }

                    const useOpenAI = useOpenAICheckbox.checked;
                    if (useOpenAI) {
                        const openaiApiKey = openaiApiKeyInput.value.trim();
                        if (!openaiApiKey) {
                            vscode.postMessage({
                                command: 'showError',
                                text: 'OpenAI API key is required when using OpenAI models'
                            });
                            return;
                        }
                    } else {
                        // Validate Gemini API key when using Google models
                        const geminiApiKey = geminiApiKeyInput.value.trim();
                        if (!geminiApiKey) {
                            vscode.postMessage({
                                command: 'showError',
                                text: 'Gemini API key is required when using Google models'
                            });
                            return;
                        }
                    }

                    if (isResearching) {
                        if (!confirm('Research is already in progress. Start a new one?')) {
                            return;
                        }
                    }

                    startResearch(
                        topic,
                        parseInt(depthSelect.value),
                        parseInt(breadthSelect.value)
                    );
                });

                // Copy report button
                copyReportBtn.addEventListener('click', () => {
                    if (currentReport) {
                        vscode.postMessage({
                            command: 'copyToClipboard',
                            text: currentReport
                        });

                        const originalText = copyReportBtn.textContent;
                        copyReportBtn.textContent = 'Copied!';
                        setTimeout(() => {
                            copyReportBtn.textContent = originalText;
                        }, 2000);
                    }
                });

                // Save report button
                saveReportBtn.addEventListener('click', () => {
                    if (currentReport) {
                        vscode.postMessage({
                            command: 'saveReport',
                            report: currentReport,
                            filename: currentReportFilename
                        });
                    }
                });

                // Start research function
                function startResearch(topic, depth, breadth) {
                    // Reset UI
                    isResearching = true;
                    currentReport = '';
                    currentReportFilename = '';
                    progressContainer.innerHTML = '<div class="progress-status">Starting research...</div>';
                    reportContainer.innerHTML = \`
                        <div class="placeholder-message">
                            <div class="text-center">
                                <div class="loading"></div>
                                <h4 class="mt-3">Researching...</h4>
                                <p class="text-muted mb-3">This may take a few minutes depending on the depth and breadth settings.</p>
                                <p class="text-muted" style="max-width: 500px;">Our AI agents are searching the web, analyzing information, and preparing a comprehensive report on your topic.</p>
                                <p class="text-muted mt-4" style="font-size: 0.8rem;">You can track the progress in the sidebar.</p>
                            </div>
                        </div>
                    \`;
                    copyReportBtn.disabled = true;
                    saveReportBtn.disabled = true;
                    startResearchBtn.disabled = true;

                    // Get API configuration
                    const useOpenAI = useOpenAICheckbox.checked;
                    const exaApiKey = exaApiKeyInput.value.trim();
                    const openaiApiKey = openaiApiKeyInput.value.trim();
                    const openaiBaseUrl = openaiBaseUrlInput.value.trim();
                    const openaiModelTool = openaiModelToolInput.value.trim();
                    const openaiModelSummary = openaiModelSummaryInput.value.trim();
                    const geminiApiKey = geminiApiKeyInput.value.trim();

                    // Send research request to extension
                    vscode.postMessage({
                        command: 'startResearch',
                        topic,
                        depth,
                        breadth,
                        useOpenAI,
                        exaApiKey,
                        openaiApiKey,
                        openaiBaseUrl,
                        openaiModelTool,
                        openaiModelSummary,
                        geminiApiKey
                    });
                }

                // Handle messages from the extension
                window.addEventListener('message', event => {
                    const message = event.data;

                    switch (message.command) {
                        case 'researchStarted':
                            addProgressItem('info', 'Research Started', \`Topic: \${message.data.topic}\`);
                            break;

                        case 'searchStarted':
                            addProgressItem('search', 'Searching', message.data.query);
                            break;

                        case 'searchCompleted':
                            // Create badge based on relevance
                            const badge = message.data.relevant ?
                                '<span class="badge bg-success">Relevant</span>' :
                                '<span class="badge bg-secondary">Not relevant</span>';

                            addProgressItem('result', 'Search Result',
                                \`\${badge} <a href="\${message.data.url}" target="_blank">\${truncateUrl(message.data.url)}</a>\`);
                            break;

                        case 'learningExtracted':
                            addProgressItem('learning', 'Learning Extracted',
                                \`\${truncateText(message.data.learning, 100)}\`);
                            break;

                        case 'generatingReport':
                            addProgressItem('info', 'Generating Report', 'Synthesizing research findings...');
                            break;

                        case 'researchCompleted':
                            isResearching = false;
                            startResearchBtn.disabled = false;
                            currentReport = message.data.report;
                            currentReportFilename = message.data.filename || \`deep_research_report_\${Date.now()}.md\`;

                            // Render the report
                            reportContainer.innerHTML = '';

                            if (!message.data.report) {
                                reportContainer.innerHTML = \`
                                    <div class="alert alert-warning">
                                        <h4>Empty Report</h4>
                                        <p>No report data was received.</p>
                                    </div>
                                \`;
                            } else {
                                try {
                                    // Parse the markdown
                                    const parsedMarkdown = marked.parse(message.data.report);

                                    // Render to DOM with ChatGPT-like styling
                                    reportContainer.innerHTML = \`
                                        <div class="d-flex align-items-start mb-4 p-3" style="background-color: var(--chatgpt-content-light, #444654); border-radius: 8px;">
                                            <div class="me-3">
                                                <div style="width: 30px; height: 30px; background-color: var(--chatgpt-primary); border-radius: 4px; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">AI</div>
                                            </div>
                                            <div class="markdown-content">\${parsedMarkdown}</div>
                                        </div>
                                    \`;
                                } catch (error) {
                                    reportContainer.innerHTML = \`
                                        <div class="d-flex align-items-start mb-4 p-3" style="background-color: rgba(255, 152, 0, 0.1); border-radius: 8px; border-left: 4px solid #ff9800;">
                                            <div class="me-3">
                                                <div style="font-size: 1.5rem;">⚠️</div>
                                            </div>
                                            <div>
                                                <h4 class="mb-2">Markdown Rendering Error</h4>
                                                <p>There was an error rendering the report. You can still save the raw markdown.</p>
                                                <div class="mt-3 p-2" style="background-color: rgba(0,0,0,0.2); border-radius: 4px; overflow: auto; max-height: 300px;">
                                                    <pre style="margin: 0; white-space: pre-wrap;">\${message.data.report.substring(0, 500)}...</pre>
                                                </div>
                                            </div>
                                        </div>
                                    \`;
                                }
                            }

                            // Enable report actions
                            copyReportBtn.disabled = false;
                            saveReportBtn.disabled = false;

                            addProgressItem('success', 'Research Completed', 'Report generated successfully');

                            // Scroll to top of report
                            reportContainer.scrollTop = 0;
                            break;

                        case 'researchError':
                            isResearching = false;
                            startResearchBtn.disabled = false;

                            reportContainer.innerHTML = \`
                                <div class="d-flex align-items-start mb-4 p-3" style="background-color: rgba(244, 67, 54, 0.1); border-radius: 8px; border-left: 4px solid #f44336;">
                                    <div class="me-3">
                                        <div style="font-size: 1.5rem;">❌</div>
                                    </div>
                                    <div>
                                        <h4 class="mb-2">Research Error</h4>
                                        <p>\${message.data.error}</p>
                                        <p class="mt-3 text-muted">Please try again with a different topic or check your API configuration.</p>
                                    </div>
                                </div>
                            \`;

                            addProgressItem('error', 'Research Error', message.data.error);
                            break;
                    }
                });

                // Helper function to add progress item
                function addProgressItem(type, title, content) {
                    const item = document.createElement('div');
                    item.className = \`progress-item \${type}\`;

                    // Create icon based on type
                    let icon = '🔍'; // Default search icon
                    if (type === 'result') icon = '📄';
                    if (type === 'learning') icon = '💡';
                    if (type === 'error') icon = '❌';
                    if (type === 'success') icon = '✅';
                    if (type === 'info') icon = 'ℹ️';

                    item.innerHTML = \`
                        <div class="d-flex align-items-start">
                            <div class="me-2" style="font-size: 1.1rem;">\${icon}</div>
                            <div class="flex-grow-1">
                                <h5>\${title}</h5>
                                <p>\${content}</p>
                                <small class="text-muted d-block mt-1">\${new Date().toLocaleTimeString()}</small>
                            </div>
                        </div>
                    \`;

                    // Add animation
                    item.style.opacity = '0';
                    item.style.transform = 'translateY(10px)';

                    // Remove "Waiting to start..." message if it exists
                    const waitingMsg = progressContainer.querySelector('.progress-status');
                    if (waitingMsg) {
                        progressContainer.removeChild(waitingMsg);
                    }

                    progressContainer.appendChild(item);
                    progressContainer.scrollTop = progressContainer.scrollHeight;

                    // Trigger animation
                    setTimeout(() => {
                        item.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                        item.style.opacity = '1';
                        item.style.transform = 'translateY(0)';
                    }, 10);
                }

                // Helper function to truncate URL
                function truncateUrl(url) {
                    try {
                        const urlObj = new URL(url);
                        return urlObj.hostname + (urlObj.pathname.length > 20 ?
                            urlObj.pathname.substring(0, 20) + '...' : urlObj.pathname);
                    } catch (e) {
                        return url.length > 40 ? url.substring(0, 40) + '...' : url;
                    }
                }

                // Helper function to truncate text
                function truncateText(text, maxLength) {
                    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
                }
            }());
        </script>
    </body>
    </html>`;
  }
}

function getNonce() {
  let text = '';
  const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  for (let i = 0; i < 32; i++) {
    text += possible.charAt(Math.floor(Math.random() * possible.length));
  }
  return text;
}
