# Deep Research VSCode Extension

A VSCode extension for the Deep Research Multi-Agent System, powered by DELL XE9680 with AMD MI300X.

## Features

- Conduct deep research on any topic directly from VSCode
- Configure research depth and breadth
- View real-time research progress
- Generate comprehensive markdown reports
- Save reports to your workspace

## Requirements

- VSCode 1.80.0 or higher
- Node.js 18 or higher
- Internet connection for web search functionality

## Extension Settings

This extension contributes the following settings:

* `deep-research.start`: Start a new research session

## Usage

1. There are multiple ways to start the Deep Research extension:
   - Press `Ctrl+Alt+R` (or `Cmd+Alt+R` on Mac) when focused in an editor
   - Press `Ctrl+Shift+Alt+R` (or `Cmd+Shift+Alt+R` on Mac) anywhere in VSCode
   - Open the command palette (`Ctrl+Shift+P`) and type "Start Deep Research"
   - Open the command palette (`Ctrl+Shift+P`) and type "Deep Research: Start New Research"
2. Enter your research topic in the input field
3. Configure depth and breadth settings:
   - Depth: Controls how many levels of follow-up questions to explore (1-3)
   - Breadth: Controls how many search queries to generate per topic (2-4)
4. Click "Start Research" to begin the research process
5. View the progress in the sidebar as the research progresses
6. When complete, view the comprehensive report in the main panel
7. Use the buttons to save the report to your workspace or copy it to clipboard

## Troubleshooting Command Not Found

If you encounter a "command not found" error:

1. Try using the alternative command "Start Deep Research" from the command palette
2. Try using the keyboard shortcut `Ctrl+Shift+Alt+R` (or `Cmd+Shift+Alt+R` on Mac)
3. Reload VSCode window (Ctrl+R or Cmd+R on Mac) and try again
4. Check the "Output" panel (View > Output) and select "Deep Research" from the dropdown to see any error messages

## Environment Variables

The extension requires the following environment variables in a `.env` file:

- `EXA_API_KEY`: API key for Exa search
- `USE_OPENAI`: Set to 'true' to use OpenAI models, 'false' to use Google models
- `OPENAI_API_KEY`: API key for OpenAI (if USE_OPENAI is true)
- `OPENAI_API_BASE_URL`: Base URL for OpenAI API (if USE_OPENAI is true)
- `GOOGLE_API_KEY`: API key for Google Gemini (if USE_OPENAI is false)

## Installation

1. Clone this repository
2. Navigate to the extension directory:
   ```bash
   cd src/co_engineer/co_engineer/agent/deep_research/extension
   ```
3. Copy the `.env.example` file to `.env` and fill in your API keys:
   ```bash
   cp .env.example .env
   # Edit the .env file with your API keys
   ```
4. Install dependencies:
   ```bash
   npm install
   ```
5. Compile the extension:
   ```bash
   npm run compile
   ```
6. Package the extension (requires vsce):
   ```bash
   npm install -g @vscode/vsce
   vsce package
   ```
7. Install the extension in VSCode:
   - Go to Extensions view (Ctrl+Shift+X)
   - Click on the "..." menu in the top-right
   - Select "Install from VSIX..."
   - Choose the .vsix file you created

## Development

### Building the Extension

```bash
# Install dependencies
npm install

# Compile the extension
npm run compile

# Package the extension
vsce package
```

### Running the Extension

- Press F5 to open a new window with your extension loaded
- Run the command "Deep Research: Start New Research" from the command palette
- Set breakpoints in your code to debug the extension

## Troubleshooting

- If you encounter errors related to API keys, make sure your `.env` file is properly configured
- If the extension fails to load, check the Developer Tools console (Help > Toggle Developer Tools)
- For issues with the research process, check the Output panel (View > Output) and select "Deep Research" from the dropdown

## License

This extension is licensed under the MIT License.
