{"name": "deep-research-extension", "displayName": "Deep Research", "description": "Multi-Agent Research System - Powered by DELL XE9680 with AMD MI300X", "version": "0.1.0", "publisher": "dell-research", "icon": "media/icon.png", "engines": {"vscode": "^1.80.0"}, "categories": ["AI", "Machine Learning", "Other"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "deep-research.start", "title": "Deep Research: Start New Research"}, {"command": "extension.startDeepResearch", "title": "Start Deep Research"}], "keybindings": [{"command": "deep-research.start", "key": "ctrl+alt+r", "mac": "cmd+alt+r", "when": "editorTextFocus"}, {"command": "extension.startDeepResearch", "key": "ctrl+shift+alt+r", "mac": "cmd+shift+alt+r"}], "configuration": {"title": "Deep Research", "properties": {"deepResearch.useOpenAI": {"type": "boolean", "default": true, "description": "Use OpenAI models instead of Google models"}, "deepResearch.openaiApiKey": {"type": "string", "default": "123", "description": "API key for OpenAI"}, "deepResearch.openaiBaseUrl": {"type": "string", "default": "http://***********:4000", "description": "Base URL for OpenAI API"}, "deepResearch.openaiModelTool": {"type": "string", "default": "QwQ-32B", "description": "OpenAI model to use for tool operations"}, "deepResearch.openaiModelSummary": {"type": "string", "default": "DeepSeek-V3-0324", "description": "OpenAI model to use for summary generation"}, "deepResearch.exaApiKey": {"type": "string", "default": "", "description": "API key for Exa search"}, "deepResearch.geminiApiKey": {"type": "string", "default": "", "description": "API key for Google Gemini models"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js"}, "dependencies": {"@ai-sdk/google": "^1.2.13", "@ai-sdk/openai": "^1.3.18", "ai": "^4.3.9", "exa-js": "^1.6.13", "zod": "^3.23.8"}, "devDependencies": {"@types/glob": "^8.1.0", "@types/mocha": "^10.0.1", "@types/node": "^20.17.30", "@types/vscode": "^1.80.0", "@typescript-eslint/eslint-plugin": "^5.59.1", "@typescript-eslint/parser": "^5.59.1", "eslint": "^8.39.0", "glob": "^8.1.0", "mocha": "^10.2.0", "typescript": "^5.8.3"}}