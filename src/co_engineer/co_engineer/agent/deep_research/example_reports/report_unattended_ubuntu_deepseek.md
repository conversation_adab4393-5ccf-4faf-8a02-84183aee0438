Okay, I understand you're looking for a comprehensive guide on performing an unattended Ubuntu installation on bare metal servers using Redfish, incorporating network configuration (static IPs via NMState), and virtual media. Let's break this down into a detailed plan.

### Report: Unattended Ubuntu Installation on Bare Metal via Redfish

**I. Overview**

This report outlines the process of installing Ubuntu on bare metal servers in an unattended fashion, leveraging Redfish for virtual media attachment and NMState for static IP configuration. This approach is particularly useful in environments where PXE booting is not feasible or desired.

**II. Prerequisites**

1.  **Hardware:**
    *   Bare metal server(s) with Redfish-compliant BMC (Baseboard Management Controller).
    *   Sufficient storage for the Ubuntu installation.
2.  **Software/Tools:**
    *   Ubuntu Server ISO image.
    *   Redfish client (e.g., `curl`, `Redfish-Tools`, `python-redfish`).
    *   NMState configuration files.
    *   A provisioning server (can be a VM) to host the Ubuntu ISO and potentially serve as a DHCP/DNS server if needed.
3.  **Network:**
    *   A routable network for the bare metal servers.
    *   (Optional) A separate provisioning network.
    *   DNS server configured with appropriate records for the servers.
4.  **Redfish Access:**
    *   BMC IP address.
    *   BMC username and password.

**III. High-Level Steps**

1.  **Prepare the Ubuntu ISO:**  Customize the ISO with a preseed file for unattended installation.
2.  **Configure NMState:** Create NMState configuration files for static IP assignment.
3.  **Provisioning Server Setup:**  Host the ISO image on a web server (e.g., Nginx, Apache) on the provisioning server.
4.  **Redfish Virtual Media Attachment:** Use Redfish to attach the ISO image as a virtual CD-ROM to the bare metal server.
5.  **Boot from Virtual Media:** Configure the server to boot from the virtual CD-ROM via Redfish.
6.  **Unattended Installation:** The server boots from the ISO, the preseed file automates the installation, and NMState configures the network.
7.  **Post-Installation Configuration:**  Any additional configuration steps after the base OS is installed.

**IV. Detailed Steps**

1.  **Ubuntu ISO Customization (Preseeding)**

    *   **Purpose:** Automate the installation process, eliminating the need for manual intervention.
    *   **Method:** Create a `preseed.cfg` file.  This file contains answers to all the questions the Ubuntu installer would normally ask.
    *   **Example `preseed.cfg`:**

        ```
        # Locale settings
        d-i debian-installer/locale string en_US
        d-i console-setup/ask_vga boolean false
        d-i console-setup/layout string USA
        d-i console-setup/variant string USA

        # Keyboard selection
        d-i keyboard-configuration/xkb-keymap select us

        # Network configuration
        d-i netcfg/choose_interface select auto
        d-i netcfg/get_hostname string ubuntu-server
        d-i netcfg/get_domain string example.com

        # Mirror settings
        d-i mirror/country string US
        d-i mirror/http/hostname string us.archive.ubuntu.com
        d-i mirror/http/directory string /ubuntu
        d-i mirror/http/proxy string

        # Account setup
        d-i passwd/root-login boolean true
        d-i passwd/root-password password your_root_password
        d-i passwd/root-password-again password your_root_password
        d-i user-setup/allow-password-weak boolean true
        d-i user-setup/passwd password your_user_password
        d-i user-setup/passwd-again password your_user_password
        d-i user-setup/user-fullname string Your User
        d-i user-setup/user string your_user

        # Clock and time zone
        d-i clock-setup/utc boolean true
        d-i time/zone string America/New_York
        d-i clock-setup/ntp boolean true

        # Partitioning
        d-i partman-auto/method string lvm
        d-i partman-auto/disk string /dev/sda
        d-i partman-auto/lvm/new_vg string vg00
        d-i partman-auto/lvm/use_new_vg boolean true
        d-i partman-auto/choose_recipe select atomic
        d-i partman-lvm/confirm boolean true
        d-i partman-auto/expert_recipe string \
              atomic :: \
                      100 100 100 ext4 \
                              $primary{ } $bootable{ } \
                              method{ format } format{ } \
                              use_filesystem{ } filesystem{ ext4 } \
                              mountpoint{ /boot } . \
                      1000 10000 1000000 ext4 \
                              method{ format } format{ } \
                              use_filesystem{ } filesystem{ ext4 } \
                              mountpoint{ / } . \
                      100%FREE swap \
                              method{ swap } format{ } .
        d-i partman-auto/confirm_nooverwrite boolean true
        d-i partman-lvm/device_remove_lvm boolean true
        d-i partman-auto/confirm boolean true
        d-i partman/choose_partition select finish
        d-i partman/confirm boolean true

        # Package selection
        tasksel tasksel/first multiselect standard, ubuntu-server
        d-i pkgsel/include string openssh-server

        # Bootloader installation
        d-i grub-installer/only_debian boolean true
        d-i grub-installer/with_other_os boolean true
        d-i grub-installer/bootdev string /dev/sda

        # Finish installation
        d-i finish/reboot_now boolean true
        ```

    *   **Integrate Preseed into ISO:** There are several methods:
        *   **Modifying the ISO:** Use tools like `mkisofs` or `xorriso` to add the `preseed.cfg` file to the ISO and modify the boot configuration to load it.  This is more complex.
        *   **Using a Boot Parameter:**  The easiest method is to pass the location of the `preseed.cfg` file as a boot parameter.  This requires hosting the `preseed.cfg` file on a web server accessible during the installation.

2.  **NMState Configuration**

    *   **Purpose:**  Configure static IP addresses, DNS settings, and other network parameters automatically after the base OS is installed.
    *   **Method:** Create NMState YAML configuration files and place them in a location where NMState can find them during boot (e.g., `/etc/nmstate/`).
    *   **Example NMState Configuration (`/etc/nmstate/eth0.yaml`):**

        ```yaml
        ---
        interfaces:
          - name: eth0
            type: ethernet
            state: up
            ipv4:
              enabled: true
              address:
                - ip: ************
                  prefix-length: 24
              gateway: ***********
              dns:
                servers:
                  - *******
                  - *******
            ipv6:
              enabled: false
        ```

    *   **Important:**  Ensure the `nmstate` package is included in the `pkgsel/include` line of your `preseed.cfg` file.

3.  **Provisioning Server Setup**

    *   **Web Server:** Install and configure a web server (Nginx or Apache) to serve the Ubuntu ISO image and the `preseed.cfg` file (if using the boot parameter method).
    *   **Example Nginx Configuration:**

        ```nginx
        server {
            listen 80;
            server_name your_provisioning_server_ip;

            root /var/www/html;
            index index.html;

            location / {
                autoindex on;
            }
        }
        ```

        Place the Ubuntu ISO and `preseed.cfg` in `/var/www/html/`.
    *   **DHCP/DNS (Optional):** If you're not using static IPs exclusively, you might need a DHCP server to provide initial IP addresses to the bare metal servers.  A DNS server is crucial for name resolution.

4.  **Redfish Virtual Media Attachment**

    *   **Purpose:**  Mount the Ubuntu ISO image as a virtual CD-ROM on the bare metal server.
    *   **Method:** Use a Redfish client to interact with the BMC.  The specific commands will vary depending on the client you choose.
    *   **Example using `curl`:**

        ```bash
        # 1. Authenticate (replace with your BMC credentials)
        AUTH_TOKEN=$(curl -k -s -u "your_username:your_password" \
                         -H "Content-Type: application/json" \
                         -X POST https://your_bmc_ip/redfish/v1/SessionService/Sessions \
                         -d '{"UserName": "your_username", "Password": "your_password"}' | jq -r .Oem.OpenBMC.Session.Token)

        # 2. Get the Virtual Media URI
        VIRTUAL_MEDIA_URI=$(curl -k -s -H "X-Auth-Token: $AUTH_TOKEN" \
                                 https://your_bmc_ip/redfish/v1/Managers/bmc/VirtualMedia | jq -r '.Members[0]["@odata.id"]')

        # 3. Insert the ISO image
        curl -k -s -H "X-Auth-Token: $AUTH_TOKEN" \
             -H "Content-Type: application/json" \
             -X POST https://your_bmc_ip$VIRTUAL_MEDIA_URI/Actions/VirtualMedia.InsertMedia \
             -d '{"Image": "http://your_provisioning_server_ip/ubuntu.iso", "Inserted": true}'

        # 4. (Optional) Eject the media after installation
        # curl -k -s -H "X-Auth-Token: $AUTH_TOKEN" \
        #      -H "Content-Type: application/json" \
        #      -X POST https://your_bmc_ip$VIRTUAL_MEDIA_URI/Actions/VirtualMedia.EjectMedia
        ```

        *   **Important:**  Replace `your_username`, `your_password`, `your_bmc_ip`, `your_provisioning_server_ip`, and `ubuntu.iso` with your actual values.  You might need to adjust the `VirtualMedia URI` based on your BMC's Redfish implementation.  Use `jq` to parse the JSON responses.

5.  **Boot from Virtual Media**

    *   **Purpose:**  Configure the server to boot from the attached virtual CD-ROM.
    *   **Method:**  Use Redfish to set the boot source override.
    *   **Example using `curl`:**

        ```bash
        # 1. Get the Boot Source Override URI
        BOOT_URI=$(curl -k -s -H "X-Auth-Token: $AUTH_TOKEN" \
                       https://your_bmc_ip/redfish/v1/Systems/system | jq -r .<EMAIL>)

        # 2. Set the Boot Source Override
        curl -k -s -H "X-Auth-Token: $AUTH_TOKEN" \
             -H "Content-Type: application/json" \
             -X PATCH https://your_bmc_ip$BOOT_URI \
             -d '{"BootSourceOverrideTarget": "Cd", "BootSourceOverrideEnabled": "Once"}'
        ```

        *   `BootSourceOverrideTarget` can be `Cd`, `Hdd`, `Pxe`, etc., depending on your desired boot source.  `BootSourceOverrideEnabled` can be `Once` (boot once from the specified source) or `Continuous` (always boot from the specified source).

6.  **Unattended Installation**

    *   **Boot the Server:** Power on or reset the bare metal server.  It should now boot from the virtual CD-ROM.
    *   **Preseed Automation:** If the preseed file is correctly configured and accessible, the Ubuntu installer will run automatically, partitioning the disk, installing packages, and configuring the system based on the settings in `preseed.cfg`.
    *   **NMState Configuration:** After the base OS is installed, NMState will apply the network configuration from the YAML files in `/etc/nmstate/`, setting the static IP address, DNS servers, and other network parameters.
    *   **Boot Parameters (if using):** If you're using boot parameters to specify the preseed file, you'll need to append the following to the kernel command line during boot:

        ```
        url=http://your_provisioning_server_ip/preseed.cfg
        ```

        This can often be done through the BMC's remote console or by modifying the boot configuration via Redfish (more complex).

7.  **Post-Installation Configuration**

    *   **SSH Access:**  Verify that you can SSH into the server using the credentials specified in the `preseed.cfg` file.
    *   **Package Updates:**  Run `apt update && apt upgrade` to ensure the system is up-to-date.
    *   **Further Configuration:**  Install any additional software or configure services as needed.

**V. Troubleshooting**

*   **Redfish Errors:**  Check the Redfish event logs for any errors during virtual media attachment or boot source configuration.
*   **Network Connectivity:**  Verify that the server can reach the provisioning server and the internet (if required).
*   **Preseed Issues:**  Examine the installer logs (usually in `/var/log/syslog` after installation) for any errors related to the preseed file.  Test the preseed file in a VM first.
*   **NMState Errors:**  Check the NMState logs (`journalctl -u nmstate`) for any errors during network configuration.  Validate your NMState YAML files.
*   **Boot Order:**  Double-check the boot order in the BMC settings to ensure the server is booting from the virtual CD-ROM.

**VI. Alternatives and Considerations**

*   **iPXE:**  Consider using iPXE for more advanced network booting scenarios.
*   **Cloud-Init:**  Cloud-init is another popular tool for configuring cloud instances and bare metal servers.  It can be used in conjunction with or as an alternative to NMState.
*   **Ansible/Terraform:**  Use configuration management tools like Ansible or Terraform to automate the entire process, including Redfish interactions, OS installation, and post-installation configuration.
*   **Redfish Eventing:**  Implement Redfish event subscriptions to monitor the installation process and receive notifications when certain events occur (e.g., server booted, OS installed).

**VII. Addressing Follow-Up Questions**

1.  **How does the process differ when using Redfish virtual media for deploying without a provisioning network?**

    *   Without a provisioning network, the bare metal server relies entirely on the routable network for all communication.
    *   The `preseed.cfg` file (if hosted on a web server) must be accessible via the routable network.
    *   DHCP is still needed unless you're using static IPs configured via NMState.  If using DHCP, the DHCP server must be on the routable network.
    *   The Redfish client (used to attach the virtual media) must also be able to communicate with the BMC and the provisioning server via the routable network.

2.  **What are the specific steps to configure static IP addresses using NMState for bare metal nodes in an Ubuntu environment?**

    *   **Install NMState:** Ensure the `nmstate` package is included during the Ubuntu installation (via `preseed.cfg`).
    *   **Create NMState YAML Files:** Create YAML files (e.g., `/etc/nmstate/eth0.yaml`) that define the desired network configuration for each interface.  The example in Section IV.2 shows a basic configuration.
    *   **Placement of YAML Files:** Place the YAML files in `/etc/nmstate/`.  NMState will automatically apply these configurations during boot.
    *   **Verification:** After the server boots, verify the network configuration using `ip addr`, `ip route`, and `resolvectl status`.  Also, check the NMState logs (`journalctl -u nmstate`) for any errors.
    *   **Troubleshooting:** If the network configuration is not applied correctly, check the NMState logs for errors.  Validate the YAML syntax using `nmstatectl show --debug` and correct any errors.

**VIII. Speculative Enhancements**

*   **Automated Redfish Discovery:**  Develop a script to automatically discover Redfish-compliant BMCs on the network.
*   **Dynamic Preseed Generation:**  Create a system to dynamically generate `preseed.cfg` files based on server hardware characteristics (e.g., number of disks, amount of memory).
*   **Integration with Infrastructure-as-Code (IaC):**  Integrate the entire process into an IaC pipeline using tools like Terraform or Pulumi.  This would allow you to define the entire infrastructure (including bare metal servers) in code and automate the provisioning process.
*   **Secure Boot Integration:**  Explore ways to integrate secure boot with the unattended installation process to ensure the integrity of the OS.

This report provides a comprehensive guide to performing unattended Ubuntu installations on bare metal servers using Redfish and NMState. Remember to adapt the steps and configurations to your specific environment and requirements. Good luck!
