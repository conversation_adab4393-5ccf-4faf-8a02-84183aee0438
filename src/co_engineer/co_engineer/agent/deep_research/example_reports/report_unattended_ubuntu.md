## Report: Unattended Ubuntu Installation on Bare Metal Servers using Redfish

This report details how to perform unattended Ubuntu installations on bare metal servers using Redfish, based on the provided research data.

### 1. Overview

The research indicates that unattended Ubuntu installations on bare metal servers can be automated using the Redfish API.  Several tools and techniques facilitate this process, including:

*   **Redfish API:** A standard RESTful API for managing physical servers.
*   **Automation Tools:**  Tools like `redfish-server-automation` and `Badfish` leverage the Redfish API to automate server provisioning and management.
*   **Autoinstall Configuration:**  The `autoinstall.yaml` file provides a declarative way to configure the Ubuntu installation process.

### 2. Key Components and Technologies

#### 2.1. Redfish API

*   **Standard Interface:** Redfish provides a standardized, vendor-agnostic interface for interacting with server BMCs (Baseboard Management Controllers). This eliminates the need for vendor-specific tools and scripts.
*   **RESTful Architecture:** Redfish uses standard HTTP methods (GET, PATCH, PUT, POST) to manage server resources.
*   **HATEOAS (Hypermedia as the Engine of Application State):**  The API is discoverable, allowing clients to navigate the API by following links provided in the responses.
*   **Security:**  While the research data doesn't explicitly detail Redfish security mechanisms, it's crucial to implement proper authentication and authorization.  (See Section 5.1 for further discussion).

#### 2.2. Automation Tools

*   **redfish-server-automation (KarmaComputing):** This GitHub project aims to automate physical server provisioning using Redfish, similar to how software is provisioned. It likely uses Python and the Redfish API to perform tasks such as:
    *   Power management (power on, power off, reset).
    *   Boot order configuration.
    *   Virtual media attachment (for ISO-based installations).
    *   BIOS configuration.
*   **Badfish (Red Hat Performance):** A vendor-agnostic tool specifically designed for managing bare-metal systems via the Redfish API.  It focuses on Dell, SuperMicro, and HPE servers but can potentially work with any Redfish-compliant system. Key features include:
    *   Boot order management (BIOS and EFI).
    *   One-time boot configuration (PXE, specific devices, MAC addresses).
    *   Power control (reboot, power cycle, power on/off).
    *   Firmware inventory.
    *   BIOS attribute management.
    *   Virtual media management.
    *   Server configuration profile export/import.

#### 2.3. Autoinstall Configuration (autoinstall.yaml)

*   **YAML Format:**  The `autoinstall.yaml` file is a YAML-formatted file that defines the configuration for an unattended Ubuntu installation.
*   **Comprehensive Configuration:**  It allows configuring various aspects of the installation, including:
    *   User identity (username, password, hostname).
    *   Networking (static IP, DHCP).
    *   Storage (disk partitioning, LVM, encryption).
    *   Package installation.
    *   SSH configuration (install server, authorized keys).
    *   Timezone.
    *   Locale.
*   **Early and Late Commands:**  The `early-commands` section allows running commands before disk and network probing, enabling dynamic configuration. The `late-commands` section allows running commands after installation but before reboot.
*   **Security Considerations:**  The `autoinstall.yaml` file contains sensitive information (e.g., passwords, SSH keys).  It's crucial to protect this file during the installation process.

### 3. Unattended Installation Process

The general process for performing an unattended Ubuntu installation on a bare metal server using Redfish involves the following steps:

1.  **Prepare the `autoinstall.yaml` file:**
    *   Create an `autoinstall.yaml` file with the desired configuration.
    *   Securely store the file (e.g., on a trusted server).
2.  **Configure the Server's BMC (using Redfish):**
    *   Power on the server (using Redfish API).
    *   Configure the boot order to boot from a network device (PXE) or virtual media (ISO) (using Redfish API).
    *   If using virtual media, attach the Ubuntu installation ISO (using Redfish API).
3.  **Boot the Server:**
    *   Reboot the server (using Redfish API).
4.  **PXE Boot or ISO Boot:**
    *   If PXE booting, the server will obtain an IP address and boot from the network.
    *   If ISO booting, the server will boot from the attached ISO image.
5.  **Installer Starts:**
    *   The Ubuntu installer starts and retrieves the `autoinstall.yaml` file. This can be achieved using the `early-commands` section to download the file from a trusted server.
6.  **Unattended Installation:**
    *   The installer uses the `autoinstall.yaml` file to configure the system automatically.
7.  **Late Commands:**
    *   The `late-commands` section is executed after the installation is complete.
8.  **Reboot:**
    *   The server reboots into the newly installed Ubuntu system.

### 4. Detailed Configuration using `autoinstall.yaml`

Here's a sample `autoinstall.yaml` file with explanations:

```yaml
autoinstall:
  version: 1
  locale: en_US.UTF-8
  keyboard:
    layout: us
    variant: ""
    toggle: null
  identity:
    realname: "Ubuntu Server"
    username: ubuntu
    password: "$6$salt$hashed_password" # Replace with a properly hashed password
    hostname: my-server
  network:
    version: 2
    ethernets:
      enp0s3: # Replace with your network interface name
        dhcp4: true
        dhcp6: false
  ssh:
    install-server: true
    authorized-keys:
      - ssh-rsa AAAAB3NzaC1yc2E... <EMAIL> # Replace with your SSH public key
    allow-pw: false # Disable password authentication for SSH
  storage:
    layout:
      name: lvm
    password: "luks_password" # Replace with a strong LUKS password
  apt:
    preserve_sources_list: false
    mirror-selection:
      primary:
        - country-mirror
      fallback: abort
      geoip: true
  packages:
    - vim
    - curl
  timezone: Etc/UTC
  updates: security
  shutdown: reboot
  late-commands:
    - curtin in-target -- apt-get update
    - curtin in-target -- apt-get install -y some-package
```

**Explanation:**

*   **`version`:**  Specifies the autoinstall file version.
*   **`locale`:** Sets the system locale.
*   **`keyboard`:** Configures the keyboard layout.
*   **`identity`:**  Defines the initial user account.  **Important:**  Replace `"hashed_password"` with a properly generated password hash. Use a tool like `mkpasswd` or `openssl passwd`.
*   **`network`:** Configures networking.  This example uses DHCP on `enp0s3`.  Adjust the interface name as needed.
*   **`ssh`:**  Installs the OpenSSH server, adds an authorized key, and disables password authentication.  **Important:** Replace the example SSH key with your actual public key.
*   **`storage`:** Configures storage using LVM and LUKS encryption.  **Important:** Replace `"luks_password"` with a strong password.
*   **`apt`:** Configures APT repositories.  This example uses the country mirror and aborts if no mirror is available.
*   **`packages`:** Installs additional packages.
*   **`timezone`:** Sets the system timezone.
*   **`updates`:** Configures automatic updates.
*   **`shutdown`:** Specifies whether to reboot or power off after installation.
*   **`late-commands`:** Runs commands after installation.

### 5. Security Considerations

Security is paramount when performing unattended installations.  Here are some key security considerations:

#### 5.1. Redfish Authentication and Authorization

*   **Authentication:** Redfish implementations typically support authentication mechanisms such as:
    *   **Basic Authentication:**  Username and password.  **Not recommended** for production environments due to security concerns.
    *   **Session-based Authentication:**  Using tokens to authenticate subsequent requests.
    *   **Certificate-based Authentication:**  Using client certificates for authentication.
*   **Authorization:**  Redfish implementations should enforce authorization to restrict access to sensitive resources and actions.  Role-Based Access Control (RBAC) is a common approach.
*   **Secure Communication:**  Always use HTTPS (TLS) to encrypt communication between the Redfish client and the BMC.

#### 5.2. `autoinstall.yaml` Security

*   **Password Hashing:**  Never store passwords in plain text.  Always use strong password hashing algorithms (e.g., SHA-512) with a salt.
*   **SSH Key Management:**
    *   Use strong SSH key pairs (e.g., Ed25519).
    *   Regularly rotate SSH keys.
    *   Consider using SSH Certificate Authorities (CAs) for more robust key management.
*   **Secure File Transfer:**  When fetching the `autoinstall.yaml` file from a remote server, use HTTPS (TLS) to ensure confidentiality and integrity.
*   **File Permissions:**  Restrict access to the `autoinstall.yaml` file on the server where it's stored.
*   **Encryption:** Consider encrypting the `autoinstall.yaml` file itself. The `early-commands` can decrypt the file before the installer uses it.

#### 5.3. Network Security

*   **Isolate the Installation Network:**  Consider using a dedicated network for the installation process to minimize the risk of unauthorized access.
*   **Firewall:**  Configure a firewall to restrict access to the BMC and the installation server.

### 6. Potential Issues and Solutions

*   **Vendor-Specific Redfish Implementations:**  While Redfish aims to be a standard, vendors may implement extensions or deviate from the standard.
    *   **Solution:**  Use tools like `Badfish` that attempt to abstract away vendor differences.  Thoroughly test the installation process on different hardware platforms.
*   **`autoinstall.yaml` Retrieval Failures:**  If the installer cannot retrieve the `autoinstall.yaml` file, the installation will fail.
    *   **Solution:**  Implement robust error handling in the `early-commands` section.  Consider using a local copy of the `autoinstall.yaml` file as a fallback.
*   **Network Connectivity Issues:**  If the server cannot connect to the network, the installation will fail.
    *   **Solution:**  Ensure that the network is properly configured and that the server can obtain an IP address.
*   **Storage Configuration Errors:**  Incorrect storage configuration can lead to data loss or an unbootable system.
    *   **Solution:**  Carefully review the storage configuration in the `autoinstall.yaml` file.  Test the configuration thoroughly before deploying it to production.

### 7. Further Research and Considerations

*   **Specific Redfish API Calls:** Investigate the specific Redfish API calls used by `redfish-server-automation` and `Badfish` for tasks such as power management, boot order configuration, and virtual media attachment.
*   **Integration with Configuration Management Tools:** Explore how Redfish-based automation can be integrated with configuration management tools like Ansible, Chef, or Puppet.
*   **Dynamic Configuration:**  Investigate how to use Redfish and `autoinstall.yaml` to dynamically configure servers based on their hardware characteristics or role.
*   **TPM-backed encryption:** Explore the hybrid layout with encrypted set to yes for TPM-backed encryption.

### 8. Conclusion

Using Redfish in conjunction with `autoinstall.yaml` provides a powerful and flexible way to automate Ubuntu installations on bare metal servers. By carefully considering security implications and potential issues, you can create a robust and reliable unattended installation process.
