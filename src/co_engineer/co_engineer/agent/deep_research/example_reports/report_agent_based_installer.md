# Unattended Ubuntu Installation on Bare Metal Servers via RedFish API  
*Technical Report | Last Updated: 2025-04-25*  

## **1. Executive Summary**  
This report outlines a methodology for fully automated Ubuntu installation on bare metal servers using **RedFish API** (DMTF standard) for out-of-band management. The solution eliminates manual intervention by combining:  
- RedFish virtual media mounting  
- Kickstart/Preseed automation  
- Secure boot and firmware compatibility considerations  

## **2. Prerequisites**  
### **2.1 Hardware/Software Requirements**  
- **Server**: HPE iLO5, Dell iDRAC9, or other RedFish 1.6+ compliant BMC  
- **Ubuntu ISO**: 22.04 LTS or later (modified for unattended install)  
- **RedFish Client**: `curl`, `python-redfish`, or Ansible `community.general.redfish` module  
- **Network**: HTTP(S) server for hosting Kickstart/Preseed files  

### **2.2 Firmware Considerations**  
- **Secure Boot**: Disable or pre-load custom keys if using modified ISOs  
- **Virtual Media**: Ensure "Continuous" connection mode to prevent timeouts  

## **3. Step-by-Step Implementation**  

### **3.1 Prepare Unattended Installation Media**  
**Option A: Custom ISO with Embedded Preseed**  
```bash
# Download Ubuntu ISO and extract  
xorriso -osirrox on -indev ubuntu-22.04.3-live-server-amd64.iso \
  -extract / custom-iso  

# Inject preseed.cfg into ISO  
echo "d-i preseed/include string preseed.cfg" > custom-iso/isolinux/txt.cfg  

# Rebuild ISO  
xorriso -as mkisofs -r -V "Ubuntu Automated" -o auto-ubuntu.iso \
  -J -l -b isolinux/isolinux.bin -c isolinux/boot.cat \
  -no-emul-boot -boot-load-size 4 -boot-info-table custom-iso
```

**Option B: Network-Hosted Kickstart**  
```kickstart
# ks.cfg example  
url --url=http://********/ubuntu  
lang en_US  
keyboard us  
timezone UTC  
rootpw --iscrypted $6$hash  
user --name=admin --password=$6$hash --groups=sudo  
reboot
```

### **3.2 RedFish Automation Workflow**  

#### **Step 1: Authenticate to BMC**  
```python
import redfish

bmc = redfish.redfish_client(
    base_url='https://bmc-ip',
    username='admin',
    password='password',
    default_prefix='/redfish/v1'
)
bmc.login()
```

#### **Step 2: Mount Virtual Media**  
```python
# HTTP ISO hosted on internal web server  
virtual_media_uri = "http://********/auto-ubuntu.iso"

response = bmc.post(
    "/redfish/v1/Managers/1/VirtualMedia/CD/Actions/VirtualMedia.InsertMedia",
    body={"Image": virtual_media_uri, "Inserted": True}
)
```

#### **Step 3: Set Boot Order**  
```python
boot_override = {
    "Boot": {
        "BootSourceOverrideEnabled": "Once",
        "BootSourceOverrideTarget": "Cd",
        "BootSourceOverrideMode": "UEFI"
    }
}
bmc.patch("/redfish/v1/Systems/1", body=boot_override)
```

#### **Step 4: Power Cycle**  
```python
bmc.post("/redfish/v1/Systems/1/Actions/ComputerSystem.Reset", body={"ResetType": "ForceRestart"})
```

### **3.3 Post-Install Validation**  
- **RedFish Event Log**: Check for `POST Complete` events  
- **SSH Access**: Automated key injection via preseed  
- **API Verification**:  
  ```bash
  curl -k https://bmc-ip/redfish/v1/Systems/1 -u admin:password | jq .PowerState
  # Expected: "On" with POST code 0x0001
  ```

## **4. Advanced Considerations**  

### **4.1 Secure Boot & TPM Integration**  
- **Signed Bootloaders**: Modify ISO with `sbctl` for Secure Boot compliance  
- **TPM Measured Boot**: Extend PCRs during install for attestation  

### **4.2 Multi-Node Scaling**  
- **Ansible Playbook**:  
  ```yaml
  - name: Deploy Ubuntu via RedFish
    hosts: baremetal_servers
    tasks:
      - community.general.redfish_command:
          category: Systems
          command: Reset
          reset_type: ForceRestart
      - community.general.redfish_virtual_media:
          media_types: CD
          image_url: "http://deploy-server/auto-ubuntu.iso"
          boot_on_next_reset: true
  ```

### **4.3 Failure Recovery**  
- **Timeout Handling**: Set watchdog timer in Kickstart  
- **Fallback ISO**: Dual-bank BMC firmware with redundant virtual media  

## **5. Vendor-Specific Notes**  
| Vendor | Quirk | Workaround |  
|--------|-------|------------|  
| HPE | iLO5 requires HTTPS for virtual media | Use self-signed certs with `--insecure` |  
| Dell | iDRAC9 enforces ISO size limits | Split ISO or use compressed `squashfs` |  
| Supermicro | RedFish implementation varies by generation | Test with `redfishfirmware` utility |  

## **6. Alternative Approaches**  
- **PXE-less iPXE**: Chainload from RedFish to network boot  
  ```ipxe
  chain --autofree http://********/ipxe-script.php?mac=${net0/mac}
  ```  
- **OpenBMC Meta-Installers**: Leverage `obmc-flash` for low-level control  

## **7. Conclusion**  
This method provides a **100% hands-off** deployment pipeline suitable for edge computing and hyperscale environments. For mission-critical deployments, integrate with:  
- HashiCorp Vault for credential management  
- OpenTelemetry for install telemetry  
- SPIFFE/SPIRE for post-install identity provisioning  

**Next Steps**:  
1. Benchmark install times across 100+ nodes  
2. Evaluate ARM64 SBSA compliance for heterogeneous clusters  
3. Test with Ubuntu Core for immutable infrastructure  

Would you like me to elaborate on any specific component (e.g., Secure Boot details, multi-arch support)?