"""
Libvirt VM Creation and Management Agent.

This package provides an autogen agent for creating and managing VMs using libvirt and Ubuntu autoinstall.
"""

from co_engineer.agent.libvirt.agent import (
    create_agent,
    create_vm,
    wait_for_vm_installation,
    power_on_vm,
    get_vm_ip,
    ssh_to_vm,
    create_and_setup_vm
)

# Import CLI module
from co_engineer.agent.libvirt import cli

__all__ = [
    'create_agent',
    'create_vm',
    'wait_for_vm_installation',
    'power_on_vm',
    'get_vm_ip',
    'ssh_to_vm',
    'create_and_setup_vm',
    'cli'
]
