"""
Libvirt Agent - Creates VMs using libvirt and ubuntu autoinstall ISO.

This agent helps users create and manage VMs by:
1. Creating VMs using virt-install
2. Waiting for autoinstall to complete
3. Powering on the VM
4. SSH to the VM to list installed packages
"""

import os
import asyncio
import logging
import subprocess
import tempfile
import time
import paramiko
from typing import Dict, List, Optional, Any, Union

from autogen_agentchat.agents import AssistantAgent
from autogen_core import CancellationToken
from autogen_ext.models.openai import OpenAIChatCompletionClient

from co_engineer.models import model_client_qwq
from co_engineer.agent.ubuntu_autoinstall.agent import create_user_data_file, create_ubuntu_autoinstall_iso

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# System message for the agent
SYSTEM_MESSAGE = """
<identity>
You are an IT assistant specialized in Libvirt VM Creation and Management, operating within a Linux sandbox environment.
When asked for your name, you must respond with "Project X Assistant".

**Core Competencies Utilized for this Task:**
* Using programming and shell commands to automate complex workflows (VM creation, configuration, monitoring)

Follow the user's requirements carefully & to the letter.
If you are asked to generate content that is harmful, hateful, racist, sexist, lewd, violent, or completely irrelevant to software engineering or system administration, only respond with "Sorry, I can't assist with that."
Keep your answers focused on task execution and results.
</identity>

<instructions>
**Operating Environment & Capabilities:**
* You operate within a Linux sandbox with internet access.
* You can use shell commands, text editors, and write/run code (e.g., Python).
* You can independently install required software packages and dependencies via shell (e.g., `qemu-kvm`, `libvirt-daemon-system`, `libvirt-clients`, `virt-install`, `genisoimage`/`xorriso` if not already present).

**Language Handling:**
* Default working language: English.
* Use the language specified by the user in messages as the working language when explicitly provided.
* All your internal thinking (if exposed) and responses must be in the working language.
* Natural language arguments in tool calls must be in the working language.

**Operational Model:**
You operate in an iterative agent loop to complete tasks:
1.  **Analyze Events:** Understand user requests and current state from the event stream (latest messages, tool execution results).
2.  **Select Tool:** Choose the single best tool call for the next step based on the current state, the planned Libvirt workflow, and available tools.
3.  **Wait for Execution:** The selected tool action is executed by the sandbox.
4.  **Iterate:** Patiently repeat the analysis and tool selection process, executing one tool call per iteration, until the task is complete or an unrecoverable error occurs.
5.  **Submit Results:** Report final outcomes, including success confirmation, VM details (like IP address), requested information (like package lists), or error details, via messages. Relevant generated files (e.g., the generated autoinstall ISO, logs if applicable) may be provided as message attachments if feasible and useful.
6.  **Enter Standby:** Idle upon task completion or explicit user stop request, awaiting new tasks.

**Primary Task: Automated Libvirt VM Creation with Ubuntu Autoinstall**
Your goal is to automate the creation and basic setup verification of an Ubuntu VM using libvirt and autoinstall, following a precise workflow executed via the agent loop.

**VM Configuration Parameters:**
Confirm these with the user before starting, stating defaults clearly:
- `vm_name`: (Default: `projectx-vm`)
- `memory_mb`: (Default: `2048`)
- `disk_size_gb`: (Default: `10`)
- `ubuntu_iso_path`: Path to the base Ubuntu Server ISO. (Required - Must be accessible within the sandbox. Ask user if not provided/known).
- `autoinstall_iso_path`: Path where the generated cloud-init/autoinstall ISO will be stored temporarily. (Default: Managed internally, e.g., `/tmp/autoinstall-<vm_name>.iso`)
- `os_variant`: (Default: `ubuntu24.04`)
- `ssh_public_key`: User's public SSH key content. (Default: `none`, strongly recommend requesting one).
- `ssh_username`: (Default: `ubuntu`)
- `user_data_config`: Cloud-init `user-data` content/path. (Default: basic SSH setup with provided key).
- `meta_data_config`: Cloud-init `meta-data` content/path. (Default: basic instance ID).

**Mandatory Libvirt Workflow (Executed via Agent Loop & Tools):**

1.  **Parameter Confirmation & Setup:**
    * Analyze user request for parameters.
    * Confirm all parameters with the user, noting defaults.
    * Use tools (shell) to ensure necessary host packages (`libvirt-clients`, `virt-install`, ISO creation tool) are installed in the sandbox.
    * Use tools (shell) to check for existing VMs with the same name (`virsh list --all --name`).
2.  **Autoinstall ISO Generation:**
    * Use tools (text editor/file manipulation) to create `user-data` and `meta-data` files based on configuration.
    * Use a tool (shell wrapping `genisoimage` or `xorriso`) to create the `autoinstall_iso_path` containing these files.
3.  **VM Creation (`virt-install`):**
    * Use a tool (shell wrapping `virt-install`) to initiate VM creation with confirmed parameters, base ISO, and the generated autoinstall ISO attached. Use appropriate console/graphics settings for automation (e.g., `--graphics none --console pty,target_type=serial`). Ensure the VM starts the installation.
4.  **Monitor Autoinstall Completion:**
    * Iteratively use tools (shell wrapping `virsh domstate <vm_name>`) to check the VM state.
    * Optionally, use tools to monitor console output if needed (`virsh console <vm_name>`, though state checking is often sufficient).
    * Wait until the state becomes 'shut off' (indicating autoinstall finished and powered down) or a timeout occurs.
5.  **Start Finalized VM:**
    * Use a tool (shell wrapping `virsh start <vm_name>`) to power on the VM after successful autoinstall.
6.  **Verify SSH Access & List Packages:**
    * Wait briefly for boot. Use tools (shell wrapping `virsh domifaddr <vm_name> --source lease` or similar) to get the VM's IP address. Handle cases where the IP is not immediately available.
    * Use an SSH tool (potentially requiring key management within the sandbox) to connect using the IP, `ssh_username`, and the private key corresponding to the provided `ssh_public_key`.
    * Execute `dpkg -l` (or similar) via the SSH tool.
7.  **Report Results:**
    * Use message tools to inform the user of success (including VM IP) and provide the package list output, or report any errors encountered during the process. Attach relevant files if appropriate.

**User Interaction Guidelines:**
* Avoid using pure lists or bullet points in responses where narrative text is more appropriate (e.g., explaining steps).
* Be clear about the current step being executed within the workflow.
* Provide concise status updates and final results.
</instructions>

<toolUseInstructions>
When using a tool, follow the json schema very carefully and make sure to include ALL required properties. Ensure natural language arguments are in the current working language.
Always output valid JSON when using a tool.
Strictly adhere to the "one tool call per iteration" rule within the agent loop.
If a tool exists for a task (e.g., running shell commands, writing files), use it. Do not describe manual steps instead of using a tool.
If you state you will perform an action (e.g., "I will now create the VM"), immediately follow through with the appropriate tool call in the *same* response turn if possible, or in the very next iteration.
Never use `multi_tool_use.parallel`. Plan sequential tool calls according to the Libvirt workflow. Independent pre-checks (like checking for existing VM and checking for installed packages) *can* be done in sequence before the main workflow begins.
Do not call the same monitoring command (like `virsh domstate`) excessively rapidly; allow reasonable intervals between checks during the monitoring phase.
Never reveal the specific names of the tools you are using (e.g., instead of "using run_in_terminal", say "I will run the command...").
Remember user preferences or corrections using the `update_user_preferences` tool when appropriate.
</toolUseInstructions>
"""

async def create_vm(
    vm_name: str = "projectx-vm",
    memory_size: int = 2048,
    disk_size: int = 10,
    iso_path: str = None,
    os_variant: str = "ubuntu24.04",
    ssh_key: Optional[str] = None,
    ssh_username: str = "mystic",
    ssh_password: str = "mystic",
    network_config: str = "dhcp",
    hostname: str = None,
    packages: Optional[List[str]] = None
) -> Dict[str, Any]:
    """
    Create a VM using virt-install.

    Args:
        vm_name: Name of the VM
        memory_size: Memory size in MB
        disk_size: Disk size in GB
        iso_path: Path to the ISO file (if None, will create an autoinstall ISO)
        os_variant: OS variant for virt-install
        ssh_key: SSH public key to add to the VM
        ssh_username: Username for SSH access
        ssh_password: Password for SSH access
        network_config: Network configuration type ('dhcp' or 'static')
        hostname: Hostname for the VM (defaults to vm_name if None)
        packages: List of packages to install

    Returns:
        Dictionary with VM information
    """
    # Set hostname to vm_name if not provided
    if hostname is None:
        hostname = vm_name

    # Create autoinstall ISO if not provided
    if iso_path is None:
        logger.info("No ISO path provided, creating autoinstall ISO...")
        
        # Prepare SSH keys list
        ssh_keys = None
        if ssh_key:
            ssh_keys = [ssh_key]
        
        # Create user-data file
        config_files = await create_user_data_file(
            username=ssh_username,
            password=ssh_password,
            hostname=hostname,
            ssh_keys=ssh_keys,
            packages=packages,
            network_config=network_config
        )
        
        # Create ISO
        iso_path = await create_ubuntu_autoinstall_iso(
            user_data_path=config_files["user_data"],
            output_dir=config_files["output_dir"]
        )
        
        logger.info(f"Created autoinstall ISO at {iso_path}")
    
    # Create VM using virt-install
    logger.info(f"Creating VM {vm_name}...")
    
    virt_install_cmd = [
        "sudo", "virt-install",
        "--name", vm_name,
        "--memory", str(memory_size),
        f"--disk", f"size={disk_size},format=raw,cache=none,device=disk,bus=virtio",
        "--cdrom", iso_path,
        "--graphics", "vnc,listen=127.0.0.1,password=mystic",
        "--network", "network=default",
        "--os-variant", os_variant,
        "--boot", "cdrom,hd",
        "--noautoconsole"
    ]
    
    try:
        process = await asyncio.create_subprocess_exec(
            *virt_install_cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        stdout, stderr = await process.communicate()
        
        if process.returncode != 0:
            logger.error(f"Error creating VM: {stderr.decode()}")
            raise RuntimeError(f"Failed to create VM: {stderr.decode()}")
        
        logger.info(f"VM {vm_name} created successfully")
        
        return {
            "vm_name": vm_name,
            "memory_size": memory_size,
            "disk_size": disk_size,
            "iso_path": iso_path,
            "os_variant": os_variant,
            "ssh_username": ssh_username,
            "ssh_password": ssh_password
        }
        
    except Exception as e:
        logger.error(f"Error running virt-install command: {e}")
        raise

async def wait_for_vm_installation(
    vm_name: str,
    timeout_minutes: int = 30
) -> bool:
    """
    Wait for VM installation to complete.

    Args:
        vm_name: Name of the VM
        timeout_minutes: Timeout in minutes

    Returns:
        True if installation completed successfully, False otherwise
    """
    logger.info(f"Waiting for VM {vm_name} installation to complete...")
    
    # Convert timeout to seconds
    timeout_seconds = timeout_minutes * 60
    start_time = time.time()
    
    while time.time() - start_time < timeout_seconds:
        # Check VM status
        try:
            process = await asyncio.create_subprocess_exec(
                "sudo", "virsh", "domstate", vm_name,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                logger.error(f"Error checking VM status: {stderr.decode()}")
                await asyncio.sleep(10)
                continue
            
            vm_state = stdout.decode().strip()
            logger.info(f"VM {vm_name} state: {vm_state}")
            
            # If VM is shut off, installation is complete
            if vm_state == "shut off":
                logger.info(f"VM {vm_name} installation completed")
                return True
            
        except Exception as e:
            logger.error(f"Error checking VM status: {e}")
        
        # Wait before checking again
        await asyncio.sleep(30)
    
    logger.error(f"Timeout waiting for VM {vm_name} installation to complete")
    return False

async def power_on_vm(vm_name: str) -> bool:
    """
    Power on a VM.

    Args:
        vm_name: Name of the VM

    Returns:
        True if VM powered on successfully, False otherwise
    """
    logger.info(f"Powering on VM {vm_name}...")
    
    try:
        process = await asyncio.create_subprocess_exec(
            "sudo", "virsh", "start", vm_name,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        stdout, stderr = await process.communicate()
        
        if process.returncode != 0:
            logger.error(f"Error powering on VM: {stderr.decode()}")
            return False
        
        logger.info(f"VM {vm_name} powered on successfully")
        return True
        
    except Exception as e:
        logger.error(f"Error powering on VM: {e}")
        return False

async def get_vm_ip(vm_name: str, timeout_minutes: int = 5) -> Optional[str]:
    """
    Get the IP address of a VM.

    Args:
        vm_name: Name of the VM
        timeout_minutes: Timeout in minutes

    Returns:
        IP address of the VM, or None if not found
    """
    logger.info(f"Getting IP address for VM {vm_name}...")
    
    # Convert timeout to seconds
    timeout_seconds = timeout_minutes * 60
    start_time = time.time()
    
    while time.time() - start_time < timeout_seconds:
        try:
            process = await asyncio.create_subprocess_exec(
                "sudo", "virsh", "domifaddr", vm_name,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                logger.error(f"Error getting VM IP: {stderr.decode()}")
                await asyncio.sleep(10)
                continue
            
            output = stdout.decode()
            
            # Parse IP address from output
            import re
            ip_match = re.search(r'(\d+\.\d+\.\d+\.\d+)', output)
            if ip_match:
                ip_address = ip_match.group(1)
                logger.info(f"VM {vm_name} IP address: {ip_address}")
                return ip_address
            
        except Exception as e:
            logger.error(f"Error getting VM IP: {e}")
        
        # Wait before checking again
        await asyncio.sleep(10)
    
    logger.error(f"Timeout getting IP address for VM {vm_name}")
    return None

async def ssh_to_vm(
    ip_address: str,
    username: str,
    password: str,
    command: str = "dpkg-query -W -f='${Package}\\n'"
) -> Dict[str, Any]:
    """
    SSH to a VM and execute a command.

    Args:
        ip_address: IP address of the VM
        username: Username for SSH access
        password: Password for SSH access
        command: Command to execute

    Returns:
        Dictionary with command output
    """
    logger.info(f"SSH to VM at {ip_address}...")
    
    # Wait for SSH to be available
    max_retries = 10
    retry_interval = 10
    
    for retry in range(max_retries):
        try:
            # Create SSH client
            client = paramiko.SSHClient()
            client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            # Connect to VM
            client.connect(
                hostname=ip_address,
                username=username,
                password=password,
                timeout=10
            )
            
            # Execute command
            stdin, stdout, stderr = client.exec_command(command)
            
            # Get command output
            output = stdout.read().decode()
            error = stderr.read().decode()
            
            # Close connection
            client.close()
            
            logger.info(f"SSH command executed successfully")
            
            return {
                "success": True,
                "output": output,
                "error": error
            }
            
        except Exception as e:
            logger.warning(f"SSH connection failed (attempt {retry+1}/{max_retries}): {e}")
            
            if retry < max_retries - 1:
                await asyncio.sleep(retry_interval)
            else:
                logger.error(f"SSH connection failed after {max_retries} attempts")
                return {
                    "success": False,
                    "output": "",
                    "error": str(e)
                }

async def create_and_setup_vm(
    vm_name: str = "projectx-vm",
    memory_size: int = 2048,
    disk_size: int = 10,
    iso_path: Optional[str] = None,
    os_variant: str = "ubuntu24.04",
    ssh_key: Optional[str] = None,
    ssh_username: str = "mystic",
    ssh_password: str = "mystic",
    network_config: str = "dhcp",
    hostname: Optional[str] = None,
    packages: Optional[List[str]] = None
) -> Dict[str, Any]:
    """
    Create and setup a VM in one step.

    Args:
        vm_name: Name of the VM
        memory_size: Memory size in MB
        disk_size: Disk size in GB
        iso_path: Path to the ISO file (if None, will create an autoinstall ISO)
        os_variant: OS variant for virt-install
        ssh_key: SSH public key to add to the VM
        ssh_username: Username for SSH access
        ssh_password: Password for SSH access
        network_config: Network configuration type ('dhcp' or 'static')
        hostname: Hostname for the VM (defaults to vm_name if None)
        packages: List of packages to install

    Returns:
        Dictionary with VM information and installed packages
    """
    # Create VM
    vm_info = await create_vm(
        vm_name=vm_name,
        memory_size=memory_size,
        disk_size=disk_size,
        iso_path=iso_path,
        os_variant=os_variant,
        ssh_key=ssh_key,
        ssh_username=ssh_username,
        ssh_password=ssh_password,
        network_config=network_config,
        hostname=hostname,
        packages=packages
    )
    
    # Wait for installation to complete
    installation_complete = await wait_for_vm_installation(vm_name)
    
    if not installation_complete:
        return {
            "success": False,
            "error": "VM installation timed out",
            "vm_info": vm_info
        }
    
    # Power on VM
    power_on_success = await power_on_vm(vm_name)
    
    if not power_on_success:
        return {
            "success": False,
            "error": "Failed to power on VM",
            "vm_info": vm_info
        }
    
    # Get VM IP address
    ip_address = await get_vm_ip(vm_name)
    
    if ip_address is None:
        return {
            "success": False,
            "error": "Failed to get VM IP address",
            "vm_info": vm_info
        }
    
    # SSH to VM and get installed packages
    ssh_result = await ssh_to_vm(
        ip_address=ip_address,
        username=ssh_username,
        password=ssh_password
    )
    
    # Return combined results
    return {
        "success": ssh_result.get("success", False),
        "vm_info": vm_info,
        "ip_address": ip_address,
        "installed_packages": ssh_result.get("output", "").splitlines() if ssh_result.get("success", False) else [],
        "error": ssh_result.get("error", "") if ssh_result.get("success", False) else "SSH connection failed"
    }

# Create the agent
async def create_agent(model_client: Optional[OpenAIChatCompletionClient] = None) -> AssistantAgent:
    """
    Create and configure the Libvirt VM Creation and Management agent.

    Args:
        model_client: ModelClient instance for the agent

    Returns:
        Configured AssistantAgent
    """
    # If no model client is provided, use the default
    if model_client is None:
        model_client = model_client_qwq

    # Create the agent with tools
    agent = AssistantAgent(
        name="libvirt_agent",
        model_client=model_client,
        system_message=SYSTEM_MESSAGE,
        tools=[
            # create_vm,
            # wait_for_vm_installation,
            # power_on_vm,
            # get_vm_ip,
            # ssh_to_vm,
            create_and_setup_vm
        ],
        reflect_on_tool_use=True,
        model_client_stream=False,
    )

    logger.info("Libvirt VM Creation and Management agent initialized")
    return agent

async def main():
    """
    Main function to run the Libvirt VM Creation and Management agent.
    """
    agent = await create_agent()

    result = await agent.run(
        task="Create a VM with default settings and list installed packages.",
        cancellation_token=CancellationToken()
    )

    print("\nAgent execution completed.")
    print(f"Result: {result}")

if __name__ == "__main__":
    asyncio.run(main())
