"""
Command-line interface for the Libvirt VM Creation and Management agent.
"""

import os
import sys
import asyncio
import argparse
import logging
from typing import List, Optional

from autogen_core import CancellationToken
from autogen_agentchat.ui import Console

from co_engineer.agent.libvirt.agent import create_agent

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def interactive_mode():
    """
    Run the agent in interactive mode, allowing the user to chat with the agent.
    """
    agent = await create_agent()

    print("=== Libvirt VM Creation and Management Agent ===")
    print("Type 'exit' or 'quit' to end the conversation.")
    print("Example: Create a VM with 4GB RAM and 20GB disk, then list installed packages.")
    print("=" * 50)

    while True:
        user_input = input("\nYou: ")
        if user_input.lower() in ["exit", "quit"]:
            break

        await Console(
            agent.run_stream(
                task=user_input,
                cancellation_token=CancellationToken()
            )
        )

async def direct_mode(args):
    """
    Run the agent in direct mode, creating a VM with the specified parameters.

    Args:
        args: Command-line arguments
    """
    # Parse SSH keys
    ssh_key = None
    if args.ssh_key:
        ssh_key = args.ssh_key.strip()

    # Parse packages
    packages = None
    if args.packages:
        packages = [pkg.strip() for pkg in args.packages.split(",")]

    # Parse DNS servers
    dns_servers = None
    if args.dns_servers:
        dns_servers = [dns.strip() for dns in args.dns_servers.split(",")]

    # Set output directory for ISO if needed
    output_dir = args.output_dir if args.output_dir else os.getcwd()

    # Create the agent
    agent = await create_agent()

    # Construct task parameters
    task_params = {
        "vm_name": args.vm_name,
        "memory_size": args.memory_size,
        "disk_size": args.disk_size,
        "os_variant": args.os_variant,
        "ssh_username": args.username,
        "ssh_password": args.password,
        "network_config": args.network_config,
        "hostname": args.hostname if args.hostname else args.vm_name,
        "output_dir": output_dir
    }

    # Add optional parameters if provided
    if args.iso_path:
        task_params["iso_path"] = args.iso_path
    if ssh_key:
        task_params["ssh_key"] = ssh_key
    if packages:
        task_params["packages"] = packages

    # Add static network parameters if applicable
    if args.network_config == "static":
        task_params.update({
            "ip_address": args.ip_address,
            "netmask": args.netmask,
            "gateway": args.gateway,
            "dns_servers": dns_servers
        })
        if args.mac_address:
            task_params["mac_address"] = args.mac_address

    # Create task description
    task_description = f" Create and setup a VM with the following parameters: {task_params}"

    print(f"Creating VM '{args.vm_name}' with {args.memory_size}MB RAM and {args.disk_size}GB disk...")

    # Run the agent with the task
    result = await agent.run(
        task=task_description,
        cancellation_token=CancellationToken()
    )

    print("\nAgent execution completed.")

    # Extract the result from the agent's response if available
    if result and hasattr(result, 'messages') and result.messages:
        last_message = result.messages[-1]
        if hasattr(last_message, 'content'):
            print(f"\nAgent result: {last_message.content}")

    print(f"\nVM creation task completed for '{args.vm_name}'.")

async def vm_operations_mode(args):
    """
    Run VM operations like power on, get IP, or SSH using the agent.

    Args:
        args: Command-line arguments
    """
    # Create the agent
    agent = await create_agent()

    # Construct task description based on operation
    if args.operation == "power-on":
        task_description = f"Power on the VM named '{args.vm_name}'."
        print(f"Powering on VM '{args.vm_name}'...")

    elif args.operation == "get-ip":
        task_description = f"Get the IP address of the VM named '{args.vm_name}' with a timeout of {args.timeout} minutes."
        print(f"Getting IP address for VM '{args.vm_name}'...")

    elif args.operation == "ssh":
        # Use custom command if provided, otherwise list packages
        command = args.command if args.command else "dpkg-query -W -f='${Package}\\n'"
        task_description = f"SSH to the VM named '{args.vm_name}' with username '{args.username}' and password '{args.password}', and execute the command: {command}"
        print(f"SSH to VM '{args.vm_name}'...")

    # Run the agent with the task
    result = await agent.run(
        task=task_description,
        cancellation_token=CancellationToken()
    )

    print("\nAgent execution completed.")

    # Extract the result from the agent's response if available
    if result and hasattr(result, 'messages') and result.messages:
        last_message = result.messages[-1]
        if hasattr(last_message, 'content'):
            print(f"\nAgent result: {last_message.content}")

    print(f"\nVM operation '{args.operation}' completed for '{args.vm_name}'.")

def main():
    """
    Main function to parse command-line arguments and run the appropriate mode.
    """
    parser = argparse.ArgumentParser(description="Libvirt VM Creation and Management")
    subparsers = parser.add_subparsers(dest="mode", help="Mode of operation")

    # Interactive mode (default)
    subparsers.add_parser("interactive", help="Interactive mode with the agent")

    # Create VM mode
    create_parser = subparsers.add_parser("create", help="Create a VM directly")
    create_parser.add_argument("--vm-name", default="projectx-vm", help="Name of the VM")
    create_parser.add_argument("--memory-size", type=int, default=2048, help="Memory size in MB")
    create_parser.add_argument("--disk-size", type=int, default=10, help="Disk size in GB")
    create_parser.add_argument("--iso-path", help="Path to the ISO file (if not provided, will create an autoinstall ISO)")
    create_parser.add_argument("--os-variant", default="ubuntu24.04", help="OS variant for virt-install")
    create_parser.add_argument("--hostname", help="Hostname for the VM (defaults to VM name if not provided)")
    create_parser.add_argument("--username", default="mystic", help="Username for SSH access")
    create_parser.add_argument("--password", default="mystic", help="Password for SSH access")
    create_parser.add_argument("--ssh-key", help="SSH public key to add to the VM")
    create_parser.add_argument("--packages", help="Comma-separated list of packages to install")
    create_parser.add_argument("--network-config", default="dhcp", choices=["dhcp", "static"], help="Network configuration type")
    create_parser.add_argument("--ip-address", help="Static IP address (required if network-config is 'static')")
    create_parser.add_argument("--netmask", help="Network mask in CIDR format (required if network-config is 'static')")
    create_parser.add_argument("--gateway", help="Default gateway (required if network-config is 'static')")
    create_parser.add_argument("--dns-servers", help="Comma-separated list of DNS servers (required if network-config is 'static')")
    create_parser.add_argument("--mac-address", help="MAC address for network interface (optional for static config)")
    create_parser.add_argument("--output-dir", help="Directory to save the autoinstall ISO if created")

    # VM operations mode
    ops_parser = subparsers.add_parser("vm-ops", help="Perform operations on existing VMs")
    ops_parser.add_argument("operation", choices=["power-on", "get-ip", "ssh"], help="Operation to perform")
    ops_parser.add_argument("--vm-name", required=True, help="Name of the VM")
    ops_parser.add_argument("--username", default="mystic", help="Username for SSH access (only for 'ssh' operation)")
    ops_parser.add_argument("--password", default="mystic", help="Password for SSH access (only for 'ssh' operation)")
    ops_parser.add_argument("--command", help="Command to execute via SSH (only for 'ssh' operation)")
    ops_parser.add_argument("--timeout", type=int, default=5, help="Timeout in minutes for operations")

    args = parser.parse_args()

    # Default to interactive mode if no mode specified
    if not args.mode:
        args.mode = "interactive"

    # Validate static network configuration
    if args.mode == "create" and args.network_config == "static":
        if not all([args.ip_address, args.netmask, args.gateway, args.dns_servers]):
            parser.error("Static network configuration requires --ip-address, --netmask, --gateway, and --dns-servers")

    # Run the selected mode
    if args.mode == "interactive":
        asyncio.run(interactive_mode())
    elif args.mode == "create":
        asyncio.run(direct_mode(args))
    elif args.mode == "vm-ops":
        asyncio.run(vm_operations_mode(args))

if __name__ == "__main__":
    main()
