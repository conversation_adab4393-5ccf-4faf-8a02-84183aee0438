#!/usr/bin/env python3
"""
Example script demonstrating how to use the Libvirt VM Creation and Management agent.
"""

import asyncio
import argparse
import logging
from typing import List, Optional

from autogen_core import CancellationToken
from co_engineer.agent.libvirt import create_agent, create_and_setup_vm

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def run_agent(task: str):
    """
    Run the libvirt agent with a specific task.
    
    Args:
        task: The task to run
    """
    agent = await create_agent()
    
    result = await agent.run(
        task=task,
        cancellation_token=CancellationToken()
    )
    
    print("\nAgent execution completed.")
    print(f"Result: {result}")

async def create_vm_directly(
    vm_name: str,
    memory_size: int,
    disk_size: int,
    iso_path: Optional[str],
    os_variant: str,
    ssh_key: Optional[str],
    ssh_username: str,
    ssh_password: str,
    packages: Optional[List[str]]
):
    """
    Create a VM directly using the create_and_setup_vm function.
    
    Args:
        vm_name: Name of the VM
        memory_size: Memory size in MB
        disk_size: Disk size in GB
        iso_path: Path to the ISO file
        os_variant: OS variant for virt-install
        ssh_key: SSH public key to add to the VM
        ssh_username: Username for SSH access
        ssh_password: Password for SSH access
        packages: List of packages to install
    """
    result = await create_and_setup_vm(
        vm_name=vm_name,
        memory_size=memory_size,
        disk_size=disk_size,
        iso_path=iso_path,
        os_variant=os_variant,
        ssh_key=ssh_key,
        ssh_username=ssh_username,
        ssh_password=ssh_password,
        packages=packages
    )
    
    print("\nVM creation completed.")
    
    if result["success"]:
        print(f"VM Name: {result['vm_info']['vm_name']}")
        print(f"IP Address: {result['ip_address']}")
        print(f"Installed Packages: {len(result['installed_packages'])} packages")
        
        # Print first 10 packages as example
        if result['installed_packages']:
            print("\nSample of installed packages:")
            for pkg in result['installed_packages'][:10]:
                print(f"  - {pkg}")
            
            if len(result['installed_packages']) > 10:
                print(f"  ... and {len(result['installed_packages']) - 10} more")
    else:
        print(f"Error: {result['error']}")

def main():
    """
    Main function to parse arguments and run the example.
    """
    parser = argparse.ArgumentParser(description="Libvirt VM Creation and Management Example")
    
    subparsers = parser.add_subparsers(dest="command", help="Command to run")
    
    # Agent command
    agent_parser = subparsers.add_parser("agent", help="Run the libvirt agent")
    agent_parser.add_argument("--task", type=str, default="Create a VM with default settings and list installed packages.",
                             help="Task to run")
    
    # Create VM command
    create_parser = subparsers.add_parser("create", help="Create a VM directly")
    create_parser.add_argument("--name", type=str, default="projectx-vm",
                              help="Name of the VM")
    create_parser.add_argument("--memory", type=int, default=2048,
                              help="Memory size in MB")
    create_parser.add_argument("--disk", type=int, default=10,
                              help="Disk size in GB")
    create_parser.add_argument("--iso", type=str, default=None,
                              help="Path to the ISO file")
    create_parser.add_argument("--os-variant", type=str, default="ubuntu24.04",
                              help="OS variant for virt-install")
    create_parser.add_argument("--ssh-key", type=str, default=None,
                              help="SSH public key to add to the VM")
    create_parser.add_argument("--username", type=str, default="mystic",
                              help="Username for SSH access")
    create_parser.add_argument("--password", type=str, default="mystic",
                              help="Password for SSH access")
    create_parser.add_argument("--packages", type=str, nargs="+", default=None,
                              help="List of packages to install")
    
    args = parser.parse_args()
    
    if args.command == "agent":
        asyncio.run(run_agent(args.task))
    elif args.command == "create":
        asyncio.run(create_vm_directly(
            vm_name=args.name,
            memory_size=args.memory,
            disk_size=args.disk,
            iso_path=args.iso,
            os_variant=args.os_variant,
            ssh_key=args.ssh_key,
            ssh_username=args.username,
            ssh_password=args.password,
            packages=args.packages
        ))
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
