# Libvirt VM Creation and Management Agent

This agent helps users create and manage VMs using libvirt and Ubuntu autoinstall. It provides a complete workflow for VM creation, installation, and package management.

## Features

- Create VMs using virt-install
- Generate Ubuntu autoinstall ISOs for automated installation
- Wait for installation to complete
- Power on VMs
- SSH to VMs to list installed packages
- Support for custom configurations (memory, disk size, SSH keys, etc.)

## Workflow

1. Create VM using virt-install
2. Wait for autoinstall to complete
3. Power on the VM
4. SSH to the VM to list installed packages

## Usage

### A2A Server

The agent provides an Agent-to-Agent (A2A) server for creating and managing VMs:

```bash
# Start the server on localhost:10002 (default)
uv run co_engineer/agent/libvirt/a2a_server.py

# Start the server on a custom host and port
uv run co_engineer/agent/libvirt/a2a_server.py --host 0.0.0.0 --port 10002
```

#### Connecting to the A2A Server

In a separate terminal, run the A2A client:

```bash
cd x/src/co_engineer
source .venv/bin/activate
uv run co_engineer/agent/a2a/hosts/cli --agent http://localhost:10002
```

#### Example Requests

Once connected to the A2A server, you can send requests like:

```
What do you want to send to the agent? (:q or quit to exit)
Create a VM with 4GB RAM and 20GB disk
```

```
What do you want to send to the agent? (:q or quit to exit)
Power on my VM named 'web-server'
```

```
What do you want to send to the agent? (:q or quit to exit)
SSH to my VM named 'database-server' and list installed packages
```

### Command-Line Interface

The agent also provides a command-line interface (CLI) for easy interaction. The CLI supports three modes of operation:

### Interactive Mode

Interactive mode allows you to chat with the agent and provide instructions in natural language:

```bash
# Run in interactive mode (default)
uv run co_engineer/agent/libvirt/cli.py interactive
```

### Create VM Mode

Create a VM directly with specific parameters:

```bash
# Create a VM with custom settings
uv run co_engineer/agent/libvirt/cli.py create \
    --vm-name my-server \
    --memory-size 4096 \
    --disk-size 20 \
    --username admin \
    --password secure123 \
    --iso-path my-ubuntu-autoinstall.iso
```

### VM Operations Mode

Perform operations on existing VMs:

```bash
# Power on a VM
uv run co_engineer/agent/libvirt/cli.py vm-ops power-on --vm-name my-server

# Get the IP address of a VM
uv run co_engineer/agent/libvirt/cli.py get-ip --vm-name my-server

# SSH to a VM and execute a command
uv run co_engineer/agent/libvirt/cli.py vm-ops ssh \
    --vm-name my-server \
    --username admin \
    --password secure123 \
    --command "ls -la /var/log"
```

### CLI Options

#### Create VM Options

- `--vm-name`: Name of the VM (default: projectx-vm)
- `--memory-size`: Memory size in MB (default: 2048)
- `--disk-size`: Disk size in GB (default: 10)
- `--iso-path`: Path to the ISO file (optional)
- `--os-variant`: OS variant for virt-install (default: ubuntu24.04)
- `--hostname`: Hostname for the VM (defaults to VM name)
- `--username`: Username for SSH access (default: mystic)
- `--password`: Password for SSH access (default: mystic)
- `--ssh-key`: SSH public key to add to the VM
- `--packages`: Comma-separated list of packages to install
- `--network-config`: Network configuration type ('dhcp' or 'static')
- `--ip-address`, `--netmask`, `--gateway`, `--dns-servers`: Static network configuration
- `--mac-address`: MAC address for network interface
- `--output-dir`: Directory to save the autoinstall ISO if created

#### VM Operations Options

- `operation`: Operation to perform (power-on, get-ip, ssh)
- `--vm-name`: Name of the VM
- `--username`: Username for SSH access (only for 'ssh' operation)
- `--password`: Password for SSH access (only for 'ssh' operation)
- `--command`: Command to execute via SSH (only for 'ssh' operation)
- `--timeout`: Timeout in minutes for operations (default: 5)

## Python API Usage

```python
import asyncio
from co_engineer.agent.libvirt import create_agent, create_and_setup_vm

# Create and use the agent
async def main():
    agent = await create_agent()

    result = await agent.run(
        task="Create a VM with 4GB RAM and 20GB disk, then list installed packages.",
        cancellation_token=CancellationToken()
    )

    print(f"Result: {result}")

# Or use the functions directly
async def create_vm_directly():
    result = await create_and_setup_vm(
        vm_name="my-vm",
        memory_size=4096,
        disk_size=20,
        ssh_username="admin",
        ssh_password="secure_password",
        packages=["nginx", "python3"]
    )

    print(f"VM created: {result['success']}")
    print(f"IP address: {result['ip_address']}")
    print(f"Installed packages: {result['installed_packages']}")

if __name__ == "__main__":
    asyncio.run(main())
```

## Configuration Options

- VM name (default: projectx-vm)
- Memory size (default: 2048 MB)
- Disk size (default: 10 GB)
- Ubuntu ISO path (default: generated autoinstall ISO)
- OS variant (default: ubuntu24.04)
- SSH key (default: none)
- SSH username (default: mystic)
- SSH password (default: mystic)
- Network configuration (default: dhcp)
- Hostname (default: same as VM name)
- Packages to install (default: none)

## Requirements

- libvirt and virt-install installed on the host system
- Sufficient permissions to create and manage VMs (sudo access)
- Python 3.8+ with asyncio and paramiko
- Ubuntu autoinstall agent from co_engineer package
