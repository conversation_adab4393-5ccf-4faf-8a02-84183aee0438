#!/usr/bin/env python3
"""
Tests for the Libvirt VM Creation and Management agent.
"""

import unittest
import asyncio
from unittest.mock import patch, MagicMock, AsyncMock

from co_engineer.agent.libvirt.agent import (
    create_vm,
    wait_for_vm_installation,
    power_on_vm,
    get_vm_ip,
    ssh_to_vm,
    create_and_setup_vm,
    create_agent
)

class TestLibvirtAgent(unittest.TestCase):
    """Test cases for the Libvirt agent."""
    
    @patch('co_engineer.agent.libvirt.agent.asyncio.create_subprocess_exec')
    @patch('co_engineer.agent.libvirt.agent.create_user_data_file')
    @patch('co_engineer.agent.libvirt.agent.create_ubuntu_autoinstall_iso')
    async def test_create_vm(self, mock_create_iso, mock_create_user_data, mock_subprocess):
        """Test creating a VM."""
        # Mock subprocess
        process_mock = AsyncMock()
        process_mock.returncode = 0
        process_mock.communicate.return_value = (b"Domain created", b"")
        mock_subprocess.return_value = process_mock
        
        # Mock user-data and ISO creation
        mock_create_user_data.return_value = {"user_data": "/tmp/user-data", "output_dir": "/tmp"}
        mock_create_iso.return_value = "/tmp/ubuntu-autoinstall.iso"
        
        # Call create_vm
        result = await create_vm(
            vm_name="test-vm",
            memory_size=1024,
            disk_size=5
        )
        
        # Check result
        self.assertEqual(result["vm_name"], "test-vm")
        self.assertEqual(result["memory_size"], 1024)
        self.assertEqual(result["disk_size"], 5)
        self.assertEqual(result["iso_path"], "/tmp/ubuntu-autoinstall.iso")
        
        # Verify subprocess was called with correct arguments
        mock_subprocess.assert_called_once()
        args = mock_subprocess.call_args[0]
        self.assertEqual(args[0], "sudo")
        self.assertEqual(args[1], "virt-install")
        self.assertIn("--name", args)
        self.assertIn("test-vm", args)
    
    @patch('co_engineer.agent.libvirt.agent.asyncio.create_subprocess_exec')
    async def test_wait_for_vm_installation(self, mock_subprocess):
        """Test waiting for VM installation."""
        # Mock subprocess to return "shut off" state
        process_mock = AsyncMock()
        process_mock.returncode = 0
        process_mock.communicate.return_value = (b"shut off", b"")
        mock_subprocess.return_value = process_mock
        
        # Call wait_for_vm_installation with short timeout
        result = await wait_for_vm_installation("test-vm", timeout_minutes=1)
        
        # Check result
        self.assertTrue(result)
        
        # Verify subprocess was called
        mock_subprocess.assert_called_once()
    
    @patch('co_engineer.agent.libvirt.agent.asyncio.create_subprocess_exec')
    async def test_power_on_vm(self, mock_subprocess):
        """Test powering on a VM."""
        # Mock subprocess
        process_mock = AsyncMock()
        process_mock.returncode = 0
        process_mock.communicate.return_value = (b"Domain started", b"")
        mock_subprocess.return_value = process_mock
        
        # Call power_on_vm
        result = await power_on_vm("test-vm")
        
        # Check result
        self.assertTrue(result)
        
        # Verify subprocess was called with correct arguments
        mock_subprocess.assert_called_once()
        args = mock_subprocess.call_args[0]
        self.assertEqual(args[0], "sudo")
        self.assertEqual(args[1], "virsh")
        self.assertEqual(args[2], "start")
        self.assertEqual(args[3], "test-vm")
    
    @patch('co_engineer.agent.libvirt.agent.asyncio.create_subprocess_exec')
    async def test_get_vm_ip(self, mock_subprocess):
        """Test getting VM IP address."""
        # Mock subprocess to return IP address
        process_mock = AsyncMock()
        process_mock.returncode = 0
        process_mock.communicate.return_value = (b"vnet0 ***************", b"")
        mock_subprocess.return_value = process_mock
        
        # Call get_vm_ip with short timeout
        result = await get_vm_ip("test-vm", timeout_minutes=1)
        
        # Check result
        self.assertEqual(result, "***************")
        
        # Verify subprocess was called
        mock_subprocess.assert_called_once()
    
    @patch('co_engineer.agent.libvirt.agent.paramiko.SSHClient')
    async def test_ssh_to_vm(self, mock_ssh_client):
        """Test SSH to VM."""
        # Mock SSH client
        client_mock = MagicMock()
        mock_ssh_client.return_value = client_mock
        
        # Mock exec_command
        stdout_mock = MagicMock()
        stdout_mock.read.return_value = b"package1\npackage2\npackage3"
        stderr_mock = MagicMock()
        stderr_mock.read.return_value = b""
        client_mock.exec_command.return_value = (None, stdout_mock, stderr_mock)
        
        # Call ssh_to_vm
        result = await ssh_to_vm(
            ip_address="***************",
            username="test",
            password="test"
        )
        
        # Check result
        self.assertTrue(result["success"])
        self.assertEqual(result["output"], "package1\npackage2\npackage3")
        self.assertEqual(result["error"], "")
        
        # Verify SSH client was called
        client_mock.connect.assert_called_once_with(
            hostname="***************",
            username="test",
            password="test",
            timeout=10
        )
    
    @patch('co_engineer.agent.libvirt.agent.create_vm')
    @patch('co_engineer.agent.libvirt.agent.wait_for_vm_installation')
    @patch('co_engineer.agent.libvirt.agent.power_on_vm')
    @patch('co_engineer.agent.libvirt.agent.get_vm_ip')
    @patch('co_engineer.agent.libvirt.agent.ssh_to_vm')
    async def test_create_and_setup_vm(self, mock_ssh, mock_get_ip, mock_power_on, mock_wait, mock_create_vm):
        """Test creating and setting up a VM."""
        # Mock create_vm
        mock_create_vm.return_value = {
            "vm_name": "test-vm",
            "memory_size": 1024,
            "disk_size": 5,
            "iso_path": "/tmp/ubuntu-autoinstall.iso",
            "os_variant": "ubuntu24.04",
            "ssh_username": "test",
            "ssh_password": "test"
        }
        
        # Mock wait_for_vm_installation
        mock_wait.return_value = True
        
        # Mock power_on_vm
        mock_power_on.return_value = True
        
        # Mock get_vm_ip
        mock_get_ip.return_value = "***************"
        
        # Mock ssh_to_vm
        mock_ssh.return_value = {
            "success": True,
            "output": "package1\npackage2\npackage3",
            "error": ""
        }
        
        # Call create_and_setup_vm
        result = await create_and_setup_vm(
            vm_name="test-vm",
            memory_size=1024,
            disk_size=5
        )
        
        # Check result
        self.assertTrue(result["success"])
        self.assertEqual(result["vm_info"]["vm_name"], "test-vm")
        self.assertEqual(result["ip_address"], "***************")
        self.assertEqual(result["installed_packages"], ["package1", "package2", "package3"])
        
        # Verify all functions were called
        mock_create_vm.assert_called_once()
        mock_wait.assert_called_once()
        mock_power_on.assert_called_once()
        mock_get_ip.assert_called_once()
        mock_ssh.assert_called_once()
    
    @patch('co_engineer.agent.libvirt.agent.AssistantAgent')
    async def test_create_agent(self, mock_assistant_agent):
        """Test creating the agent."""
        # Mock AssistantAgent
        agent_mock = MagicMock()
        mock_assistant_agent.return_value = agent_mock
        
        # Call create_agent
        agent = await create_agent()
        
        # Verify AssistantAgent was called
        mock_assistant_agent.assert_called_once()
        
        # Check agent
        self.assertEqual(agent, agent_mock)

if __name__ == "__main__":
    # Run tests
    unittest.main()
