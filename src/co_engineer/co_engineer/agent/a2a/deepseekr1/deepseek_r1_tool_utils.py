import re
import xml.etree.ElementTree as ET
from litellm.types.utils import ChatCompletionMessageToolCall
from litellm.types.completion import Function

def convert_tool_schema_to_xml(tools):
    """Convert a list of tool schemas to XML tool use format.
    
    Args:
        tools: List of tool dictionaries in OpenAI schema format
        
    Returns:
        str: XML-formatted tools string
    """
    result = """
# TOOL USE
You have access to a set of tools. You can use one tool per message, and will receive the result of that tool use in the user's response. You use tools step-by-step to accomplish a given task, with each tool use informed by the result of the previous tool use.

# Tool Use Formatting
Each tool is defined with:
- **Name**: Prefix with "tool_" (e.g. `tool_file_read`)
- **Description**: Explains purpose and usage
- **Parameters**: List with type and constraints
- **Example**: XML usage example

# Tools
"""
    
    for tool in tools:
        func = tool["function"]
        name = func["name"]
        description = func["description"].split("\n")[0].strip()
        parameters = func["parameters"]["properties"]
        
        # Build description section
        result += f"### tool_{name}\n"
        result += f"Description: {description}\n"
        
        # Build parameters section
        if parameters:
            result += "Parameters:\n"
            for param_name, param_details in parameters.items():
                param_type = param_details.get("type", "string")
                desc = param_details.get("description", "")
                result += f"- {param_name}: {param_type} (required) - {desc}\n"
        
        # Build example section
        result += "Example:\n"
        result += "```xml\n"
        example_params = []
        for param_name, param_details in parameters.items():
            param_type = param_details.get("type", "string")
            if param_type == "string":
                value = "example_string"
            elif param_type == "integer":
                value = 0
            elif param_type == "boolean":
                value = False
            else:
                value = "value"
            example_params.append(f"<{param_name}>{value}</{param_name}>")
        example_xml = f"<tool_{name}>\n" + "\n".join(example_params) + f"\n</tool_{name}>"
        result += example_xml + "\n"
        result += "```\n\n"
    # print("**********convert_tool_schema_to_xml******************")
    # print(result)
    # print("******************************************************")
    return result.strip()


def _extract_tool_xml(text: str) -> str:
        match = re.search(r'<(tool_\w+)>(.*?)</\1>', text, re.DOTALL)
        return match.group(0) if match else None

def trim_think_string(input_string):
    # Split the string at the first occurrence of "</think>"
    parts = input_string.split("</think>", 1)
    
    # Check if the split was successful and return the second part
    if len(parts) > 1:
        return parts[1]
    else:
        return input_string


def _extract_tool_name(tool_name):
    return tool_name[5:]


def get_tool_xml_from_content(content):
    tool_xml = _extract_tool_xml(content)
    if not tool_xml:
        return None
    try:
        root = ET.fromstring(tool_xml)
        tool_name = _extract_tool_name(root.tag)
        # print("*********toolname******************")
        # print(tool_name)
        # print("***********************************")

        params = {child.tag: child.text.strip() for child in root}
        arguments=_convert_params(params)

        ret = ChatCompletionMessageToolCall(id='call_DN6IiLULWZw7sobV6puCji1O', function=Function(arguments=arguments, name=tool_name), type='function')
        # print("*********ChatCompletionMessageToolCall******************")
        # print(ret)
        # print("***********************************")
        return ret
        
    except ET.ParseError:
        return "Error: Invalid XML format"

def _convert_params(params):
        typed_params = {}
        for key, value in params.items():
            typed_params[key] = value
        return typed_params