# Multiagent Host for Agent-to-Agent (A2A) Communication

This module implements a Host Agent that orchestrates communication between users and multiple remote agents using the A2A (Agent-to-Agent) protocol. The Host Agent acts as a central coordinator that can delegate tasks to specialized remote agents based on their capabilities.

## Overview

The Multiagent Host provides:

- A Google ADK-based Host Agent that can communicate with multiple remote A2A-compatible agents
- Dynamic registration and management of remote agents
- Task delegation and coordination between agents
- Session management for maintaining conversation context
- Support for various content types including text, data, and files

## Key Components

### HostAgent Class

The `HostAgent` class is the central component responsible for:

- Initializing connections to remote agents
- Registering agent cards
- Creating the Google ADK agent with appropriate tools
- Managing conversation state
- Delegating tasks to appropriate remote agents
- Processing responses from remote agents

### RemoteAgentConnections Class

The `RemoteAgentConnections` class handles:

- Maintaining connections to remote A2A agents
- Sending tasks to remote agents (both streaming and non-streaming)
- Processing task responses and updates
- Managing metadata for conversations

### Utility Functions

The module includes utility functions for:

- Converting A2A message parts to appropriate formats
- Handling file artifacts
- Managing task state

## Usage

### Basic Usage

```python
from host_agent import HostAgent

# Initialize with a list of remote agent addresses
host_agent = HostAgent(["http://localhost:10000"])

# Create the Google ADK agent
agent = host_agent.create_agent()

# Use the agent to process user requests
# The agent will delegate tasks to appropriate remote agents
```

### Registering New Agents

```python
from co_engineer.agent.a2a.common.types import AgentCard

# Create or retrieve an agent card
agent_card = AgentCard(...)

# Register the agent with the host
host_agent.register_agent_card(agent_card)
```

### Web UI Integration

This Host Agent is designed to work with the A2A UI module, which provides a web-based interface for:

- Managing remote agents
- Conducting conversations
- Viewing task history and status
- Monitoring events

## Environment Configuration

The Host Agent requires the following environment variables:

- `API_BASE_URL`: URL for the LLM API (default: "http://***********:4000/v1")
- `MODEL_NAME_AT_ENDPOINT`: Name of the model to use (default: "openai/QwQ-32B")
- `API_KEY`: API key for authentication (default: "DUMMY_API_KEY")

## Prerequisites

- Python 3.12 or higher
- Google ADK library
- LiteLLM for model access
- Remote agents implementing the A2A protocol

## Related Components

- **CLI Host**: A simpler command-line interface for interacting with a single A2A agent
- **A2A UI**: A web-based interface for managing and interacting with multiple agents through the Host Agent
- **Remote Agents**: Specialized agents that implement the A2A protocol, such as the Summary Agent

