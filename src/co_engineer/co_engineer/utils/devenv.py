import docker
import io
import tarfile
import logging
import os

# Set up logging
logger = logging.getLogger(__name__)

class DockerManager:
    def __init__(self):
        self.client = docker.from_env()
        self.container = None
        self.image = None
        logger.info("Initialized DockerManager")

    def start_container(self):
        """Start or use existing container named devenv"""
        try:
            # Ensure directory exists before mounting
            host_dir = os.path.abspath('./devenv')
            if not os.path.exists(host_dir):
                os.makedirs(host_dir)
                logger.info(f"Created directory: {host_dir}")
            else:
                logger.info(f"Directory already exists: {host_dir}")

            # Check if container named 'devenv' already exists
            containers = self.client.containers.list(all=True, filters={"name": "devenv"})
            if containers:
                self.container = containers[0]
                if not self.container.status == 'running':
                    self.container.start()
                logger.info(f"Using existing container ID: {self.container.id}")
            else:
                self.container = self.client.containers.run(
                    'ubuntu:22.04',
                    name='devenv',
                    detach=True,
                    tty=True,
                    stdin_open=True,
                    auto_remove=True,
                    volumes={host_dir: {'bind': '/app', 'mode': 'rw'}}
                )
                logger.info(f"Started new container ID: {self.container.id}")
            return self.container.id
        except docker.errors.DockerException as e:
            logger.error(f"Failed to start container: {str(e)}")
            raise

    def run_cli(self, command):
        """Execute command in container and keep session"""
        if not self.container:
            logger.error("Attempted to run command without active container")
            raise RuntimeError("Container not running")

        logger.info(f"Executing command: {command}")
        exec_id = self.client.api.exec_create(self.container.id, cmd=command, stdin=True, tty=True)
        output = self.client.api.exec_start(exec_id, tty=True).decode()
        logger.debug(f"Command output: {output[:200]}...")  # Log first 200 chars to avoid noise
        return output

    def read_file(self, container_path):
        """Read file from container"""
        if not self.container:
            logger.error("Attempted to read file without active container")
            raise RuntimeError("Container not running")

        logger.info(f"Reading file: {container_path}")
        try:
            stream, stat = self.container.get_archive(container_path)
            file_obj = io.BytesIO()
            for chunk in stream:
                file_obj.write(chunk)
            file_obj.seek(0)

            with tarfile.open(fileobj=file_obj) as tar:
                member = tar.next()
                if member:
                    content = tar.extractfile(member).read().decode()
                    logger.debug(f"Read {len(content)} characters from {container_path}")
                    return content
            logger.warning(f"File not found: {container_path}")
            return None
        except Exception as e:
            logger.error(f"Error reading file: {str(e)}")
            raise

    def write_file(self, container_path, content):
        """Write file to container"""
        if not self.container:
            logger.error("Attempted to write file without active container")
            raise RuntimeError("Container not running")

        logger.info(f"Writing {len(content)} characters to {container_path}")
        try:
            file_obj = io.BytesIO()
            with tarfile.open(fileobj=file_obj, mode='w') as tar:
                data = io.BytesIO(content.encode())
                info = tarfile.TarInfo(name=container_path)
                info.size = len(data.getvalue())
                tar.addfile(info, data)

            file_obj.seek(0)
            self.container.put_archive(path='/', data=file_obj.read())
            logger.debug(f"Successfully wrote to {container_path}")
            return True
        except Exception as e:
            logger.error(f"Error writing file: {str(e)}")
            raise
