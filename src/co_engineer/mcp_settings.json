{"mcpServers": {"poweredge": {"autoApprove": [], "disabled": false, "timeout": 60, "command": "docker", "args": ["run", "-i", "--rm", "20.1.200.187:5000/mcp/mcp-poweredge:latest"], "transportType": "stdio"}, "lab": {"autoApprove": [], "disabled": false, "timeout": 60, "command": "docker", "args": ["run", "-i", "--rm", "20.1.200.187:5000/mcp/mcp-lab:latest"], "transportType": "stdio"}, "sse-ocp-release": {"autoApprove": [], "disabled": false, "timeout": 60, "url": "http://20.1.200.187:8082/mcp/ocp-release/sse", "transportType": "sse"}, "sse-weather": {"url": "http://20.1.200.187:8082/mcp/weather/sse", "disabled": false, "autoApprove": []}}}