# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# Virtual environment
venv/
env/
.venv/
.venvs/
env.bak/
venv.bak/

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython profile_default
profile_default/

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Environments
.env
.env.bak
.env.test
.env.development
.env.production

# IDEs
.vscode/
.idea/
*.iml
*.iws
*.ipr

# VSCode settings
.vscode/settings.json
.vscode/tasks.json
.vscode/launch.json
.vscode/extensions.json

# PyCharm
*.pycharm/
*.pycharm.*

# Sublime Text
*.sublime-project
*.sublime-workspace

# Atom
.atom/

# Temporary files
*.tmp
*.swp
*.swo
*.bak
*~

# Compiled files
*.pyc
*.pyo
*.pyd

# OS generated files
.DS_Store
Thumbs.db
