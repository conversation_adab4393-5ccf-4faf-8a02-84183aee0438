**As an IT Admin,**  
**I want to create a Python script to install Ubuntu 22.04 on my PowerEdge server,**  
**So that I can automate the OS installation process and ensure consistency across multiple servers.**

**Acceptance Criteria:**
- Criteria 1: The Python script should accept input parameters for the Ubuntu OS image URL and PowerEdge server access information.
- Criteria 2: The script should use iDRAC and Redfish APIs to mount the ISO file on the server.
- Criteria 3: The script should automate the installation process in unattended mode, including partitioning the disk and setting up the necessary configurations.
- Criteria 4: The script should provide logs and error messages for troubleshooting.
- Criteria 5: The script should be tested and verified on a PowerEdge server.

**Definition of Done:**
- All acceptance criteria are met.
- Code is reviewed and approved.
- Necessary tests are written and pass.
- Documentation is updated, if applicable.
- Feature is deployed to the testing environment.


