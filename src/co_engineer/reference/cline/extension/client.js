import * as vscode from 'vscode';
import * as WebSocket from 'ws';

export function activate(context: vscode.ExtensionContext) {
    let disposable = vscode.commands.registerCommand('extension.openChat', () => {
        const panel = vscode.window.createWebviewPanel(
            'chatWindow',
            'Chat Window',
            vscode.ViewColumn.One,
            {}
        );

        panel.webview.html = getWebviewContent();

        const ws = new WebSocket('ws://localhost:8080');

        ws.on('open', () => {
            console.log('Connected to server');
        });

        ws.on('message', message => {
            panel.webview.postMessage({ type: 'response', text: message });
        });

        panel.webview.onDidReceiveMessage(message => {
            if (message.type === 'sendMessage') {
                ws.send(message.text);
            }
        });
    });

    context.subscriptions.push(disposable);
}

function getWebviewContent() {
    return `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <title>Chat Window</title>
        </head>
        <body>
            <input type="text" id="input" placeholder="Type a message">
            <button onclick="sendMessage()">Send</button>
            <div id="response"></div>
            <script>
                const vscode = acquireVsCodeApi();

                function sendMessage() {
                    const input = document.getElementById('input').value;
                    vscode.postMessage({ type: 'sendMessage', text: input });
                }

                window.addEventListener('message', event => {
                    const message = event.data;
                    if (message.type === 'response') {
                        document.getElementById('response').innerText = message.text;
                    }
                });
            </script>
        </body>
        </html>
    `;
}
