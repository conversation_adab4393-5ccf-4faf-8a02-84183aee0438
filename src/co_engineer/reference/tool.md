# Tool Use Formatting

Tool use is formatted using XML-style tags. The tool name is enclosed in opening and closing tags, and each parameter is similarly enclosed within its own set of tags. Here's the structure:

<tool_name>
<parameter1_name>value1</parameter1_name>
<parameter2_name>value2</parameter2_name>
...
</tool_name>

For example:

<tool_read_file>
<path>src/main.js</path>
</tool_read_file>

## Real example

### Example 1
randome textdfadsfjdksla;dsfkalj;sdaf</think>dfadsfdsafdsafasdf
dsfasdfsdaf
<tool_read_file>
<path>src/example.js</path>
</tool_read_file>
randome testet


### Example2
randome textdfadsfjdksla;dsfkalj;sdaf</think>dfadsfdsafdsafasdf
dsfasdfsdaf
<tool_write_file>
<path>src/main.js</path>
<content>
Your file content here
</content>
</tool_write_file>

### Example3
<tool_write_file>
<path>src/main.js</path>
<content>
Your file content here
</content>
</tool_write_file>


Sample code to handle this tool call
```python
import xml.etree.ElementTree as ET
import re

def read_file(path):
    with open(path, 'r') as file:
        return file.read()

def write_file(path, content):
    with open(path, 'w') as file:
        file.write(content)

def extract_xml(text):
    match = re.search(r'<(tool_\w+)>.*?</\1>', text, re.DOTALL)
    return match.group(0) if match else None

def handle_tool_call(text):
    xml_input = extract_xml(text)
    if not xml_input:
        return "No valid tool call found in the input."

    root = ET.fromstring(xml_input)
    tool_name = root.tag

    if tool_name == 'tool_read_file':
        path = root.find('path').text
        return read_file(path)
    elif tool_name == 'tool_write_file':
        path = root.find('path').text
        content = root.find('content').text
        write_file(path, content)
        return f"File written to {path}"
    else:
        return f"Unknown tool: {tool_name}"

# Example usage
text_input1 = """
random text
<tool_read_file>
<path>src/example.js</path>
</tool_read_file>
more random text
"""

text_input2 = """
random text
<tool_write_file>
<path>src/main.js</path>
<content>Your file content here</content>
</tool_write_file>
"""

print(handle_tool_call(text_input1))
print(handle_tool_call(text_input2))

```