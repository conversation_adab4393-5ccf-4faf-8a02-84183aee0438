const express = require('express');
const path = require('path');
const app = express();
const PORT = process.env.PORT || 80;

// 静态文件目录
app.use(express.static(path.join(__dirname, 'dist')));

// 所有路由都返回 index.html
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist', 'index.html'));
});

// 启动服务器
app.listen(PORT, '0.0.0.0', () => {
  console.log(`Server is running on 0.0.0.0:${PORT}`);
  console.log(`Visit http://localhost:${PORT} or http://<your-ip-address>:${PORT} to view the dashboard`);
});
