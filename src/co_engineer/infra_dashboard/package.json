{"name": "infra-dashboard", "version": "1.0.0", "description": "AI Inference Infrastructure Overview Dashboard", "default": "index.tsx", "scripts": {"start": "parcel index.html --host 0.0.0.0", "build": "parcel build index.html", "type-check": "tsc --noEmit", "serve": "node server.js", "deploy": "npm run build && sudo npm run serve"}, "dependencies": {"express": "^5.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "recharts": "^2.10.3"}, "devDependencies": {"@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "parcel": "^2.10.3", "typescript": "^5.3.3"}}