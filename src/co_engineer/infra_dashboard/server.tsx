import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer, LabelList, Cell, TooltipProps } from 'recharts';
import { ValueType } from 'recharts/types/component/DefaultTooltipContent';

// Tailwind CSS classes for consistent styling
const cardClasses = "bg-white shadow-lg rounded-xl p-6 m-4";
const titleClasses = "text-xl font-semibold text-gray-700 mb-4 text-center";

interface ColorPalette {
  NVIDIA_H100: string;
  NVIDIA_L40S: string;
  NVIDIA_B200: string;
  NVIDIA_H200: string;
  NVIDIA_H20: string;
  AMD_MI300X: string;
  INTEL_GAUDI3: string;
  CODING_TASKS: string;
  GENERAL_TASKS: string;
  DARK_TEXT: string;
  LIGHT_TEXT: string;
}

// Colors for charts
const COLORS: ColorPalette = {
  NVIDIA_H100: '#76B900', // NVIDIA Green
  NVIDIA_L40S: '#8BC34A', // Lighter Green
  NVIDIA_B200: '#4CAF50', // Darker Green
  NVIDIA_H200: '#66BB6A', // Medium Green
  NVIDIA_H20: '#A5D6A7', // Lightest Green
  AMD_MI300X: '#ED1C24',   // AMD Red
  INTEL_GAUDI3: '#0071C5', // Intel Blue
  CODING_TASKS: '#1E88E5', // Blue for Coding
  GENERAL_TASKS: '#FFB300', // Amber for General
  DARK_TEXT: '#333333',
  LIGHT_TEXT: '#FFFFFF',
};

interface HardwareInventoryData {
  name: string;
  GPUs: number;
  fill: string;
}

// Chart 1: Hardware Inventory
const HardwareInventoryChart: React.FC = () => {
  const data: HardwareInventoryData[] = [
    { name: 'NVIDIA H100', GPUs: 16, fill: COLORS.NVIDIA_H100 },
    { name: 'AMD MI300X', GPUs: 16, fill: COLORS.AMD_MI300X },
    { name: 'INTEL Gaudi3', GPUs: 16, fill: COLORS.INTEL_GAUDI3 },
    { name: 'NVIDIA L40S', GPUs: 24, fill: COLORS.NVIDIA_L40S },
  ];
  const chartTitle = "Current Hardware Inventory (Austin)";

  return (
    <div className={cardClasses}>
      <h3 className={titleClasses}>{chartTitle}</h3>
      <ResponsiveContainer width="100%" height={300}>
        <BarChart data={data} layout="vertical" margin={{ top: 5, right: 40, left: 50, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis type="number" stroke={COLORS.DARK_TEXT} />
          <YAxis dataKey="name" type="category" stroke={COLORS.DARK_TEXT} width={120} />
          <Tooltip wrapperStyle={{ backgroundColor: '#fff', border: '1px solid #ccc' }} />
          <Legend />
          <Bar dataKey="GPUs" name="Number of GPUs" barSize={30}>
            {data.map((entry, index) => ( <Cell key={`cell-${index}`} fill={entry.fill} /> ))}
            <LabelList dataKey="GPUs" position="right" style={{ fill: COLORS.DARK_TEXT, fontSize: 12 }} />
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

interface ProductionCapacityData {
  name: string;
  'Coding Tasks Concurrent Users': number;
  'General Tasks Concurrent Users': number;
}

// Chart 2: Production Capacity (L40S)
const ProductionCapacityChart: React.FC = () => {
  const data: ProductionCapacityData[] = [
    { name: 'Qwen2.5-Coder-32B (16x L40S)', 'Coding Tasks Concurrent Users': 32, 'General Tasks Concurrent Users': 64, },
  ];
  const chartTitle = "Production Environment Model Capacity (16x NVIDIA L40S)";
  return (
    <div className={cardClasses}>
      <h3 className={titleClasses}>{chartTitle}</h3>
      <ResponsiveContainer width="100%" height={300}>
        <BarChart data={data} margin={{ top: 30, right: 30, left: 20, bottom: 40 }}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="name" stroke={COLORS.DARK_TEXT} angle={-15} textAnchor="end" height={50} interval={0} />
          <YAxis stroke={COLORS.DARK_TEXT} label={{ value: 'Concurrent Users', angle: -90, position: 'insideLeft', fill: COLORS.DARK_TEXT }} />
          <Tooltip wrapperStyle={{ backgroundColor: '#fff', border: '1px solid #ccc' }} />
          <Legend verticalAlign="top" wrapperStyle={{ lineHeight: '40px' }}/>
          <Bar dataKey="Coding Tasks Concurrent Users" fill={COLORS.CODING_TASKS} name="Coding Tasks">
            <LabelList dataKey="Coding Tasks Concurrent Users" position="top" style={{ fill: COLORS.DARK_TEXT, fontSize: 12 }} />
          </Bar>
          <Bar dataKey="General Tasks Concurrent Users" fill={COLORS.GENERAL_TASKS} name="General Tasks">
            <LabelList dataKey="General Tasks Concurrent Users" position="top" style={{ fill: COLORS.DARK_TEXT, fontSize: 12 }} />
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

interface ExperimentalCapacityData {
  name: string;
  'Coding Tasks Concurrent Users': number;
  'General Tasks Concurrent Users': number;
}

// Chart 3: Experimental Models Capacity Comparison
const ExperimentalCapacityChart: React.FC = () => {
  const data: ExperimentalCapacityData[] = [
    { name: 'Qwen3-235B (8x H100)', 'Coding Tasks Concurrent Users': 15, 'General Tasks Concurrent Users': 30, },
    { name: 'Qwen3-30B (4x H100)', 'Coding Tasks Concurrent Users': 40, 'General Tasks Concurrent Users': 80, },
    { name: 'Qwen2.5-Coder (4x H100)', 'Coding Tasks Concurrent Users': 20, 'General Tasks Concurrent Users': 40, },
    { name: 'Deepseek-R1 (16x MI300X)', 'Coding Tasks Concurrent Users': 8, 'General Tasks Concurrent Users': 20, },
  ];
  const chartTitle = "Experimental Environment Model Capacity Comparison";
  return (
    <div className={cardClasses}>
      <h3 className={titleClasses}>{chartTitle}</h3>
      <ResponsiveContainer width="100%" height={450}>
        <BarChart data={data} margin={{ top: 30, right: 30, left: 20, bottom: 120 }}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="name" stroke={COLORS.DARK_TEXT} angle={-30} textAnchor="end" interval={0} height={130} />
          <YAxis stroke={COLORS.DARK_TEXT} label={{ value: 'Concurrent Users', angle: -90, position: 'insideLeft', fill: COLORS.DARK_TEXT }} />
          <Tooltip wrapperStyle={{ backgroundColor: '#fff', border: '1px solid #ccc' }} />
          <Legend verticalAlign="top" wrapperStyle={{ lineHeight: '40px' }}/>
          <Bar dataKey="Coding Tasks Concurrent Users" fill={COLORS.CODING_TASKS} name="Coding Tasks">
            <LabelList dataKey="Coding Tasks Concurrent Users" position="top" style={{ fill: COLORS.DARK_TEXT, fontSize: 12 }} />
          </Bar>
          <Bar dataKey="General Tasks Concurrent Users" fill={COLORS.GENERAL_TASKS} name="General Tasks">
            <LabelList dataKey="General Tasks Concurrent Users" position="top" style={{ fill: COLORS.DARK_TEXT, fontSize: 12 }} />
          </Bar>
        </BarChart>
      </ResponsiveContainer>
      <p className="text-xs text-gray-500 mt-2 text-center">* Lower concurrency for Deepseek-R1 (AMD MI300x) reflects current optimization levels and software maturity.</p>
    </div>
  );
};

interface HardwareUpliftData {
  name: string;
  uplift: number;
  label: string;
  fill: string;
}

// Chart 4: Recommended Hardware Performance Uplift (vs H100)
const RecommendedHardwareUpliftChart: React.FC = () => {
  const data: HardwareUpliftData[] = [
    { name: 'NVIDIA H100', uplift: 1.0, label: 'Baseline', fill: COLORS.NVIDIA_H100 },
    { name: 'NVIDIA H200', uplift: 1.4, label: '~1.4x vs H100', fill: COLORS.NVIDIA_H200 },
    { name: 'NVIDIA B200', uplift: 4.2, label: '~3x vs H200\n(~4.2x vs H100)', fill: COLORS.NVIDIA_B200 },
  ];
  const chartTitle = "Recommended Hardware Performance Uplift (Token Gen. Speed vs H100)";

  const h200 = data.find(d => d.name === 'NVIDIA H200');
  const h200UpliftVsH100 = h200 ? h200.uplift : 1.4; // Default to 1.4 if not found
  const b200UpliftVsH200 = 3.0;
  const b200UpliftVsH100 = b200UpliftVsH200 * h200UpliftVsH100;

  const b200 = data.find(d => d.name === 'NVIDIA B200');
  if (b200) {
    b200.label = `~${b200UpliftVsH200.toFixed(1)}x vs H200\n(~${b200UpliftVsH100.toFixed(1)}x vs H100)`;
  }

  const formatUpliftLabel = (value: number) => `${value.toFixed(1)}x`;

  return (
    <div className={cardClasses}>
      <h3 className={titleClasses}>{chartTitle}</h3>
      <ResponsiveContainer width="100%" height={350}>
        <BarChart data={data} margin={{ top: 30, right: 30, left: 20, bottom: 60 }}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="name" stroke={COLORS.DARK_TEXT} angle={-20} textAnchor="end" interval={0} height={70}/>
          <YAxis stroke={COLORS.DARK_TEXT} label={{ value: 'Relative Performance Uplift', angle: -90, position: 'insideLeft', fill: COLORS.DARK_TEXT }} domain={[0, 'dataMax + 0.5']} />
          <Tooltip
            formatter={(value: ValueType, _name: string, props: any) => {
              return typeof value === 'number' ? [`${value.toFixed(1)}x`, props.payload.label] : [value, props.payload.label];
            }}
            wrapperStyle={{ backgroundColor: '#fff', border: '1px solid #ccc' }}
          />
          <Bar dataKey="uplift" name="Performance Uplift">
            {data.map((entry, index) => ( <Cell key={`cell-${index}`} fill={entry.fill} /> ))}
            <LabelList dataKey="uplift" position="top" formatter={formatUpliftLabel} style={{ fill: COLORS.DARK_TEXT, fontSize: 12 }} />
          </Bar>
        </BarChart>
      </ResponsiveContainer>
      <p className="text-xs text-gray-500 mt-2 text-center">* B200 performance is approx. 3x that of H200.</p>
    </div>
  );
};

type ChartType = 'inventory' | 'production' | 'experimental' | 'recommendation';

interface ChartComponentMap {
  [key: string]: JSX.Element;
}

// Main App Component
const App: React.FC = () => {
  const [activeChart, setActiveChart] = useState<ChartType>('inventory');

  const chartComponents: ChartComponentMap = {
    inventory: <HardwareInventoryChart />,
    production: <ProductionCapacityChart />,
    experimental: <ExperimentalCapacityChart />,
    recommendation: <RecommendedHardwareUpliftChart />,
  };
  const buttonClasses = (chartName: string) =>
    `px-4 py-2 mx-1 my-1 rounded-lg transition-colors text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${
      activeChart === chartName ? 'bg-indigo-600 text-white hover:bg-indigo-700' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
    }`;

  return (
    <div className="bg-slate-100 min-h-screen p-4 font-sans">
      <header className="mb-6 text-center">
        <h1 className="text-3xl font-bold text-gray-800">AI Inference Infrastructure Overview</h1>
      </header>
      <nav className="flex flex-wrap justify-center mb-8">
        <button onClick={() => setActiveChart('inventory')} className={buttonClasses('inventory')}>Hardware Inventory</button>
        <button onClick={() => setActiveChart('production')} className={buttonClasses('production')}>Production Capacity</button>
        <button onClick={() => setActiveChart('experimental')} className={buttonClasses('experimental')}>Experimental Capacity</button>
        <button onClick={() => setActiveChart('recommendation')} className={buttonClasses('recommendation')}>Recommended Hardware</button>
      </nav>
      <main>
        {chartComponents[activeChart]}
      </main>
      <footer className="text-center mt-12 py-4 border-t border-gray-300">
        <p className="text-sm text-gray-500">&copy; {new Date().getFullYear()} AI Infra Team. All rights reserved.</p>
      </footer>
    </div>
  );
}

export default App;
