#!/bin/bash

# 确保脚本以 root 权限运行
if [ "$EUID" -ne 0 ]; then
  echo "请以 root 权限运行此脚本"
  exit 1
fi

# 设置工作目录
DASHBOARD_DIR="/home/<USER>/eric/x/src/co_engineer/infra_dashboard"
cd $DASHBOARD_DIR

# 构建应用
echo "构建应用..."
npm run build

# 安装系统服务
echo "安装系统服务..."
cp $DASHBOARD_DIR/infra-dashboard.service /etc/systemd/system/
systemctl daemon-reload
systemctl enable infra-dashboard.service

# 启动服务
echo "启动服务..."
systemctl restart infra-dashboard.service

# 检查服务状态
echo "服务状态:"
systemctl status infra-dashboard.service

echo "部署完成！您可以通过 http://$(hostname -I | awk '{print $1}') 访问仪表板"
