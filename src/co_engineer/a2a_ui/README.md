## Demo Web App

This demo application showcases agents talking to other agents over A2A. 

![image](assets/image1.png)

* The frontend is a [mesop](https://github.com/mesop-dev/mesop) web application that renders conversations as content between the end user and the "Host Agent". This app can render text content, thought bubbles, web forms (requests for input from agents), and images. More content types coming soon 

* The [Host Agent](/src/co_engineer/co_engineer/agent/a2a/hosts/multiagent/README.md) is a Google ADK agent which orchestrates user requests to Remote Agents. 

* Each [Remote Agent](/src/co_engineer/co_engineer/agent/summary/README.md) is an A2AClient running inside a Google ADK agent. Each remote agent will retrieve the A2AServer's [AgentCard](https://google.github.io/A2A/#documentation?id=agent-card) and then proxy all requests using A2A. 

## Features

<need quick gif>

### Dynamically add agents
Clicking on the robot icon in the web app lets you add new agents. Enter the address of the remote agent's Agent<PERSON>ard and the app will fetch the card and add the remote agent to the local set of known agents.  

### Speak with one or more agents
Click on the chat button to start or continue an existing conversation. This conversation will go to the Host Agent which will then delegate the request to one or more remote agents. 

If the agent returns complex content - like an image or a web-form - the frontend will render this in the conversation view. The Remote Agent will take care of converting this content between A2A and the web apps native application representation.

### Explore A2A Tasks
Click on the history to see the messages sent between the web app and all of the agents (Host agent and Remote agents). 

Click on the task list to see all the A2A task updates from the remote agents

## Prerequisites

- Python 3.12 or higher
- UV
- Agent servers speaking A2A ([use these samples](/src/co_engineer/co_engineer/agent/summary/README.md))

## Running the Examples

1. Navigate to the demo ui directory:
    ```bash
    cd a2a_ui
    ```
2. Update the environment file with your configuration:

    ```bash
    # cat .env
    API_BASE_URL=http://***********:4000/v1
    MODEL_NAME_AT_ENDPOINT=openai/QwQ-32B
    API_KEY=DUMMY_API_KEY
    ```
3. Run a remote agent service:

    Refer to [Summay agent](/src/co_engineer/co_engineer/agent/summary/README.md)

4. Run the front end example:
    ```bash
    uv run main.py
    ```
    > Note: The application runs on port 12000 by default

5. Open the web app in your browser:
    ```bash
    http://localhost:12000
    ```
    For example:
    ![image](assets/image2.png)


6. Add remote agent to host agent:

    * Click `Agents` on the sidebar
    * Click `Upload` bottom
    * Fill one remote host address
    For example `localhost:10000`
    * Click `Read`
    ![image](assets/image3.png)

7. Start your Task with remote agents

    * Click `Home` on the sidebar
    * Fill your task
    For example `Summary the web content of: https://en.wikipedia.org/wiki/PowerEdge`
    * Click `Send`
    
    For example:
    ![image](assets/image4.png)
