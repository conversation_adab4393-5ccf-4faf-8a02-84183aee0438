[build-system]
requires = ["setuptools>=42"]
build-backend = "setuptools.build_meta"

[project]
name = "co_engineer"
version = "0.1.0"
description = "Co-Engineering Agent System"
authors = [{name = "Your Name", email = "<EMAIL>"}]
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "asyncclick>=*******",
    "autogen-agentchat",
    "autogen-ext[mcp,openai]",
    "docker>=7.0.0",
    "google-adk>=0.1.0",
    "google-genai==1.10.0",
    "jwcrypto>=1.5.6",
    "jwt>=1.3.1",
    "litellm>=1.66.0",
    "pyjwt>=2.9.0",
    "requests==2.32.3",
    "markdownify==0.11.6",
    "paramiko>=3.5.1",
    "mcp>=1.8.0",
    "r2r>=3.6.0",
    "smolagents[mcp]>=1.15.0",
    "httpx[socks]>=0.28.1",
]

[tool.setuptools]
packages = ["co_engineer"]
package-dir = {"co_engineer" = "co_engineer"}
