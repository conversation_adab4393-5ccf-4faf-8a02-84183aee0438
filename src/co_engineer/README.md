# Co Engineer

## Overview

Co Engineer is a project that utilizes multiple agents to perform tasks step-by-step. The project leverages Autogen to manage and coordinate these agents.

## Getting Started

## Setup Environment
To get started with Co Engineer, follow these steps:

1. **Create a Virtual Environment**

   First, create a virtual environment to manage your project's dependencies. Run the following command:

   ```bash
   python -m venv venv
   ```

2. **Activate the Virtual Environment**

   Activate the virtual environment. On Linux and macOS, use:

   ```bash
   source venv/bin/activate
   ```

   On Windows, use:

   ```bash
   .\venv\Scripts\activate
   ```

3. **Install Package and Dependencies**
   
   Install the co_engineer package along with all dependencies in development mode:
   
   ```bash
   pip install -e .
   ```

4. **Run the Project**

   To run the project, execute the `helloworld.py` script:

   ```bash
   python example/helloworld.py
   ```

5. **Verify the Result**
   You can check the coding result in devenv
   ```bash
   docker ps #get the container id of your devenv
   docker exec -it <container_id> bash
   more /app/main.py
   ```
