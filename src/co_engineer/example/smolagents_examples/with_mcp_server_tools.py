import os
from smolagents import OpenAIServerModel, ToolCollection, ToolCallingAgent
from mcp import StdioServerParameters

model = OpenAIServerModel(
    model_id="Qwen/Qwen3-235B-A22B",
    api_base="http://100.80.20.5:4000/v1/", # Leave this blank to query OpenAI servers.
    api_key="API_KEY", # Switch to the API key for the server you're targeting.
)

fs_server_params = StdioServerParameters(
    command="mcp-filesystem-server",
    args=["/home/<USER>", "/tmp"],
    env={
        **os.environ
    },
)

cli_server_params = StdioServerParameters(
    command="uvx",
    args=["cli-mcp-server"],
    env={
        "ALLOWED_DIR": "/home/<USER>",
        "ALLOWED_COMMANDS": "ls,cat,pwd,echo,docker",
        "ALLOWED_FLAGS": "-l,-a,--help,--version",
        "MAX_COMMAND_LENGTH": "1024",
        "COMMAND_TIMEOUT": "30",
        "ALLOW_SHELL_OPERATORS": "false", 
        **os.environ
    },
)

task = """
Get running docker containers info with 'docker ps' command and save to file named docker-ps.json in directory /tmp.
"""
with ToolCollection.from_mcp([fs_server_params, cli_server_params], trust_remote_code=True) as tool_collection:
    agent = ToolCallingAgent(tools=[*tool_collection.tools], model=model, add_base_tools=True)
    agent.run(task=task, max_steps=20)
    