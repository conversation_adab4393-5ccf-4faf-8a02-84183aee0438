# smolagents examples

## with mcp server tools
### mcp-filesystem-server
- Install 
```bash
go install github.com/mark3labs/mcp-filesystem-server
```

- Config
```json
"mcp-filesystem-server": {
      "disabled": false,
      "timeout": 60,
      "command": "mcp-filesystem-server",
      "args": [
        "/home/<USER>"
      ],
      "transportType": "stdio"
    }
```

### mcp-cli-server
- Install
```bash
N/A
```

- Config
```json
"mcp-cli-server": {
      "disabled": false,
      "timeout": 60,
      "command": "uvx",
      "args": [
        "cli-mcp-server"
      ],
      "env": {
        "ALLOWED_DIR": "/home/<USER>",
        "ALLOWED_COMMANDS": "ls,cat,pwd,echo,docker",
        "ALLOWED_FLAGS": "-l,-a,--help,--version",
        "MAX_COMMAND_LENGTH": "1024",
        "COMMAND_TIMEOUT": "30",
        "ALLOW_SHELL_OPERATORS": "false"
      },
      "transportType": "stdio"
    }
```

Run agent:
```bash
uv venv --python 3.12
uv pip install -r requirements.txt
python3 with_mcp_server_tools.py
```

## Notes
There is an bug in smolagents lib, `mcpadapt/smolagents_adapter.py` add after line92:
```python
if "properties" not in input_schema:
    return MCPAdaptTool(
        name=mcp_tool.name,
        description=mcp_tool.description or "",
        inputs={},
        output_type="string",
    )
```
