import logging
import asyncio
from co_engineer.agent.developer.developer import developer_agent
from autogen_agentchat.ui import Console

# Configure root logger for the entire application
logging.basicConfig(level=logging.WARNING)

logger = logging.getLogger(__name__)
autogen_core_logger = logging.getLogger('autogen_core')
autogen_core_logger.setLevel(logging.ERROR)

async def main():
    url = "https://github.com/microsoft/autogen/blob/main/README.md"
    # async for result in developer_agent.run_stream(task=f"tool_fetch_web_content {url}"):
    #     await Console(result)
    await Console(developer_agent.run_stream(task=f"tool_fetch_web_content {url}"))


if __name__ == "__main__":
    logger.info("Application starting...")
    asyncio.run(main())
