# Test Plan

## Test Target
Test the document ubuntu_setup.md, verify document is correct
- the overall workflow is correct
- the command is correct and workable
- if any error, stop and return failed on which part with error message

## Test environment
4 PowerEdage Servers, the network are all connected. And can be accessed from working env

### Rack Info

| Rack         | L05          | Slot         | 1               |
|--------------|--------------|--------------|-----------------|
| Site         | Shanghai     | Build Server | ************    |
| Mgmt VLAN    | IPv4         | 2871         | Mgmt VLAN (IPv6)|
| IPv4 Range   | ***********/24 | IPv6 Range | fc00::0571:0:0:0/80 |
| IPv4 Gateway | ***********  | IPv6 Gateway | fc00::0571:0:0:1 |


### PowerEdge Server Info

| Device Name | Serial Number | Model | iDRAC IP     | Connection    | OS IP (reserved) |
|-------------|----------------|-------|--------------|---------------|----------------|
| DELL 2080   | H364H24        | -     | ************ | Not Connected | *************  |
| DELL 2081   | 1464H24        | -     | ************ | Connected     | *************  |
| DELL 2082   | J364H24        | -     | ************ | Connected     | *************  |
| DELL 2083   | G364H24        | -     | ************ | Connected     | *************  |


### DNS Server

| Type                        | Address                          |
|-----------------------------|----------------------------------|
| Dual stack Primary DNS      | fc00::17 / ************          |
| Dual stack Reserve DNS      | fc00::18 / ************          |
| IPv4 Primary DNS            | ***********                      |
| IPv4 Primary DNS Public     | ***********                      |
| IPv4 Reserve DNS            | ***********                      |
| IPv4 Reserve DNS Public     | *************                    |
| IPv6 Primary DNS            | fc00::7                          |
| IPv6 Reserve DNS            | fc00::8                          |
| DNS List                    | Link |

NTP Server

| Type                        | Address                          |
|-----------------------------|----------------------------------|
| Dual stack Primary NTP      | fc00::17 / ************          |
| Dual stack Reserve NTP      | fc00::18 / ************          |
| IPv4 Primary NTP            | ***********                      |
| IPv4 Primary NTP Public     | ***********                      |
| IPv4 Reserve NTP            | ***********                      |
| IPv4 Reserve NTP Public     | *************                    |
| IPv6 Primary NTP            | fc00::7                          |
| IPv6 Reserve NTP            | fc00::8                          |


