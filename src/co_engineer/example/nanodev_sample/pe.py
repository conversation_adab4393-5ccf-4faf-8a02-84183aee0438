pe_reboot_task="""
create a nano scripts
module name: poweredge
nano script name: poweredge_reboot.py
nano script description: force reboot the poweredge server through idrac with redfish. wait until the server reboot completes successfully
parameters:
- idrac_ip: iDRAC IP address
- idrac_user: iDRAC username 
- idrac_password: iDRAC password
- force: Force immediate reboot (true/false)
steps:
1. Connect to iDRAC using Redfish API
2. Check current power state
3. Initiate forced reboot action
4. Monitor reboot status through Redfish
5. Wait until system returns to operational state
Test environment:
| Device Name | Serial Number | iDRAC IP     | iDRAC Username | iDRAC Password | OS IP (reserved)  |
|:-----------:|:-------------:|:------------:|:--------------:|:--------------:|:-----------------:|
| DELL 2080   | H364H24       | ************ | root           | 760xa123       | *************    |
"""

pe_mount_iso_task="""
create a nano scripts
module name: poweredge
nano script name: poweredge_mount_iso.py
nano script description: mount iso image through idrac using redfish API with iso URL
parameters:
- idrac_ip: iDRAC IP address
- idrac_user: iDRAC username 
- idrac_password: iDRAC password
- iso_url: HTTP URL of the ISO image to mount
steps:
1. Verify virtual media service is available
2. Insert virtual media using HTTP URI source
3. Verify mount status
4. Wait until media is successfully mounted
Test environment:
| Device Name | Serial Number | iDRAC IP     | iDRAC Username | iDRAC Password | OS IP (reserved)  |
|:-----------:|:-------------:|:------------:|:--------------:|:--------------:|:-----------------:|
| DELL 2080   | H364H24       | ************ | root           | 760xa123       | *************    |
"""

from co_engineer.agent.nanodev.run import run_task
from phoenix.otel import register
from openinference.instrumentation.smolagents import SmolagentsInstrumentor


if __name__ == "__main__":
    register()
    SmolagentsInstrumentor().instrument()
    result = run_task(pe_reboot_task)
    print(f"result: {result}")