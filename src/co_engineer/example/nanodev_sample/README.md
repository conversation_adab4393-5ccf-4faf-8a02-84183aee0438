## Run nanodev samples

### MCP Setup
<TODO>

### PowerEdge 
1. Prepare the repo in /home/<USER>/sandbox
```bash
<NAME_EMAIL>:wux13/POC6.git
```
1. Run Example
Run example in the porject root dir
```bash
~/dev/x/src/co_engineer$ uv run example/nanodev_sample/pe.py
```

### Inspecting
#### Use OpenTelemetry
1. Install dependency
```bash
uv pip install 'smolagents[telemetry,toolkit]'
```

1. Run Inspect Server
In a new terminal in vs code, run
```
python -m phoenix.server.main serve
```

1. Check the result in browser http://0.0.0.0:6006/projects/


Reference https://huggingface.co/docs/smolagents/tutorials/inspect_runs

#### Use LLM model monitor
Proxy: socks5h://************:3389
http://************:3000/ <EMAIL>:Dell.123456
