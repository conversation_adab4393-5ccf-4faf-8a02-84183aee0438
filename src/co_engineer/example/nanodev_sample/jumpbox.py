jumpbox_vm_create_task = """
create a nano scripts
module name: jumpbox
nano script name: jumpbox_vm_create.py
nano script description: create KVM virtual machine on Ubuntu jumpbox using existing VM image
parameters:
- vm_name: Name for new VM
- vm_image_path: Path to base VM image (qcow2 format)
- cpu_cores: Number of virtual CPUs
- memory_mb: Memory allocation in MB 
- disk_size_gb: Disk space to allocate
- network_bridge: Network bridge to attach to (default: virbr0)
- ssh_key: SSH public key for admin access
steps:
1. Verify KVM virtualization support is enabled
2. Check existing VM image is valid
3. Create new disk image from base image:
   qemu-img create -f qcow2 -F qcow2 -b ${vm_image_path} ${vm_name}.qcow2 ${disk_size_gb}G
4. Generate cloud-init configuration for SSH key
5. Create VM with virt-install:
   virt-install \
   --name ${vm_name} \
   --memory ${memory_mb} \
   --vcpus ${cpu_cores} \
   --disk path=${vm_name}.qcow2 \
   --network bridge=${network_bridge} \
   --os-type linux \
   --os-variant ubuntu22.04 \
   --import \
   --noautoconsole
6. Verify VM creation with virsh list
7. Start VM and wait for successful boot
"""

jumpbox_cloudinit_task = """
create a nano scripts
module name: jumpbox
nano script name: jumpbox_cloudinit_gen.py
nano script description: generate cloud-init configuration ISO for VM provisioning
parameters:
- output_path: Path to save generated ISO
- hostname: VM hostname
- username: Admin username
- ssh_key: SSH public key
- packages: List of packages to install
steps:
1. Create cloud-init directory structure
2. Generate meta-data file:
   instance-id: ${hostname}
   local-hostname: ${hostname}
3. Generate user-data file:
   #cloud-config
   users:
     - name: ${username}
       ssh-authorized-keys:
         - ${ssh_key}
   packages: ${packages}
4. Create ISO image:
   genisoimage -output ${output_path} -volid cidata -joliet -rock user-data meta-data
5. Verify ISO file creation
"""
