nutanix_config_task = """
create a nano scripts
module name: nutanix
nano script name: nutanix_config_gen.py
nano script description: generate nutanix cluster configuration yaml file
parameters:
- config_path: Output path for yaml file
- cluster_name: Cluster name
- nodes: List of node IP addresses
- redundancy_factor: Data redundancy factor
- dns_servers: List of DNS servers
- ntp_servers: List of NTP servers
steps:
1. Validate YAML file path
2. Create configuration structure
3. Generate sample content:
   ---
   cluster:
     name: {{ cluster_name }}
     nodes: {% for node in nodes %}
       - {{ node }}{% endfor %}
     storage:
       redundancy_factor: {{ redundancy_factor }}
   network:
     dns_servers: {{ dns_servers }}
     ntp_servers: {{ ntp_servers }}
4. Write formatted YAML to file
"""

nutanix_provision_task = """
create a nano scripts
module name: nutanix
nano script name: nutanix_cluster_provision.py
nano script description: provision nutanix cluster using REST API with configuration yaml
parameters:
- cluster_ip: Target cluster IP
- username: Admin username
- password: Admin password
- config_file: Path to configuration yaml
steps:
1. Read and validate YAML configuration
2. Authenticate with cluster API
3. Submit provisioning request with config
4. Monitor progress via task API
5. Validate final cluster status
6. Generate completion report
"""
