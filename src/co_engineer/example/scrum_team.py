import asyncio
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_agentchat.agents import Assistant<PERSON>gent
from autogen_agentchat.teams import MagenticOneGroupChat
from autogen_agentchat.ui import <PERSON>sole
from co_engineer.models import model_client_deepseek_r1


async def main() -> None:
    model_client = model_client_deepseek_r1

    assistant = Assistant<PERSON><PERSON>(
        "Assistant",
        model_client=model_client,
    )
    team = MagenticOneGroupChat([assistant], model_client=model_client)
    await Console(team.run_stream(task="Provide a different proof for Fermat's Last Theorem"))
    await model_client.close()


asyncio.run(main())