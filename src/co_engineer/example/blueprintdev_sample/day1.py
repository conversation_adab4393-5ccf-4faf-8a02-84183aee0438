day1_blueprint_task="""
create blueprint to setup nutanix cluster
blueprint project: /home/<USER>/sandbox/POC7
blueprint module name: nutanix_day1
blueprint input parameters:
    <TODO> - list the parameters

steps:
1. Power On the Poweredge Server
2. Create and Start Foundation VM in jumpbox, using script <string path>
3. Wait until the Day1 VM is ready
4. Discovery poweredge nodes
5. Config Foundation VM network
6. Generate Day1 api call payload
7. Wait until the Day1 VM is ready
8. Call REST API of the VM with script <string path>
9. Wait until the Cluster is ready
"""

from co_engineer.agent.blueprintdev.run import run_task
from phoenix.otel import register
from openinference.instrumentation.smolagents import SmolagentsInstrumentor

if __name__ == "__main__":
    register()
    SmolagentsInstrumentor().instrument()
    result = run_task(day1_blueprint_task)
    print(f"result: {result}")