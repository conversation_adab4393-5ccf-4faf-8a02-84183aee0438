import logging
import async<PERSON>
from autogen_agentchat.ui import Console
from co_engineer.agent.developer.developer import developer_agent


# Configure root logger for the entire application
# logging.basicConfig(
#     level=logging.INFO,
#     format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
#     handlers=[
#         logging.FileHandler('app.log'),
#         logging.StreamHandler()
#     ]
# )

logger = logging.getLogger(__name__)

task = """
## Description
Create a command line tool named ubuntu_installer to install Ubuntu 22.04 on PowerEdge Server.

**Acceptance Criteria:**
- Use Python as program language
- Input of ubuntu_installer
    - ISO_URL: Ubuntu ISO file
    - PowerEdge Server access info: idarc IP, username, password
    - configuration file, autoinstall configuration
- Output of ubuntu_installer
    - the installation result, success or error

- Criteria 2: The script should use iDRAC and Redfish APIs to mount the ISO file on the server.
- Criteria 3: The script should automate the installation process in unattended mode, including partitioning the disk and setting up the necessary configurations.
- Criteria 4: The script should provide logs and error messages for troubleshooting.
- Criteria 5: The script should be tested and verified on a PowerEdge server.

**Definition of Done:**
- All acceptance criteria are met.
- Code is reviewed and approved.
- Necessary tests are written and pass.
- Documentation is updated, if applicable.
- Feature is deployed to the testing environment.

** Outcome:**
- python script named /app/install_ubuntu.py
"""
logger = logging.getLogger(__name__)
autogen_core_logger = logging.getLogger('autogen_core')
autogen_core_logger.setLevel(logging.ERROR)

async def main():
    await Console(developer_agent.run_stream(task=task))

if __name__ == "__main__":
    logger.info("Application starting...")
    asyncio.run(main())
