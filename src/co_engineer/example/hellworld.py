import logging
import asyncio
from co_engineer.agent.developer.developer import developer_agent
from autogen_agentchat.ui import Console

# Configure root logger for the entire application
# logging.basicConfig(
#     level=logging.WARNING,
#     format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
#     handlers=[
#         logging.FileHandler('app.log'),
#         logging.StreamHandler()
#     ]
# )
logging.basicConfig(level=logging.WARNING)


logger = logging.getLogger(__name__)
autogen_core_logger = logging.getLogger('autogen_core')
autogen_core_logger.setLevel(logging.ERROR)

async def main():
    await <PERSON>sole(developer_agent.run_stream(task="create python file /app/main.py in to print helloworld"))


if __name__ == "__main__":
    logger.info("Application starting...")
    asyncio.run(main())
