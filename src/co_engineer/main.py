import logging
import asyncio
from co_engineer.agent.developer import developer_agent

# Configure root logger for the entire application
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# Configure logger for autogen_core
autogen_core_logger = logging.getLogger('autogen_core')
autogen_core_logger.setLevel(logging.ERROR)

async def main():
    await developer_agent.run(task="create python file named main.py to print helloworld")

if __name__ == "__main__":
    logger.info("Application starting...")
    asyncio.run(main())
