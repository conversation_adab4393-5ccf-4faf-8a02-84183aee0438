import unittest
from co_engineer.agent.tool_agent import ToolAgent
import os

def tool_file_write(path: str, content: str) -> str:
    try:
        os.makedirs(os.path.dirname(path), exist_ok=True)
        with open(path, 'w') as f:
            f.write(content)
        return f"File {path} updated"
    except Exception as e:
        return f"Write error: {str(e)}"
    
def tool_file_read(path: str) -> str:
    try:
        with open(path, 'r') as f:
            return f.read()
    except Exception as e:
        return f"Read error: {str(e)}"

class TestToolAgent(unittest.TestCase):
    def setUp(self):
        self.tool_agent = ToolAgent(name="test_tool_agent", ext_tools=[tool_file_write, tool_file_read])

    def test_tool_file_read(self):
        from autogen_agentchat.messages import TextMessage
        from autogen_core import CancellationToken

        message_content = """
        some mockup text
        <tool_file_read>
        <path>test/test_read_file.txt</path>
        </tool_file_read>
        other test here
        """
        message = TextMessage(content=message_content, source="test_source")

        import asyncio

        async def run_test():
            response = await self.tool_agent.on_messages([message], CancellationToken())
            self.assertIn("Test file for reading", response.chat_message.content)

        asyncio.run(run_test())

if __name__ == '__main__':
    unittest.main()
