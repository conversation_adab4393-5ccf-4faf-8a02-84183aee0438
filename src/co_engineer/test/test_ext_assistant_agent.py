import unittest
from co_engineer.agent.ext_assistant_agent import ExtAssistantAgent

class TestExtAssistantAgent(unittest.TestCase):
    def setUp(self):
        self.agent = ExtAssistantAgent()

    def test_parse_tool_call_single_tool(self):
        message = """random text here
<tool_file_read>
    <path>src/main.js</path>
</tool_file_read>
more random text"""
        expected = [('tool_file_read', {'path': 'src/main.js'})]
        result = self.agent.parse_tool_call(message)
        self.assertEqual(result, expected)

    def test_parse_tool_call_multiple_tools(self):
        message = """random text
<tool_file_read>
    <path>src/main.js</path>
</tool_file_read>
bdafdsafdsaf
<tool_file_write>
    <path>src/main.js</path>
    <content>console.log("Hello World");</content>
</tool_file_write>
more random text"""
        expected = [
            ('tool_file_read', {'path': 'src/main.js'}),
            ('tool_file_write', {'path': 'src/main.js', 'content': 'console.log("Hello World");'})
        ]
        result = self.agent.parse_tool_call(message)
        self.assertEqual(result, expected)

    def test_parse_tool_call_no_tools(self):
        message = "This is a normal message without any tool calls."
        expected = []
        result = self.agent.parse_tool_call(message)
        self.assertEqual(result, expected)

    def test_parse_tool_call_with_type_conversion(self):
        message = """random text
<tool_file_write>
    <path>data.json</path>
    <content>{"count": 5}</content>
</tool_file_write>
more text"""
        expected = [('tool_file_write', {'path': 'data.json', 'content': '{"count": 5}'})]
        result = self.agent.parse_tool_call(message)
        self.assertEqual(result, expected)

    def test_parse_tool_call_invalid_format(self):
        message = """broken xml
<command>echo Hello<requires_approval>false</invalid_tag>
more broken text"""
        expected = []
        result = self.agent.parse_tool_call(message)
        self.assertEqual(result, expected)

if __name__ == '__main__':
    unittest.main()
