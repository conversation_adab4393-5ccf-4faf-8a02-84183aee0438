import unittest
from co_engineer.agent.devenv import DockerManager

class TestDockerManager(unittest.TestCase):
    def setUp(self):
        self.dm = DockerManager()
        self.dm.start_container()

    def test_run_cli(self):
        result = self.dm.run_cli('echo "test"')
        self.assertIn("test", result)

    def test_read_write_file(self):
        test_content = "test file content"
        self.dm.write_file('/app/test.txt', test_content)
        content = self.dm.read_file('/app/test.txt')
        self.assertEqual(content, test_content)

    def tearDown(self):
        if self.dm.container:
            self.dm.container.stop()

if __name__ == '__main__':
    unittest.main()
