import unittest
import os
from co_engineer.tool.tool_handler import ToolHandler

class TestToolHandler(unittest.TestCase):
    def setUp(self):
        def tool_file_read(path: str) -> str:
            try:
                with open(path, 'r') as f:
                    return f.read()
            except Exception as e:
                return f"Read error: {str(e)}"

        def tool_file_write(path: str, content: str) -> str:
            try:
                os.makedirs(os.path.dirname(path), exist_ok=True)
                with open(path, 'w') as f:
                    f.write(content)
                return f"File {path} updated"
            except Exception as e:
                return f"Write error: {str(e)}"

        def tool_file_delete(path: str) -> str:
            try:
                os.remove(path)
                return f"Deleted {path}"
            except Exception as e:
                return f"Delete error: {str(e)}"

        # _handle_read.schema = {'path': str}
        # _handle_write.schema = {'path': str, 'content': str}
        # _handle_delete.schema = {'path': str}

        ext_tools = [
            tool_file_read,
            tool_file_write,
            tool_file_delete
        ]

        self.tool_handler = ToolHandler(ext_tools=ext_tools)

    def test_tool_file_read(self):
        response = self.tool_handler.execute("""
        some mockup text
        <tool_file_read>
        <path>test/test_read_file.txt</path>
        </tool_file_read>
        other test here
        """)
        self.assertIn("Test file for reading", response)

    def test_tool_file_write(self):
        response = self.tool_handler.execute("""
        <tool_file_write>
        <path>test/temp.txt</path>
        <content>Sample content</content>
        </tool_file_write>
        """)
        self.assertEqual(response, "File test/temp.txt updated")
        with open("test/temp.txt", "r") as f:
            content = f.read()
        self.assertEqual(content, "Sample content")

    def test_tool_file_delete(self):
        # First, create a temporary file
        with open("test/temp_delete.txt", "w") as f:
            f.write("Content to delete")
        
        response = self.tool_handler.execute("""
        <tool_file_delete>
            <path>test/temp_delete.txt</path>
        </tool_file_delete>
        """)
        self.assertEqual(response, "Deleted test/temp_delete.txt")
        self.assertFalse(os.path.exists("test/temp_delete.txt"))

if __name__ == '__main__':
    unittest.main()
