#!/bin/bash
# Copyright (c) 2023 Dell Inc. or its subsidiaries. All Rights Reserved.
#
# This software contains the intellectual property of Dell Inc. or is licensed to Dell Inc. from third parties.
# Use of this software and the intellectual property contained therein is expressly limited to the terms and
# conditions of the License Agreement under which it is provided by or on behalf of Dell Inc. or its subsidiaries.


# This script retrieves the SSH connection details for a given agent_name using a GraphQL query.
# It then sets the runtime properties for the ECE proxy host and port.

home=""

delete_kube_dir() {
  local user=$1
  home=$(su "$user" -c 'echo $HOME')
  echo "$user home: $home"
  if [ -d "$home/.kube" ]; then
    rm -rf "$home/.kube"
  fi
}

create_kube_dir() {
  local user=$1
  home=$(su "$user" -c 'echo $HOME')
  echo "home: $home"
  uid=$(su "$user" -c 'id -u')
  echo "uid: $uid"
  gid=$(su "$user" -c 'id -g')
  echo "gid: $gid"

  su "$user" -c 'mkdir -p $HOME/.kube'

  cp -i /etc/kubernetes/admin.conf "$home"/.kube/config
  chown "$uid":"$gid" "$home"/.kube/config
}

cleanup() {
  echo "Cleanup cluster"
  kubeadm reset -f
}

join_cluster() {
  local join_command=$1
  local role=$2
  local key=$3
  if [ -z "$join_command" ]; then
    echo "join command is not provided."
    exit 1
  fi

  if [ -z "$role" ]; then
    echo "role is not provided."
    exit 1
  fi

  # join node to the existing cluster
  if [ "$role" == "control-plane" ]; then
    if [ -z "$key" ]; then
      echo "certificate key is not provided."
      exit 1
    fi
    command="$join_command --control-plane --certificate-key $key"
    echo "Add a control plane node to existing cluster with command: $command"
  else
    command=$join_command
    echo "Add a worker node to existing cluster with command: $command"
  fi

  eval "$command"
}

wait_for_resource_ready () {
  local resource=$1
  local ns=$2
  local timeout=$3
  if [ -z "$timeout" ]; then
    timeout="2m"
  fi
  if [ -z "$ns" ]; then
    status=$(kubectl wait --timeout=$timeout --for=condition=Ready --all "$resource")
  else
    status=$(kubectl wait --timeout=$timeout --for=condition=Ready --all "$resource" -n $ns)
  fi
  if [[ $status =~ "condition met" ]]; then
    echo "$resource is ready"
    return 0
  else
    echo "$resource is not ready"
    return 1
  fi
}
# Join the cluster by the given join command and role as main
cleanup

join_cluster "$join_command" "$role" "$certificate_key"

if [ $? -ne 0 ]; then
  host=$(hostname -f)
  echo "$host Failed to join cluster"
  exit 1
fi

if [ "$role" == "control-plane" ]; then
  # Configure kubeconfig to use the cluster
  unset KUBECONFIG
  export KUBECONFIG=/etc/kubernetes/admin.conf

  delete_kube_dir "$SUDO_USER"
  create_kube_dir "$SUDO_USER"
  host=$(hostname)
  echo "Waiting for $host to be ready"
  if ! wait_for_resource_ready "nodes/$host"; then
    kubectl get node
    exit 1
  fi

fi

echo "Join $role node to cluster successfully!"
