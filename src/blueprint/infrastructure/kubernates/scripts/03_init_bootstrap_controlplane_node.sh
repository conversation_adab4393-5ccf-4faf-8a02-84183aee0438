#!/bin/bash
# Copyright (c) 2023 Dell Inc. or its subsidiaries. All Rights Reserved.
#
# This software contains the intellectual property of Dell Inc. or is licensed to Dell Inc. from third parties.
# Use of this software and the intellectual property contained therein is expressly limited to the terms and
# conditions of the License Agreement under which it is provided by or on behalf of Dell Inc. or its subsidiaries.


# This script retrieves the SSH connection details for a given agent_name using a GraphQL query.
# It then sets the runtime properties for the ECE proxy host and port.

set -e

join_command=""
key=""

delete_kube_dir() {
  local user=$1
  home=$(su "$user" -c 'echo $HOME')
  echo "$user home: $home"
  if [ -d "$home/.kube" ]; then
    rm -rf "$home/.kube"
  fi
}

create_kube_dir() {
  local user=$1
  home=$(su "$user" -c 'echo $HOME')
  echo "home: $home"
  uid=$(su "$user" -c 'id -u')
  echo "uid: $uid"
  gid=$(su "$user" -c 'id -g')
  echo "gid: $gid"

  su "$user" -c 'mkdir -p $HOME/.kube'

  cp -i /etc/kubernetes/admin.conf "$home"/.kube/config
  chown "$uid":"$gid" "$home"/.kube/config
}

install_flannel_cni() {
  echo "Install flannel CNI plugin"
  kubectl apply -f https://github.com/flannel-io/flannel/releases/latest/download/kube-flannel.yml
  sleep 5
}

cleanup() {
  echo "Cleanup cluster"
  #
  kubeadm reset -f
}

persist_cluster_info() {
  echo "Persist cluster info to file /tmp/cluster-info.json"
  rm -rf /tmp/cluster-info.json
  local endpoint=$1
  local token=$2
  local hash=$3
  local key=$4

  cat <<EOF > /tmp/cluster-info.json
{
  "control_plane_endpoint": "$endpoint",
  "token": "$token",
  "discovery_token_ca_cert_hash": "$hash",
  "certificate_key": "$key"
}
EOF
}

init_cluster() {
  local endpoint=$1
  command="kubeadm init --pod-network-cidr=10.244.0.0/16 --upload-certs --control-plane-endpoint=$endpoint"
  echo "Init cluster with command: $command."

  output=""
  while IFS= read -r line; do
    echo "$line"
    output+="$line"$'\n'
  done < <(eval "$command")

  #echo "$output"

  token=$(echo "$output" | grep -- --token | sed -E 's/.*--token ([^ ]+).*/\1/' | tail -n1)
  hash=$(echo "$output" | grep -- --discovery-token-ca-cert-hash | sed -E 's/.*--discovery-token-ca-cert-hash ([^ ]+).*/\1/' | tail -n1)
  key=$(echo "$output" | grep -- --certificate-key | sed -E 's/.*--certificate-key ([^ ]+).*/\1/')
  join_command="kubeadm join $endpoint --token $token --discovery-token-ca-cert-hash $hash"
  echo "Join command: $join_command"
  echo "Certificate key: $key"

  persist_cluster_info "$endpoint" "$token" "$hash" "$key"

  echo "Configure KUBECONFIG for root user"
  unset KUBECONFIG
  export KUBECONFIG=/etc/kubernetes/admin.conf
}


wait_for_resource_ready () {
  local resource=$1
  local ns=$2
  local timeout=$3
  if [ -z "$timeout" ]; then
    timeout="2m"
  fi
  if [ -z "$ns" ]; then
    status=$(kubectl wait --timeout=$timeout --for=condition=Ready --all "$resource")
  else
    status=$(kubectl wait --timeout=$timeout --for=condition=Ready --all "$resource" -n $ns)
  fi
  if [[ $status =~ "condition met" ]]; then
    echo "$resource is ready"
    return 0
  else
    echo "$resource is not ready"
    return 1
  fi
}

write_context() {
  echo "Write runtime-properties to node instance"
  # Update the node instance with join command
  ctx instance runtime-properties join_command "$join_command"

  # Update the node instance with certificate key
  ctx instance runtime-properties certificate_key "$key"

  # Update the node instance with kubeconfig
  ctx instance runtime-properties kubeconfig "$(cat /etc/kubernetes/admin.conf)"
}

if [ -z "$control_plane_endpoint" ]; then
  echo "control plane endpoint is not provided."
  exit 1
fi

cleanup

# Init single node cluster
init_cluster "$control_plane_endpoint"

# Install flannel CNI plugin
install_flannel_cni

# Check status
if ! wait_for_resource_ready "node"; then
  kubectl get node
  exit 1
fi

if ! wait_for_resource_ready "pods" "kube-flannel"; then
  kubectl get pod -n kube-flannel
  exit 1
fi

# configure kubeconfig for user
delete_kube_dir "$SUDO_USER"
create_kube_dir "$SUDO_USER"

# Update the node instance
write_context

echo "Init bootstrap control plane node successfully!"

