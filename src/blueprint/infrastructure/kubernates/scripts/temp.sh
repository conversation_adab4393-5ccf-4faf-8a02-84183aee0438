check_all_pod_ready() {
  pods=$(kubectl get pods -n $namespace -o jsonpath='{.items[*].metadata.name}')
  if [ -z "$pods" ]; then
    return 1
  fi

  for pod in $pods; do
      status=$(kubectl get pod -n $namespace $pod -o jsonpath='{.status.phase}')
      if [ "$status" != "Running" ]; then
          return 1
      fi
  done

  return 0
}

wait_for_pod_ready () {
  counter=0
  while [ $counter -le 60 ]; do
    sleep 5;
    # Get the list of all pods and their statuses
    kubectl get pods -n kube-flannel --no-headers | awk '$3 != "Running" {exit 1}'
    if [ $? -eq 0 ]; then
      echo "All pods are in running state."
      return 0
    fi

    if [ $counter -eq 60 ]; then
      echo "pod did not ready for more than 5 mins, please check it";
      return 1;
    fi
    kubectl get pods -n kube-flannel
    counter=$($counter + 1)
  done

}


wait_for_resource_ready () {
  local resource=$1
  local ns=$2
  if [ -z "$ns" ]; then
    status=$(kubectl wait --timeout=2m --for=condition=Ready --all "$resource" -n $ns)
  else
    status=$(kubectl wait --timeout=2m --for=condition=Ready --all "$resource")
  fi
  if [[ $status =~ "condition met" ]]; then
    echo "$resource is ready"
    return 0
  else
    echo "$resource is not ready"
    return 1
  fi
}