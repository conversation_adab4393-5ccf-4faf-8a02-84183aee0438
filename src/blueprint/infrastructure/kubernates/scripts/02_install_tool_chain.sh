#!/bin/bash
# Copyright (c) 2023 Dell Inc. or its subsidiaries. All Rights Reserved.
#
# This software contains the intellectual property of Dell Inc. or is licensed to Dell Inc. from third parties.
# Use of this software and the intellectual property contained therein is expressly limited to the terms and
# conditions of the License Agreement under which it is provided by or on behalf of Dell Inc. or its subsidiaries.


# This script retrieves the SSH connection details for a given agent_name using a GraphQL query.
# It then sets the runtime properties for the ECE proxy host and port.
set -ue

KUBEADM_VERSION="v1.29"
CNI_PLUGINS_VERSION="v1.3.0"
ARCH="amd64"
CNI_BIN_DEST="/opt/cni/bin"

# help info
function print_help {
  echo "usage: $0 [options]"
  echo
  echo "options:"
  echo "  -kv,  --kube-version                    Set kubeadm version (default: v1.29)"
  echo "  -cv,  --cni-version                     Set CNI plugins version (default: v1.3.0)"
  echo "  -h,   --help                            Show help"
  echo
  echo "sample: $0 -kv v1.29 -cv v1.3.0"
}

if [ $# -eq 0 ]; then
    echo "No arguments supplied, use default ones KUBEADM_VERSION: $KUBEADM_VERSION , CNI_PLUGINS_VERSION: $CNI_PLUGINS_VERSION"
fi

# parse args
while [[ "$#" -gt 0 ]]; do
  case $1 in
    -kv|--kube-version)
      KUBEADM_VERSION="$2"
      shift
      ;;
    -cv|--cni-version)
      CNI_PLUGINS_VERSION="$2"
      shift
      ;;
    -h|--help)
      print_help
      exit 0
      ;;
    *)
      echo "unknown option: $1"
      print_help
      exit 1
      ;;
  esac
  shift
done


# 1. system configuration
cat <<EOF | sudo tee /etc/modules-load.d/k8s.conf
overlay
br_netfilter
EOF

sudo modprobe overlay
sudo modprobe br_netfilter

cat <<EOF | sudo tee /etc/sysctl.d/k8s.conf
net.bridge.bridge-nf-call-iptables  = 1
net.bridge.bridge-nf-call-ip6tables = 1
net.ipv4.ip_forward                 = 1
EOF

sudo sysctl --system

# 2. Download EMC_CA_ROOT.crt from amaas-eos-drm1.cec.lab.emc.com or prepare it below install
wget  https://cnshcronus2.ccoe.lab.emc.com:7000/artifactory/vxrail-binaries-virtual/certs/EMC_CA_ROOT.crt -O ./EMC_CA_ROOT.crt --no-check-certificate
sudo cp EMC_CA_ROOT.crt /usr/local/share/ca-certificates/
sudo update-ca-certificates

# 3.1 Add Docker's official GPG key:
rm /var/lib/apt/lists/lock
sudo apt-get -qq -y update
sudo apt-get install -o DPkg::Lock::Timeout=-1 -y ca-certificates curl
sudo install -m 0755 -d /etc/apt/keyrings
sudo curl -fsSL https://download.docker.com/linux/ubuntu/gpg -o /etc/apt/keyrings/docker.asc
sudo chmod a+r /etc/apt/keyrings/docker.asc

# 3.2 Add the repository to Apt sources:
echo \
  "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu \
  $(. /etc/os-release && echo "$VERSION_CODENAME") stable" | \
  sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# 3.3 apt update and install
rm /var/lib/apt/lists/lock
sudo apt-get -qq -y update
sudo apt-get install -o DPkg::Lock::Timeout=-1 -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
sudo systemctl daemon-reload
sudo systemctl enable --now containerd

# 3.4 configure containerd
sudo touch /etc/containerd/config.toml
sudo echo "" > /etc/containerd/config.toml
cat <<EOF | sudo tee /etc/containerd/config.toml
version = 2

[plugins]

  [plugins."io.containerd.grpc.v1.cri"]

    [plugins."io.containerd.grpc.v1.cri".containerd]
      [plugins."io.containerd.grpc.v1.cri".containerd.runtimes]

        [plugins."io.containerd.grpc.v1.cri".containerd.runtimes.runc]
          runtime_type = "io.containerd.runc.v2"
          [plugins."io.containerd.grpc.v1.cri".containerd.runtimes.runc.options]
            SystemdCgroup = true
EOF

sudo systemctl restart containerd


# 4 Install CNI
sudo mkdir -p "$CNI_BIN_DEST"
curl -L "https://github.com/containernetworking/plugins/releases/download/${CNI_PLUGINS_VERSION}/cni-plugins-linux-${ARCH}-${CNI_PLUGINS_VERSION}.tgz" | sudo tar -C "$CNI_BIN_DEST" -xz


# 5 Install required pkg for kubectl kubelet and kubeadm
rm /var/lib/apt/lists/lock
sudo apt-get -qq -y update
# apt-transport-https may be a dummy package; if so, you can skip that package
sudo apt-get install -o DPkg::Lock::Timeout=-1 -y apt-transport-https ca-certificates curl gpg
curl -fsSL https://pkgs.k8s.io/core:/stable:/$KUBEADM_VERSION/deb/Release.key | sudo gpg --yes --dearmor -o /etc/apt/keyrings/kubernetes-apt-keyring.gpg
echo "deb [signed-by=/etc/apt/keyrings/kubernetes-apt-keyring.gpg] https://pkgs.k8s.io/core:/stable:/$KUBEADM_VERSION/deb/ /" | sudo tee /etc/apt/sources.list.d/kubernetes.list
rm /var/lib/apt/lists/lock
sudo apt-get -qq -y update
sudo apt-get install -o DPkg::Lock::Timeout=-1 -y kubelet kubeadm kubectl
sudo apt-mark hold kubelet kubeadm kubectl
sudo systemctl enable --now kubelet

# 6 final check status
containerdStatus=$(sudo systemctl status containerd.service | grep "Active:" | awk '{print $2}')
if [[ "$containerdStatus" == "active" ]]; then
 echo "containerd service is active "
else
 echo "containerd service is not active"
 exit 1
fi

kubeadmVer=$(sudo kubeadm version && echo "kubeadmVersionPrint")
if [[ "$kubeadmVer" == *"kubeadmVersionPrint" ]]; then
 echo "kubeadm is installed"
else
 echo "kubeadm is not installed properly"
 exit 1
fi

kubectlVer=$(sudo kubectl version --client && echo "kubectlVersionPrint")
if [[ "$kubectlVer" == *"kubectlVersionPrint" ]]; then
 echo "kubectl is installed"
else
 echo "kubectl is not installed properly"
 exit 1
fi

kletSta=$(sudo systemctl status kubelet.service | grep "Active:" | awk '{print $2}')
if [[ "$kletSta" != "activ"* ]]; then
 echo "kubelet.service is not active or activating, $kletSta"
 exit 1
else
 echo "kubelet.service is active or activating"
fi

echo "Installation of containerd, kubectl, kubeadm, kubelet is done !"
