inputs:
  # servicetag_host_01:
  #   type: deployment_id
  #   display_label: "controlplane node #1 Service Tag"
  #   description: Service Tag of the controlplane node.
  #   required: true
  #   constraints:
  #     - labels:
  #         - cloud:
  #             edge-cloud

  host_ip_01:
    type: string
    display_label: "controlplane node #1 IP"
    description: "controlplane node #1 IP"
    default: "************"

  # servicetag_host_02:
  #   type: deployment_id
  #   display_label: "controlplane node #2 Service Tag"
  #   description: Service Tag of the controlplane node.
  #   required: true
  #   constraints:
  #     - labels:
  #         - cloud:
  #             edge-cloud

#  host_ip_02:
#    type: string
#    display_label: "controlplane node #2 IP"
#    description: "controlplane node #2 IP"
#    default: "************"

  # servicetag_host_03:
  #   type: deployment_id
  #   display_label: "controlplane node #3 Service Tag"
  #   description: Service Tag of the controlplane node.
  #   required: true
  #   constraints:
  #     - labels:
  #         - cloud:
  #             edge-cloud
#  host_ip_03:
#    type: string
#    display_label: "controlplane node #3 IP"
#    description: "controlplane node #3 IP"
#    default: "************"

  # servicetag_host_04:
  #   type: deployment_id
  #   display_label: "worker node #1 Service Tag"
  #   description: Service Tag of the worker node.
  #   required: true
  #   constraints:
  #     - labels:
  #         - cloud:
  #             edge-cloud

  host_ip_02:
    type: string
    display_label: "worker node #1 IP"
    description: "worker node #1 IP"
    default: "************"

  # servicetag_host_05:
  #   type: deployment_id
  #   display_label: "worker node #2 Service Tag"
  #   description: Service Tag of the worker node.
  #   required: true
  #   constraints:
  #     - labels:
  #         - cloud:
  #             edge-cloud

  host_ip_03:
    type: string
    display_label: "worker node #2 IP"
    description: "worker node #2 IP"
    default: "************"

  host_ip_04:
    type: string
    display_label: "worker node #3 IP"
    description: "worker node #3 IP"
    default: "************"
