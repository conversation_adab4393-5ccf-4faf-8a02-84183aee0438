This directory is used to contain all k8s cluster related blueprints.

1. Precheck and configure
- check CPU and memory
- Check required ports
- Disable swap
- Update cert
- Sync time

2. Install tools for each node
- install container runtime
- install CNI plugin
- configure a cgroup driver
- Installing kubeadm, kubelet and kubectl

3. Init bootstrap control-plane node
- init single node cluster
- Configure flannel cni plugin

4. Other control-plane nodes and worker nodes join cluster parallel
- control-plane join cluster with step3 generated token
- worker node join cluster with step3 generated token


==============Temp notes =================

1. Precheck and configure
- check CPU and memory
- Check required ports
- Disable swap   ()
- Configure DNS  (No need)
- Update cert
- Sync time

2. Install tools for each node
- install container runtime
- install CNI plugin
- configure a cgroup driver
- Installing kubeadm, kubelet and kubectl

3. Init bootstrap control-plane node
- init single node cluster
- Configure flannel cni plugin

4. Other control-plane nodes and worker nodes join cluster
   parallel
- control-plane join cluster with step3 generated token
- worker node join cluster with step3 generated token


Swap off
mystic@c3-ub01:~$ free -h
total        used        free      shared  buff/cache   available
Mem:            15Gi       239Mi        15Gi       1.0Mi       327Mi        15Gi
Swap:          4.0Gi          0B       4.0Gi


sudo swapoff -a

mystic@c3-ub01:~$ free -h
total        used        free      shared  buff/cache   available
Mem:            15Gi       239Mi        15Gi       1.0Mi       327Mi        15Gi
Swap:             0B          0B          0B
mystic@c3-ub01:~$

sudo swapoff -a will disable swapping temporarily. To make this change persistent across reboots, make sure swap is disabled in config files like /etc/fstab, systemd.swap, depending how it was configured on your system.

From <https://v1-29.docs.kubernetes.io/docs/setup/production-environment/tools/kubeadm/install-kubeadm/#installing-runtime>

Forwarding IPv4 and letting iptables see bridged traffic
Execute the below mentioned instructions:

cat <<EOF | sudo tee /etc/modules-load.d/k8s.conf
overlay
br_netfilter
EOF

sudo modprobe overlay
sudo modprobe br_netfilter

# sysctl params required by setup, params persist across reboots
cat <<EOF | sudo tee /etc/sysctl.d/k8s.conf
net.bridge.bridge-nf-call-iptables  = 1
net.bridge.bridge-nf-call-ip6tables = 1
net.ipv4.ip_forward                 = 1
EOF

# Apply sysctl params without reboot
sudo sysctl --system


mystic@c3-ub01:~$ sudo cp EMC_CA_ROOT.crt /usr/local/share/ca-certificates/
mystic@c3-ub01:~$ sudo update-ca-certificates




https://github.com/containerd/containerd/blob/main/docs/getting-started.md#option-2-from-apt-get-or-dnf
Option 2: From apt-get or dnf
The containerd.io packages in DEB and RPM formats are distributed by Docker (not by the containerd project). See the Docker documentation for how to set up apt-get or dnf to install containerd.io packages:
? CentOS
? Debian
? Fedora
? Ubuntu
The containerd.io package contains runc too, but does not contain CNI plugins.

From <https://github.com/containerd/containerd/blob/main/docs/getting-started.md#option-2-from-apt-get-or-dnf>

Todo: firewall rule



Container runtime
containerd

From <https://v1-29.docs.kubernetes.io/docs/setup/production-environment/tools/kubeadm/install-kubeadm/>



https://docs.docker.com/engine/install/ubuntu/#install-using-the-repository

# Add Docker's official GPG key:
sudo apt-get update
sudo apt-get install ca-certificates curl
sudo install -m 0755 -d /etc/apt/keyrings
sudo curl -fsSL https://download.docker.com/linux/ubuntu/gpg -o /etc/apt/keyrings/docker.asc
sudo chmod a+r /etc/apt/keyrings/docker.asc

# Add the repository to Apt sources:
echo \
"deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu \
$(. /etc/os-release && echo "$VERSION_CODENAME") stable" | \
sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
sudo apt-get update

sudo apt-get install docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin

########
systemctl daemon-reload
systemctl enable --now containerd

mystic@c3-ub02:~$ cat /etc/containerd/config.toml
version = 2

[plugins]

[plugins."io.containerd.grpc.v1.cri"]

    [plugins."io.containerd.grpc.v1.cri".containerd]
      [plugins."io.containerd.grpc.v1.cri".containerd.runtimes]

        [plugins."io.containerd.grpc.v1.cri".containerd.runtimes.runc]
          runtime_type = "io.containerd.runc.v2"
          [plugins."io.containerd.grpc.v1.cri".containerd.runtimes.runc.options]
            SystemdCgroup = true
mystic@c3-ub02:~$


sudo systemctl start containerd

From <https://github.com/containerd/containerd/blob/main/docs/getting-started.md>

sudo systemctl restart containerd

From <https://v1-29.docs.kubernetes.io/docs/setup/production-environment/container-runtimes/#containerd>



Install CNI plugins (required for most pod network):
CNI_PLUGINS_VERSION="v1.3.0"
ARCH="amd64"
DEST="/opt/cni/bin"
sudo mkdir -p "$DEST"
curl -L "https://github.com/containernetworking/plugins/releases/download/${CNI_PLUGINS_VERSION}/cni-plugins-linux-${ARCH}-${CNI_PLUGINS_VERSION}.tgz" | sudo tar -C "$DEST" -xz


From <https://v1-29.docs.kubernetes.io/docs/setup/production-environment/tools/kubeadm/install-kubeadm/>

Installing kubeadm, kubelet and kubectl

From <https://v1-29.docs.kubernetes.io/docs/setup/production-environment/tools/kubeadm/install-kubeadm/>

? Debian-based distributions


