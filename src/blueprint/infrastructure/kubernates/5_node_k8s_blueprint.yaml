# Copyright (c) 2023 Dell Inc. or its subsidiaries. All Rights Reserved.
#
# This software contains the intellectual property of Dell Inc. or is licensed to Dell Inc. from third parties.
# Use of this software and the intellectual property contained therein is expressly limited to the terms and
# conditions of the License Agreement under which it is provided by or on behalf of Dell Inc. or its subsidiaries.

tosca_definitions_version: nativeedge_1_0
description: >
  The blueprint is used to create k8s 5 node cluster.

imports:
  - nativeedge/types/types.yaml
  - plugin:nativeedge-fabric-plugin
  - plugin:nativeedge-utilities-plugin
  - inputs/5_node_k8s_cluster_inputs.yaml
  - inputs/5_node_k8s_host_inputs.yaml
  - nodes/5_node_k8s_definitions.yaml

capabilities:
  secret_name:
    description: "secret that contains kubeconfig content"
    value:
     concat:
      - "kubeconfig-"
      - { get_input: kube_api_server_fqdn }

labels:
  csys-obj-type:
    values:
      - environment
  target_environment:
    values:
      - ece
  vendor:
    values:
      - cncf
  solution:
    values:
      - kubernetes
  version:
    values:
      - "0.1"

blueprint_labels:
  env:
    values:
      - NED