dsl_definitions:
  ubuntu_account_host_01: &ubuntu_account_host_01
    user: {get_secret: [mystic_credential, username]}
    password: {get_secret: [mystic_credential, password]}
  ubuntu_account_host_02: &ubuntu_account_host_02
    user: {get_secret: [mystic_credential, username]}
    password: {get_secret: [mystic_credential, password]}
  ubuntu_account_host_03: &ubuntu_account_host_03
    user: {get_secret: [mystic_credential, username]}
    password: {get_secret: [mystic_credential, password]}
  ubuntu_account_host_04: &ubuntu_account_host_04
    user: {get_secret: [mystic_credential, username]}
    password: {get_secret: [mystic_credential, password]}
  ubuntu_account_host_05: &ubuntu_account_host_05
    user: {get_secret: [mystic_credential, username]}
    password: {get_secret: [mystic_credential, password]}

node_templates:
  # eo_proxy_01:
  #   type: nativeedge.nodes.ApplicationModule
  #   interfaces:
  #     nativeedge.interfaces.lifecycle:
  #       start:
  #         implementation: scripts/get-eo-proxy.sh
  #         max_retries: 3
  #         inputs:
  #           agent_name: { get_input: servicetag_host_01 }

  modify_mount_flag_01:
    type: nativeedge.nodes.Root
    interfaces:
      nativeedge.interfaces.lifecycle:
        create:
          implementation: fabric.fabric_plugin.tasks.run_commands
          max_retries: 3
          inputs:
            fabric_env:
              host: {get_input: host_ip_01}
              port: 22
              <<: *ubuntu_account_host_01
            use_sudo: true
            sudo:
              <<: *ubuntu_account_host_01
            commands:
              - mount -o remount,exec /tmp || true
    # relationships:
    #   - type: nativeedge.relationships.depends_on
    #     target: eo_proxy_01

  precheck_and_configure_host_01:
    type: nativeedge.nodes.Root
    interfaces:
      nativeedge.interfaces.lifecycle:
        create:
          implementation: fabric.fabric_plugin.tasks.run_script
          max_retries: 1
          inputs:
            script_path: scripts/01_precheck_and_config.sh
            fabric_env:
              host: {get_input: host_ip_01}
              port: 22
              connect_timeout: 1800
              <<: *ubuntu_account_host_01
            use_sudo: true
            sudo: *ubuntu_account_host_01
    relationships:
      - type: nativeedge.relationships.depends_on
        target: modify_mount_flag_01

  install_tools_host_01:
    type: nativeedge.nodes.Root
    interfaces:
      nativeedge.interfaces.lifecycle:
        create:
          implementation: fabric.fabric_plugin.tasks.run_script
          max_retries: 1
          inputs:
            script_path: scripts/02_install_tool_chain.sh
            fabric_env:
              host: {get_input: host_ip_01}
              port: 22
              connect_timeout: 1800
              <<: *ubuntu_account_host_01
            use_sudo: true
            sudo: *ubuntu_account_host_01
            process:
              env:
                k8s_version: { get_input: k8s_version }
    relationships:
      - type: nativeedge.relationships.depends_on
        target: precheck_and_configure_host_01

  # eo_proxy_02:
  #   type: nativeedge.nodes.ApplicationModule
  #   interfaces:
  #     nativeedge.interfaces.lifecycle:
  #       start:
  #         implementation: scripts/get-eo-proxy.sh
  #         max_retries: 3
  #         inputs:
  #           agent_name: { get_input: servicetag_host_02 }

  modify_mount_flag_02:
    type: nativeedge.nodes.Root
    interfaces:
      nativeedge.interfaces.lifecycle:
        create:
          implementation: fabric.fabric_plugin.tasks.run_commands
          max_retries: 3
          inputs:
            fabric_env:
              host: {get_input: host_ip_02}
              port: 22
              <<: *ubuntu_account_host_02
            use_sudo: true
            sudo:
              <<: *ubuntu_account_host_02
            commands:
              - mount -o remount,exec /tmp || true
    # relationships:
    #   - type: nativeedge.relationships.depends_on
    #     target: eo_proxy_02

  precheck_and_configure_host_02:
    type: nativeedge.nodes.Root
    interfaces:
      nativeedge.interfaces.lifecycle:
        create:
          implementation: fabric.fabric_plugin.tasks.run_script
          max_retries: 1
          inputs:
            script_path: scripts/01_precheck_and_config.sh
            fabric_env:
              host: {get_input: host_ip_02}
              port: 22
              connect_timeout: 1800
              <<: *ubuntu_account_host_02
            use_sudo: true
            sudo: *ubuntu_account_host_02
    relationships:
      - type: nativeedge.relationships.depends_on
        target: modify_mount_flag_02

  install_tools_host_02:
    type: nativeedge.nodes.Root
    interfaces:
      nativeedge.interfaces.lifecycle:
        create:
          implementation: fabric.fabric_plugin.tasks.run_script
          max_retries: 1
          inputs:
            script_path: scripts/02_install_tool_chain.sh
            fabric_env:
              host: {get_input: host_ip_02}
              port: 22
              connect_timeout: 1800
              <<: *ubuntu_account_host_02
            use_sudo: true
            sudo: *ubuntu_account_host_02
            process:
              env:
                k8s_version: { get_input: k8s_version }
    relationships:
      - type: nativeedge.relationships.depends_on
        target: precheck_and_configure_host_02

  # eo_proxy_03:
  #   type: nativeedge.nodes.ApplicationModule
  #   interfaces:
  #     nativeedge.interfaces.lifecycle:
  #       start:
  #         implementation: scripts/get-eo-proxy.sh
  #         max_retries: 3
  #         inputs:
  #           agent_name: { get_input: servicetag_host_03 }

  modify_mount_flag_03:
    type: nativeedge.nodes.Root
    interfaces:
      nativeedge.interfaces.lifecycle:
        create:
          implementation: fabric.fabric_plugin.tasks.run_commands
          max_retries: 3
          inputs:
            fabric_env:
              host: {get_input: host_ip_03}
              port: 22
              <<: *ubuntu_account_host_03
            use_sudo: true
            sudo:
              <<: *ubuntu_account_host_03
            commands:
              - mount -o remount,exec /tmp || true
    # relationships:
    #   - type: nativeedge.relationships.depends_on
    #     target: eo_proxy_03

  precheck_and_configure_host_03:
    type: nativeedge.nodes.Root
    interfaces:
      nativeedge.interfaces.lifecycle:
        create:
          implementation: fabric.fabric_plugin.tasks.run_script
          max_retries: 1
          inputs:
            script_path: scripts/01_precheck_and_config.sh
            fabric_env:
              host: {get_input: host_ip_03}
              port: 22
              connect_timeout: 1800
              <<: *ubuntu_account_host_03
            use_sudo: true
            sudo: *ubuntu_account_host_03
    relationships:
      - type: nativeedge.relationships.depends_on
        target: modify_mount_flag_03

  install_tools_host_03:
    type: nativeedge.nodes.Root
    interfaces:
      nativeedge.interfaces.lifecycle:
        create:
          implementation: fabric.fabric_plugin.tasks.run_script
          max_retries: 1
          inputs:
            script_path: scripts/02_install_tool_chain.sh
            fabric_env:
              host: {get_input: host_ip_03}
              port: 22
              connect_timeout: 1800
              <<: *ubuntu_account_host_03
            use_sudo: true
            sudo: *ubuntu_account_host_03
            process:
              env:
                k8s_version: { get_input: k8s_version }
    relationships:
      - type: nativeedge.relationships.depends_on
        target: precheck_and_configure_host_03

  # eo_proxy_04:
  #   type: nativeedge.nodes.ApplicationModule
  #   interfaces:
  #     nativeedge.interfaces.lifecycle:
  #       start:
  #         implementation: scripts/get-eo-proxy.sh
  #         max_retries: 3
  #         inputs:
  #           agent_name: { get_input: servicetag_host_04 }

  modify_mount_flag_04:
    type: nativeedge.nodes.Root
    interfaces:
      nativeedge.interfaces.lifecycle:
        create:
          implementation: fabric.fabric_plugin.tasks.run_commands
          max_retries: 3
          inputs:
            fabric_env:
              host: {get_input: host_ip_04}
              port: 22
              <<: *ubuntu_account_host_04
            use_sudo: true
            sudo:
              <<: *ubuntu_account_host_04
            commands:
              - mount -o remount,exec /tmp || true
    # relationships:
    #   - type: nativeedge.relationships.depends_on
    #     target: eo_proxy_04

  precheck_and_configure_host_04:
    type: nativeedge.nodes.Root
    interfaces:
      nativeedge.interfaces.lifecycle:
        create:
          implementation: fabric.fabric_plugin.tasks.run_script
          max_retries: 1
          inputs:
            script_path: scripts/01_precheck_and_config.sh
            fabric_env:
              host: {get_input: host_ip_04}
              port: 22
              connect_timeout: 1800
              <<: *ubuntu_account_host_04
            use_sudo: true
            sudo: *ubuntu_account_host_04
    relationships:
      - type: nativeedge.relationships.depends_on
        target: modify_mount_flag_04

  install_tools_host_04:
    type: nativeedge.nodes.Root
    interfaces:
      nativeedge.interfaces.lifecycle:
        create:
          implementation: fabric.fabric_plugin.tasks.run_script
          max_retries: 1
          inputs:
            script_path: scripts/02_install_tool_chain.sh
            fabric_env:
              host: {get_input: host_ip_04}
              port: 22
              connect_timeout: 1800
              <<: *ubuntu_account_host_04
            use_sudo: true
            sudo: *ubuntu_account_host_04
            process:
              env:
                k8s_version: { get_input: k8s_version }
    relationships:
      - type: nativeedge.relationships.depends_on
        target: precheck_and_configure_host_04

  # eo_proxy_05:
  #   type: nativeedge.nodes.ApplicationModule
  #   interfaces:
  #     nativeedge.interfaces.lifecycle:
  #       start:
  #         implementation: scripts/get-eo-proxy.sh
  #         max_retries: 3
  #         inputs:
  #           agent_name: { get_input: servicetag_host_05 }



  # will export runtime-properties: join_command , certificate_key , kubeconfig
  init_bootstrap_controlplane_node:
    type: nativeedge.nodes.Root
    interfaces:
      nativeedge.interfaces.lifecycle:
        create:
          implementation: fabric.fabric_plugin.tasks.run_script
          max_retries: 0
          inputs:
            script_path: scripts/03_init_bootstrap_controlplane_node.sh
            fabric_env:
              host: {get_input: host_ip_01}
              port: 22
              connect_timeout: 1800
              <<: *ubuntu_account_host_01
            use_sudo: true
            sudo: *ubuntu_account_host_01
            process:
              env:
                control_plane_endpoint:
                  concat:
                  - { get_input: kube_api_server_fqdn }
                  - ":6443"
    relationships:
      - type: nativeedge.relationships.depends_on
        target: install_tools_host_01

#  join_controlplane_node_02:
#    type: nativeedge.nodes.Root
#    interfaces:
#      nativeedge.interfaces.lifecycle:
#        create:
#          implementation: fabric.fabric_plugin.tasks.run_script
#          max_retries: 0
#          inputs:
#            script_path: scripts/04_join_cluster.sh
#            fabric_env:
#              host: {get_input: host_ip_02}
#              port: 22
#              connect_timeout: 1800
#              <<: *ubuntu_account_host_02
#            use_sudo: true
#            sudo: *ubuntu_account_host_02
#            process:
#              env:
#                join_command:
#                  concat:
#                  - "'"
#                  - { get_attribute: [init_bootstrap_controlplane_node, join_command] }
#                  - "'"
#                certificate_key: { get_attribute: [init_bootstrap_controlplane_node, certificate_key] }
#                role: "control-plane"
#    relationships:
#      - type: nativeedge.relationships.depends_on
#        target: install_tools_host_02
#      - type: nativeedge.relationships.depends_on
#        target: init_bootstrap_controlplane_node

#  join_controlplane_node_03:
#    type: nativeedge.nodes.Root
#    interfaces:
#      nativeedge.interfaces.lifecycle:
#        create:
#          implementation: fabric.fabric_plugin.tasks.run_script
#          max_retries: 0
#          inputs:
#            script_path: scripts/04_join_cluster.sh
#            fabric_env:
#              host: {get_input: host_ip_03}
#              port: 22
#              connect_timeout: 1800
#              <<: *ubuntu_account_host_03
#            use_sudo: true
#            sudo: *ubuntu_account_host_03
#            process:
#              env:
#                join_command:
#                  concat:
#                  - "'"
#                  - { get_attribute: [init_bootstrap_controlplane_node, join_command] }
#                  - "'"
#                certificate_key: { get_attribute: [init_bootstrap_controlplane_node, certificate_key] }
#                role: "control-plane"
#    relationships:
#      - type: nativeedge.relationships.depends_on
#        target: install_tools_host_03
#      - type: nativeedge.relationships.depends_on
#        target: init_bootstrap_controlplane_node

  join_worker_node_01:
    type: nativeedge.nodes.Root
    interfaces:
      nativeedge.interfaces.lifecycle:
        create:
          implementation: fabric.fabric_plugin.tasks.run_script
          max_retries: 0
          inputs:
            script_path: scripts/04_join_cluster.sh
            fabric_env:
              host: {get_input: host_ip_02}
              port: 22
              connect_timeout: 1800
              <<: *ubuntu_account_host_02
            use_sudo: true
            sudo: *ubuntu_account_host_02
            process:
              env:
                join_command:
                  concat:
                  - "'"
                  - { get_attribute: [init_bootstrap_controlplane_node, join_command] }
                  - "'"
                role: "worker"
    relationships:
      - type: nativeedge.relationships.depends_on
        target: install_tools_host_02
      - type: nativeedge.relationships.depends_on
        target: init_bootstrap_controlplane_node

  join_worker_node_02:
    type: nativeedge.nodes.Root
    interfaces:
      nativeedge.interfaces.lifecycle:
        create:
          implementation: fabric.fabric_plugin.tasks.run_script
          max_retries: 0
          inputs:
            script_path: scripts/04_join_cluster.sh
            fabric_env:
              host: {get_input: host_ip_03}
              port: 22
              connect_timeout: 1800
              <<: *ubuntu_account_host_03
            use_sudo: true
            sudo: *ubuntu_account_host_03
            process:
              env:
                join_command:
                  concat:
                  - "'"
                  - { get_attribute: [init_bootstrap_controlplane_node, join_command] }
                  - "'"
                role: "worker"
    relationships:
      - type: nativeedge.relationships.depends_on
        target: install_tools_host_03
      - type: nativeedge.relationships.depends_on
        target: init_bootstrap_controlplane_node

  join_worker_node_03:
    type: nativeedge.nodes.Root
    interfaces:
      nativeedge.interfaces.lifecycle:
        create:
          implementation: fabric.fabric_plugin.tasks.run_script
          max_retries: 0
          inputs:
            script_path: scripts/04_join_cluster.sh
            fabric_env:
              host: {get_input: host_ip_04}
              port: 22
              connect_timeout: 1800
              <<: *ubuntu_account_host_04
            use_sudo: true
            sudo: *ubuntu_account_host_04
            process:
              env:
                join_command:
                  concat:
                  - "'"
                  - { get_attribute: [init_bootstrap_controlplane_node, join_command] }
                  - "'"
                role: "worker"
    relationships:
      - type: nativeedge.relationships.depends_on
        target: install_tools_host_04
      - type: nativeedge.relationships.depends_on
        target: init_bootstrap_controlplane_node

  secrets:
    type: nativeedge.nodes.secrets.Writer
    properties:
      entries:
        kubeconfig: { get_attribute: [init_bootstrap_controlplane_node, kubeconfig] }
      do_not_delete: false
      variant: { get_input: kube_api_server_fqdn }
      separator: "-"
    relationships:
      - type: nativeedge.relationships.depends_on
        target: init_bootstrap_controlplane_node
      - type: nativeedge.relationships.depends_on
        target: join_worker_node_01
      - type: nativeedge.relationships.depends_on
        target: join_worker_node_02
      - type: nativeedge.relationships.depends_on
        target: join_worker_node_03