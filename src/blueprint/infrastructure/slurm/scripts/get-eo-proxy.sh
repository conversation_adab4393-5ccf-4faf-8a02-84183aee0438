#!/bin/bash
# Copyright (c) 2023 Dell Inc. or its subsidiaries. All Rights Reserved.
#
# This software contains the intellectual property of Dell Inc. or is licensed to Dell Inc. from third parties.
# Use of this software and the intellectual property contained therein is expressly limited to the terms and
# conditions of the License Agreement under which it is provided by or on behalf of Dell Inc. or its subsidiaries.


# This script retrieves the SSH connection details for a given agent_name using a GraphQL query.
# It then sets the runtime properties for the ECE proxy host and port.


# Remove the "ece-" prefix from the agent_name
agent_name=$(echo "$agent_name" | sed 's/ece-//')

# Log the modified agent_name
ctx logger info "agent_name: $agent_name"

# Define the GraphQL query to get the SSH connection details
script="mutation {
        getSSHConnection(input:{ serviceTag: \\\"${agent_name}\\\", port: 22}) {
                host
                port
        }
}"

# Remove newlines from the query
script="$(echo $script)"

# Send the GraphQL query to the EO proxy service and store the response
response=$(curl -f -s -H 'Content-Type: application/json' \
   -X POST -d "{\"query\": \"$script\"}" http://internal-ingress-gateway.istio-system.svc.cluster.local/eoproxy/api/v1/eoproxy)

# Check the exit code of the curl command
if [ $? -ne 0 ]; then
   echo "Error: $response"
   ctx logger error "Error: $response"
   exit 1
fi

# Log the response
ctx logger info "response: $response"

# Extract the host and port from the response using Python
host=$(python3 -c "import json; data = json.loads('$response'); print(data['data']['getSSHConnection']['host'])")
port=$(python3 -c "import json; data = json.loads('$response'); print(data['data']['getSSHConnection']['port'])")

# Set the runtime properties for the ECE proxy host and port
ctx instance runtime-properties "ece_proxy_host" "${host}"
ctx instance runtime-properties "ece_proxy_port" "${port}"

# Check if the host is set in the runtime properties
host_from_runtime_properties=$(ctx instance runtime_properties "ece_proxy_host")
if [ -z "$host_from_runtime_properties" ]; then
   ctx logger error "host_from_runtime_properties: $host_from_runtime_properties host: $host"
   exit 1
fi


# Check if the port is set in the runtime properties
port_from_runtime_properties=$(ctx instance runtime_properties "ece_proxy_port")
if [ -z "$port_from_runtime_properties" ]; then
   ctx logger error "port_from_runtime_properties: $port_from_runtime_properties port: $port"
   exit 1
fi

# Log the host and port
ctx logger info "host: $host port: $port"