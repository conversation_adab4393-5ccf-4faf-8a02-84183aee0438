#!/bin/bash
# Copyright (c) 2023 Dell Inc. or its subsidiaries. All Rights Reserved.
#
# This software contains the intellectual property of Dell Inc. or is licensed to Dell Inc. from third parties.
# Use of this software and the intellectual property contained therein is expressly limited to the terms and
# conditions of the License Agreement under which it is provided by or on behalf of Dell Inc. or its subsidiaries.


# This script retrieves the SSH connection details for a given agent_name using a GraphQL query.
# It then sets the runtime properties for the ECE proxy host and port.
set -ue

K8S_TOOL_CHAIN_FILE=""
CURRENT_DIR=$(pwd)
K8S_TOOL_CHAIN_FOLDER="k8s-tool-chain"
ARCH="amd64"
CNI_BIN_DEST="/opt/cni/bin"
USR_LOCAL_BIN_DEST="/usr/local/bin"
USR_LOCAL_SBIN_DEST="/usr/local/sbin"
USR_LOCAL_DEST="/usr/local"
UNIT_DEST="/etc/systemd/system"

# help info
function print_help {
  echo "Place k8s-tool-chain.tar.gz same folder to this script, and run it with sudo. usage: $0 [options]"
  echo
  echo "options:"
  echo "  -kf,  --k8s-file                    Set k8s-tool-chain.tar.gz file location"
  echo "  -h,   --help                        Show help"
  echo
  echo "sample: $0 -kf /home/<USER>/k8s-tool-chain.tar.gz"
}

check_file_exist() {
  local pattern=$1
  theFile=$(find "$CURRENT_DIR/$K8S_TOOL_CHAIN_FOLDER" -type f -name "$pattern" -print -quit)
  if [ -n "$theFile" ]; then
      echo "$theFile"
  else
      echo "1"
  fi
}

if [ $# -eq 0 ]; then
    echo "No arguments supplied !"
    print_help
    exit 1
fi

# parse args
while [[ "$#" -gt 0 ]]; do
  case $1 in
    -kf|--k8s-file)
      if [[ -z "$2" ]]; then
        echo "K8S_TOOL_CHAIN_FILE is empty !"
        print_help
        exit 1
      fi
      K8S_TOOL_CHAIN_FILE="$2"
      shift
      ;;
    -h|--help)
      print_help
      exit 0
      ;;
    *)
      echo "unknown option: $1"
      print_help
      exit 1
      ;;
  esac
  shift
done

# 1. check K8S_TOOL_CHAIN_FILE and unzip it
tar -zxvf $K8S_TOOL_CHAIN_FILE

if ! [ -d $CURRENT_DIR/$K8S_TOOL_CHAIN_FOLDER ]; then
  echo "$CURRENT_DIR/$K8S_TOOL_CHAIN_FOLDER is missing !"
  exit 1
fi

# 2. system configuration
cat <<EOF | sudo tee /etc/modules-load.d/k8s.conf
overlay
br_netfilter
EOF

sudo modprobe overlay
sudo modprobe br_netfilter

cat <<EOF | sudo tee /etc/sysctl.d/k8s.conf
net.bridge.bridge-nf-call-iptables  = 1
net.bridge.bridge-nf-call-ip6tables = 1
net.ipv4.ip_forward                 = 1
EOF

sudo sysctl --system

# 3 Install Containerd
containerdFile=$(check_file_exist "containerd.io*")
if [[ "$containerdFile" != "1" ]]; then
    echo "File found: $containerdFile"
else
    echo "No file found for installation binary for containerd!"
    exit 1
fi

dbuildxFile=$(check_file_exist "docker-buildx-*")
if [[ "$dbuildxFile" != "1" ]]; then
    echo "File found: $dbuildxFile"
else
    echo "No file found for installation binary for containerd!"
    exit 1
fi

dockerceFile=$(check_file_exist "docker-ce_*")
if [[ "$dockerceFile" != "1" ]]; then
    echo "File found: $dockerceFile"
else
    echo "No file found for installation binary for containerd!"
    exit 1
fi

dockercecliFile=$(check_file_exist "docker-ce-cli*")
if [[ "$dockercecliFile" != "1" ]]; then
    echo "File found: $dockercecliFile"
else
    echo "No file found for installation binary for containerd!"
    exit 1
fi

dockercomposeFile=$(check_file_exist "docker-compose-plugin*")
if [[ "$dockercomposeFile" != "1" ]]; then
    echo "File found: $dockercomposeFile"
else
    echo "No file found for installation binary for containerd!"
    exit 1
fi

dpkg -i $containerdFile $dbuildxFile $dockerceFile $dockercecliFile $dockercomposeFile


# 4.1 Install CNI plugins
cniPluginsFile=$(check_file_exist "cni-plugins-*")
if [[ "$cniPluginsFile" != "1" ]]; then
    echo "File found: $cniPluginsFile"
else
    echo "No file found for installation binary for CNI plugins !"
    exit 1
fi

mkdir -p "$CNI_BIN_DEST"
tar Cxzvf "$CNI_BIN_DEST" "$cniPluginsFile"

# 4.2 Install CRICTL
crictlFile=$(check_file_exist "crictl-*")
if [[ "$crictlFile" != "1" ]]; then
    echo "File found: $crictlFile"
else
    echo "No file found for installation binary for CRICTL !"
    exit 1
fi
tar Cxzvf "$USR_LOCAL_BIN_DEST" "$crictlFile"

# 4.3 Install kubelet and kubeadm
kubeadmFile=$(check_file_exist "kubeadm")
if [[ "$kubeadmFile" != "1" ]]; then
    echo "File found: $kubeadmFile"
else
    echo "No file found for installation binary for kubeadm !"
    exit 1
fi
sudo cp "$kubeadmFile" "$USR_LOCAL_BIN_DEST/kubeadm"
sudo chmod +x "$USR_LOCAL_BIN_DEST/kubeadm"

kubeletFile=$(check_file_exist "kubelet")
if [[ "$kubeletFile" != "1" ]]; then
    echo "File found: $kubeletFile"
else
    echo "No file found for installation binary for kubelet !"
    exit 1
fi
sudo cp "$kubeletFile" "$USR_LOCAL_BIN_DEST/kubelet"
sudo chmod +x "$USR_LOCAL_BIN_DEST/kubelet"

kubeletSvcFile=$(check_file_exist "kubelet.service")
if [[ "$kubeletSvcFile" != "1" ]]; then
    echo "File found: $kubeletSvcFile"
else
    echo "No file found for installation binary for kubelet.service !"
    exit 1
fi
sudo cp "$kubeletSvcFile" "$UNIT_DEST/kubelet.service"

kubeadmConfFile=$(check_file_exist "10-kubeadm.conf")
if [[ "$kubeadmConfFile" != "1" ]]; then
    echo "File found: $kubeadmConfFile"
else
    echo "No file found for installation binary for 10-kubeadm.conf !"
    exit 1
fi

sudo mkdir -p "$UNIT_DEST/kubelet.service.d"
sudo cp "$kubeadmConfFile" "$UNIT_DEST/kubelet.service.d/10-kubeadm.conf"

sudo systemctl daemon-reload
sudo systemctl enable --now kubelet

# 4.4 Install kubectl
kubectlFile=$(check_file_exist "kubectl")
if [[ "$kubectlFile" != "1" ]]; then
    echo "File found: $kubectlFile"
else
    echo "No file found for installation binary for kubectl"
    exit 1
fi

sudo install -o root -g root -m 0755 "$CURRENT_DIR/$K8S_TOOL_CHAIN_FOLDER/kubectl" "$USR_LOCAL_BIN_DEST/kubectl"

# 5 final check
containerdStatus=$(sudo systemctl status containerd.service | grep "Active:" | awk '{print $2}')
if [[ "$containerdStatus" == "active" ]]; then
 echo "containerd service is active "
else
 echo "containerd service is not active"
 exit 1
fi

kubeadmVer=$(sudo kubeadm version && echo "kubeadmVersionPrint")
if [[ "$kubeadmVer" == *"kubeadmVersionPrint" ]]; then
 echo "kubeadm installed"
else
 echo "kubeadm is not installed properly"
 exit 1
fi

kubectlVer=$(sudo kubectl version --client && echo "kubectlVersionPrint")
if [[ "$kubectlVer" == *"kubectlVersionPrint" ]]; then
 echo "kubectl installed"
else
 echo "kubectl not installed properly"
 exit 1
fi

kletSta=$(sudo systemctl status kubelet.service | grep "Active:" | awk '{print $2}')
if [[ "$kletSta" != "activ"* ]]; then
 echo "kubelet.service is not active or activating, $kletSta"
 exit 1
else
 echo "kubelet.service is active or activating"
fi

echo "Installation of containerd, kubectl, kubeadm, kubelet is done !"
