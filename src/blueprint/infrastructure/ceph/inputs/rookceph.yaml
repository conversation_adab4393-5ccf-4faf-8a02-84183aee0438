inputs:

  rook_ceph_repo_url:
    type: string
    display_label: "Rook Ceph Repo URL"
    description: "The Github repository URL of Rook Ceph"
    default: "https://github.com/rook/rook.git"
    hidden: true

  rook_ceph_release_name:
    type: string
    display_label: "Rook Ceph Release Name"
    description: "The realse name in Github Repo of Rook Ceph "
    default: "release-1.15"

  rook_ceph_default_storageclass_name:
    type: string
    display_label: "Rook Ceph Default Storageclass Name"
    description: "Storageclass name to set to default"
    default: "rook-ceph-block"
    constraints:
      - valid_values: ["rook-cephfs", "rook-nfs", "rook-ceph-block"]
    
  kubectl_binary_url:    
    type: string
    default: "https://dl.k8s.io/release/v1.31.0/bin/linux/amd64/kubectl"
    hidden: true