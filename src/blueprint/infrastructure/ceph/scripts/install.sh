#!/usr/bin/env bash

set -ex

ctx logger info "installing rook ceph..."

# deployment_path="/opt/manager/resources/deployments/${TENANT}/${DEPLOYMENT_ID}"
# cd "${deployment_path}" || exit
repo=${REPO_URL}
release=${RELEASE_NAME}
kubectl_binary=${KUBECTL_BINARY}
kubeconfig=${KUBECONFIG}

chmod +x ${kubectl_binary}
workdir=$(pwd)
kubeconfigfile=${workdir}/kubeconfig
echo "${kubeconfig}" >${kubeconfigfile}
kubectl="${kubectl_binary} --kubeconfig ${kubeconfigfile}"

cloned_dir=$(basename "${repo}" .git)
rm -rf "${cloned_dir}"
git clone --depth 1 --single-branch --branch "${release}" "${repo}"
pushd "${cloned_dir}/deploy/examples" || exit

ns="rook-ceph"
${kubectl} create -f crds.yaml -f common.yaml -f operator.yaml
${kubectl} wait --for=condition=ready --timeout=5m pod --all -n ${ns}
${kubectl} create -f cluster.yaml
${kubectl} wait --for=condition=ready --timeout=5m pod --all -n ${ns}
${kubectl} create -f filesystem.yaml
${kubectl} wait --for=condition=ready --timeout=5m pod --all -n ${ns}
${kubectl} get pods -n ${ns}

cephfs_sc_name="rook-cephfs"
nfs_sc_name="rook-nfs"
block_sc_name="rook-ceph-block"
${kubectl} apply -f csi/cephfs/storageclass.yaml
${kubectl} apply -f csi/rbd/storageclass.yaml
${kubectl} apply -f csi/nfs/storageclass.yaml

popd || exit

ctx instance runtime-properties ceph_fs_storageclass ${cephfs_sc_name}
ctx instance runtime-properties ceph_nfs_storageclass ${nfs_sc_name}
ctx instance runtime-properties ceph_block_storageclass ${block_sc_name}

ctx logger info "installed rook ceph..."
