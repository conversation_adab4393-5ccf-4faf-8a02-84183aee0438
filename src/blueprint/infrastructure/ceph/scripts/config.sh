#!/usr/bin/env bash

set -ex

ctx logger info "configuring rook ceph..."

sc_name=${DEFAULT_STORAGECLASS}
kubectl_binary=${KUBECTL_BINARY}
kubeconfig=${KUBECONFIG}

chmod +x ${kubectl_binary}
workdir=$(pwd)
kubeconfigfile=${workdir}/kubeconfig
echo "${kubeconfig}" >${kubeconfigfile}
kubectl="${kubectl_binary} --kubeconfig ${kubeconfigfile}"

${kubectl} patch storageclass "${sc_name}" -p '{"metadata": {"annotations":{"storageclass.kubernetes.io/is-default-class":"true"}}}'
${kubectl} get sc

ctx logger info "configured rook ceph..."
