#!/usr/bin/env bash

set -ex

# deployment_path="/opt/manager/resources/deployments/${TENANT}/${DEPLOYMENT_ID}"
# cd "${deployment_path}" || exit
# kubectl_bin="${deployment_path}/kubectl"

workdir=$(pwd)
kubectl_bin="${workdir}/kubectl"

if [ -e "${kubectl_bin}" ]; then
    ctx logger info "Kubectl already installed"
else
    ctx logger info "Installing kubectl..."
    curl -LO "${KUBECTL_SOURCE}" || {
        ctx logger info "Failed to download kubectl"
        exit 1
    }
fi
chmod +x "${kubectl_bin}"
${kubectl_bin} version
ctx logger info "Installed kubectl"
ctx instance runtime-properties kubectl "${kubectl_bin}"
