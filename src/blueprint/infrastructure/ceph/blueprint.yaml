tosca_definitions_version: cloudify_dsl_1_5

imports:
  - cloudify/types/types.yaml
  - inputs/common.yaml
  - inputs/rookceph.yaml

node_templates:

  kubectl_binary:  
    type: cloudify.nodes.SoftwareComponent
    interfaces:
      cloudify.interfaces.lifecycle: 
        create:
          implementation: scripts/kubectl_binary.sh
          executor: central_deployment_agent
          inputs:
            KUBECTL_SOURCE:
              get_input: kubectl_binary_url
            TENANT:
              get_sys: [tenant, name]
            DEPLOYMENT_ID:
              get_sys: [deployment, id]

  rook_ceph:
    type: cloudify.nodes.ApplicationModule
    relationships:
      - type: cloudify.relationships.depends_on
        target: kubectl_binary
    interfaces:
      cloudify.interfaces.lifecycle: 
        
        precreate:  
          implementation: scripts/disks.sh
          executor: central_deployment_agent
        
        create:
          implementation: scripts/install.sh
          executor: central_deployment_agent
          inputs:
            REPO_URL: 
              get_input: rook_ceph_repo_url
            RELEASE_NAME:
              get_input: rook_ceph_release_name
            KUBECTL_BINARY:
              { get_attribute: [kubectl_binary, kubectl] }
            KUBECONFIG:
              get_input: kubeconfig
        
        configure: 
          implementation: scripts/config.sh
          executor: central_deployment_agent
          inputs:
            DEFAULT_STORAGECLASS:
              get_input: rook_ceph_default_storageclass_name
            KUBECTL_BINARY:
              { get_attribute: [kubectl_binary, kubectl] }
            KUBECONFIG:
              get_input: kubeconfig


      # cloudify.interfaces.validation:
      #   create: 
      #     implementation: scripts/validate.sh
      #     executor: central_deployment_agent
          