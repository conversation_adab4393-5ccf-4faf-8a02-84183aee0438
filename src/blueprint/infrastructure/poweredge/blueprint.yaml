tosca_definitions_version: cloudify_dsl_1_5

imports:
  - cloudify/types/types.yaml

inputs:

  hardware_config:
    type: string
    description: hardware configuration
  deployment_id:
    type: deployment_id
    description: The deployment ID of the first machine

relationships:
  source_connected_to_target:
    derived_from: cloudify.relationships.connected_to
    source_interfaces:
      cloudify.interfaces.relationship_lifecycle:
        establish:
          implementation: scripts/configure_source_node.py
    target_interfaces:
      cloudify.interfaces.relationship_lifecycle:
        preconfigure:
          implementation: scripts/configure_target_node.py

node_templates:

  hardware_config:
    type: cloudify.nodes.Root
    relationships:
      - type: cloudify.relationships.depends_on
        target: ece_deployment
  
  ece_deployment:
    type: source_connected_to_target
    properties:
      resource_config:
        deployment:
          id: { get_input: deployment_id}