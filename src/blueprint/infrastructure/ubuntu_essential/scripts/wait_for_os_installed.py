import sys
import time
import paramiko

from nativeedge.state import ctx_parameters as inputs
from nativeedge import ctx

port = 22
WAIT_TIMEOUT_SECONDS = 60 * 30
TRY_INTERVAL_SECONDS = 30

def establish_ssh_connection(hostname, port, username, password):
    # Create an SSH client
    conn = paramiko.SSHClient()
    # Automatically add the server's host key
    conn.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    # Connect to the server
    conn.connect(hostname, port=port, username=username, password=password, timeout=60)
    return conn

# def sudo_execute_command(conn, command):
#     # process command
#     command = command.replace('"','\"')
#     command = 'sudo -s -- "{}"\n'.format(command)
#     # set get_pty to True to get a pseudo terminal
#     stdin, stdout, stderr = conn.exec_command(command, get_pty=True)
#     # write and flush the password to the remote terminal
#     stdin.write(password+'\n')
#     stdin.flush()
#     ctx.logger.error("STDERR:" + stderr.read().decode('utf-8'))

def main():
    global host, username, password
    host = inputs['OS_IP']
    username = inputs['OS_USERNAME']
    password = inputs['OS_PASSWORD']

    install_success = False
    end_time = time.time() + WAIT_TIMEOUT_SECONDS
    while time.time() < end_time:
        try:
            ctx.logger.info(f"Connecting to host: {host}")
            conn = establish_ssh_connection(host, port, username, password)
            _, stdout, _ = conn.exec_command("pwd")
            exit_status = stdout.channel.recv_exit_status()
            if exit_status == 0:
                conn.close()
                install_success = True
                break
        except Exception as e:
            ctx.logger.info(f"cannot establish connection to the {host}. sleep {TRY_INTERVAL_SECONDS}")
            time.sleep(TRY_INTERVAL_SECONDS)
    
    if install_success:
        ctx.logger.info("OS installed!!!")
    else:
        ctx.logger.error(f"failed to connect to {host} after {WAIT_TIMEOUT_SECONDS} seconds, TIMEOUT!!!")
        sys.exit(1)

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        ctx.logger.error("Error: %s" % str(e))
        sys.exit(3)