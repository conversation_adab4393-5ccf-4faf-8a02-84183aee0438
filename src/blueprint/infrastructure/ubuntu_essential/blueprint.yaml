tosca_definitions_version: cloudify_dsl_1_5

imports:
  - cloudify/types/types.yaml
  - inputs/common.yaml
  - inputs/ubuntu.yaml

node_templates:

  mount_seed_iso:  
    type: cloudify.nodes.SoftwareComponent
    interfaces:
      cloudify.interfaces.lifecycle: 
        create:
          implementation: scripts/mount-iso-to-2nd-virtual-media.py
          executor: central_deployment_agent
          inputs:
            IDRAC_IP:
              get_input: idrac_ip
            IDRAC_USERNAME:
              get_input: idrac_username
            IDRAC_PASSWORD:
              get_input: idrac_password
            SEED_ISO_URL:
              get_input: seed_iso_url
  
  mount_os_iso:
    type: cloudify.nodes.SoftwareComponent
    relationships:
      - type: cloudify.relationships.depends_on
        target: mount_seed_iso
    interfaces:
      cloudify.interfaces.lifecycle: 
        create:
          implementation: scripts/mount-iso-to-1st-virtual-media.py
          executor: central_deployment_agent
          inputs:
            IDRAC_IP:
              get_input: idrac_ip
            IDRAC_USERNAME:
              get_input: idrac_username
            IDRAC_PASSWORD:
              get_input: idrac_password
            OS_ISO_URL:
              get_input: os_iso_url

  wait_for_os:
    type: cloudify.nodes.SoftwareComponent
    relationships:
      - type: cloudify.relationships.depends_on
        target: mount_os_iso
    interfaces:
      cloudify.interfaces.lifecycle: 
        create:
          implementation: scripts/wait_for_os_installed.py
          executor: central_deployment_agent
          inputs:
            OS_IP:
              get_input: os_ip
            OS_USERNAME:
              get_input: os_username
            OS_PASSWORD:
              get_input: os_password
          