dsl_definitions:
  bootos_account: &bootos_account
    user: eceuser
    password: ecepassword

node_templates:
  eo_proxy:
    type: nativeedge.nodes.ApplicationModule
    interfaces:
      nativeedge.interfaces.lifecycle:
        start:
          implementation: scripts/get-eo-proxy.sh
          max_retries: 3
          inputs:
            agent_name: { get_input: host_servicetag }

  generate_cloudinit_config:
    type: nativeedge.nodes.Root
    interfaces:
      nativeedge.interfaces.lifecycle:
        start:
          implementation: scripts/generate_cloudinit_config.py
          inputs:
            ubuntu_os_version: { get_input: ubuntu_os_version }
            nfs_server_ip: { get_input: nfs_server_ip }
            nfs_server_path: { get_input: nfs_server_path }
            deployment_config: { get_input: deployment_config_host }
            endpoint: { get_attribute: [eo_proxy, ece_proxy_host] }
            port: { get_attribute: [eo_proxy, ece_proxy_port] }
            <<: *bootos_account
    relationships:
      - type: nativeedge.relationships.depends_on
        target: eo_proxy

  modify_mount_flag:
    type: nativeedge.nodes.Root
    interfaces:
      nativeedge.interfaces.lifecycle:
        create:
          implementation: fabric.fabric_plugin.tasks.run_commands
          max_retries: 3
          inputs:
            fabric_env:
              host: { get_attribute: [eo_proxy, ece_proxy_host] }
              port: { get_attribute: [eo_proxy, ece_proxy_port] }
              <<: *bootos_account
            use_sudo: true
            sudo:
              <<: *bootos_account
            commands:
              - mount -o remount,exec /tmp || true
    relationships:
      - type: nativeedge.relationships.depends_on
        target: generate_cloudinit_config

  install_provision_package:
    type: nativeedge.nodes.Root
    interfaces:
      nativeedge.interfaces.lifecycle:
        create:
          implementation: scripts/install_provision_package.py
          max_retries: 3
          inputs:
            host: { get_attribute: [eo_proxy, ece_proxy_host] }
            port: { get_attribute: [eo_proxy, ece_proxy_port] }
            <<: *bootos_account
    relationships:
      - type: nativeedge.relationships.depends_on
        target: modify_mount_flag

  ubuntu_provision:
    type: nativeedge.nodes.Root
    interfaces:
      nativeedge.interfaces.lifecycle:
        create:
          implementation: fabric.fabric_plugin.tasks.run_commands
          max_retries: 0
          inputs:
            fabric_env:
              host: { get_attribute: [eo_proxy, ece_proxy_host] }
              port: { get_attribute: [eo_proxy, ece_proxy_port] }
              <<: *bootos_account
            use_sudo: true
            sudo:
              <<: *bootos_account
            commands:
              - concat:
                - "bash /usr/share/dsp-os-provision/scripts/provision/provision.sh "
                - { get_attribute: [generate_cloudinit_config, config_path] }
    relationships:
      - type: nativeedge.relationships.depends_on
        target: install_provision_package

  reboot_host:
    type: nativeedge.nodes.Root
    interfaces:
      nativeedge.interfaces.lifecycle:
        create:
          implementation: scripts/reboot_node.py
          max_retries: 1
          inputs:
            host: { get_attribute: [eo_proxy, ece_proxy_host] }
            port: { get_attribute: [eo_proxy, ece_proxy_port] }
            connect_timeout: 300
            <<: *bootos_account
    relationships:
      - type: nativeedge.relationships.depends_on
        target: ubuntu_provision

  # # wait_provisioned_node_up:
  # #   type: nativeedge.nodes.Root
  # #   interfaces:
  # #     nativeedge.interfaces.lifecycle:
  # #       create:
  # #         implementation: scripts/wait_provisioned_node_up.py
  # #         max_retries: 30
  # #         retry_interval: 60
  # #         inputs:
  # #           conn_name: { get_input: host_servicetag }
  # #   relationships:
  # #     - type: nativeedge.relationships.depends_on
  # #       target: reboot_host

  wait_provisioned_node_up:
    type: nativeedge.nodes.Root
    interfaces:
      nativeedge.interfaces.lifecycle:
        create:
          implementation: scripts/wait_provisioned_node_up_lan.py
          max_retries: 180
          retry_interval: 10
          inputs:
            host_ip: { get_attribute: [generate_cloudinit_config, host_ip] }
    relationships:
      - type: nativeedge.relationships.depends_on
        target: reboot_host

  secrets:
    type: nativeedge.nodes.secrets.Writer
    properties:
      entries:
        connection-info:
          service_tag: { get_input: host_servicetag }
          node_access_ip: { get_attribute: [generate_cloudinit_config, host_ip] }
          username: { get_attribute: [generate_cloudinit_config, username] }
          password: { get_attribute: [generate_cloudinit_config, password] }
      do_not_delete: false
      variant: { get_input: host_servicetag }
      separator: "-"
    relationships:
      - type: nativeedge.relationships.depends_on
        target: wait_provisioned_node_up
