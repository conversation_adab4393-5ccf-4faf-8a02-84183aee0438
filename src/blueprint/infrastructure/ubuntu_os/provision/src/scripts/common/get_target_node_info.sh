#!/bin/bash
# Copyright (c) 2023 Dell Inc. or its subsidiaries. All Rights Reserved.
#
# This software contains the intellectual property of Dell Inc. or is licensed to Dell Inc. from third parties.
# Use of this software and the intellectual property contained therein is expressly limited to the terms and
# conditions of the License Agreement under which it is provided by or on behalf of Dell Inc. or its subsidiaries.

PACKAGE_NAME=dsp-os-provision
SCRIPT_NAME=$(basename "$0" .sh)
SCRIPTS_DIR=/usr/share/$PACKAGE_NAME/scripts
LOG_DIR=/var/run/$PACKAGE_NAME/log
LOG_FILE="$LOG_DIR/$SCRIPT_NAME.log"

source $SCRIPTS_DIR/common/common.sh
source $SCRIPTS_DIR/common/constants.sh
source $SCRIPTS_DIR/common/util.sh
source $SCRIPTS_DIR/common/get_target_disk.sh

HARDWARE_BUNDLE_KEY_WORD=BOOTSTRAP_OS
HARDWARE_BUNDLE_PREFIX="$BUNDLE_PREFIX"_FOUNDATION_"$HARDWARE_BUNDLE_KEY_WORD"

[ -d "$LOG_DIR" ] || mkdir -p "$LOG_DIR"
# get $TARGET_DISK
generate_boss_list &> /dev/null || true

if [[ -z "$TARGET_DISK" ]]; then
    echo "hardware_bundle_file_name: "
    echo "software_bundle_file_name: "
    echo "avaliable_space: "
    echo "rw_flag: "
    exit 0
fi

# get payload partition
target_disk_payload=$(lsblk -l -o PATH,PARTLABEL "$TARGET_DISK" | grep "$LABEL_PAYLOAD" | awk '{print $1}')

# mount payload partition
if ! mount | grep -q "$target_disk_payload"; then
    umount -fl "$PATH_PAYLOAD" &> /dev/null || true
    [ -d "$PATH_PAYLOAD" ] || mkdir -p "$PATH_PAYLOAD"
    mount "$target_disk_payload" "$PATH_PAYLOAD"
fi

# check hardware bundle and software bundle file name
hw_bundle_file_name=$(find "$PATH_PAYLOAD" -maxdepth 2 -type f -name '*.zip' -printf '%f\n' | grep -i "$HARDWARE_BUNDLE_PREFIX" | sort -r | head -n 1)
sw_bundle_file_name=$(find "$PATH_PAYLOAD" -maxdepth 2 -type f -name '*.zip' -printf '%f\n' | grep -i "$BUNDLE_PREFIX" | grep -iv "$HARDWARE_BUNDLE_PREFIX" | sort -r | head -n 1)

# check avaliable space
log_2_file "$LOG_FILE" "Mounting partition $target_disk_payload"
avaliable_space=$(df | grep "$target_disk_payload" | awk '{print $4}')

# check writable disk
rw_able_disk=$(grep 'rw' /proc/mounts | grep "$PATH_PAYLOAD" | awk '{print $1}')
if [ "$target_disk_payload" == "$rw_able_disk" ]; then
    rw_flag="true"
else
    rw_flag="false"
fi
# umount os and payload finally
umount -fl "$PATH_PAYLOAD" &> /dev/null || true

echo "hardware_bundle_file_name: $hw_bundle_file_name"
echo "software_bundle_file_name: $sw_bundle_file_name"
echo "avaliable_space: $avaliable_space"
echo "rw_flag: $rw_flag"
exit 0
