#!/bin/bash
# Copyright (c) 2023 Dell Inc. or its subsidiaries. All Rights Reserved.
#
# This software contains the intellectual property of Dell Inc. or is licensed to Dell Inc. from third parties.
# Use of this software and the intellectual property contained therein is expressly limited to the terms and
# conditions of the License Agreement under which it is provided by or on behalf of Dell Inc. or its subsidiaries.

PACKAGE_NAME=dsp-os-provision
SCRIPT_NAME=$(basename "$0" .sh)
SCRIPTS_DIR=/usr/share/$PACKAGE_NAME/scripts
LOG_DIR=/var/run/$PACKAGE_NAME/log
LOG_FILE="$LOG_DIR/$SCRIPT_NAME.log"

source $SCRIPTS_DIR/common/common.sh
source $SCRIPTS_DIR/common/constants.sh
source $SCRIPTS_DIR/common/util.sh

[ -d "$LOG_DIR" ] || mkdir -p "$LOG_DIR"

function generate_boss_list() {
    # BOSS_LIST stores device path of BOSS card
    BOSS_LIST=()

    # Get BOSS card
    for model in "${BOSS_MODEL[@]}"; do
        local boss_path
        local boss_path_array
        boss_path=$(lsblk -d -l -o PATH,MODEL | grep "$model" | awk '{print $1}')
        if [[ -n "$boss_path" ]]; then
            readarray -t boss_path_array <<<"$boss_path"
            BOSS_LIST+=("${boss_path_array[@]}")
        fi
    done

    log_2_file "$LOG_FILE" "BOSS card list: ${BOSS_LIST[*]}"

    if [[ "${#BOSS_LIST[@]}" -gt 0 ]]; then
        # Set the first BOSS card as a default BOSS card, no matter how many BOSS cards are there
        TARGET_DISK=${BOSS_LIST[0]}
        log_2_file "$LOG_FILE" "BOSS card found: $TARGET_DISK"
        if [[ "${BASH_SOURCE[0]}" == "$0" ]]; then
            lsblk --json "$TARGET_DISK" -o MODEL,NAME,PARTLABEL,PATH,SERIAL,SIZE,STATE,TYPE
        fi
    else
        service_tag=$(dmidecode -s system-serial-number)
        if [[ ${service_tag:0:6} == "VMware" ]];then
          TARGET_DISK="/dev/sda"
        else
          echo "{\"blockdevices\": []}"
          log_2_file "$LOG_FILE" "BOSS card not found. Exit!"
          return 1
        fi
    fi
}

function get_boss_controller() {
    local boss_slot
    local boss_svendor
    local boss_sdevice
    local boss_keyword="BOSS"

    boss_slot=$(lspci -Dmm | grep "$boss_keyword" | head -n1)
    if [[ -z "$boss_slot" ]]; then
        # Return empty json if BOSS controller is not found
        echo "{ \"SVendor\": \"\", \"SDevice\": \"\" }" | jq
        log_2_file "$LOG_FILE" "BOSS card controller not found"
        return 0
    fi

    boss_slot=$(echo "$boss_slot" | awk '{print $1}')

    # Get BOSS controller info
    boss_svendor=$(lspci -D -vmm -s "$boss_slot" | grep '^SVendor:' | cut -d':' -f2 | sed 's/^[[:space:]]*//')
    boss_sdevice=$(lspci -D -vmm -s "$boss_slot" | grep '^SDevice:' | cut -d':' -f2 | sed 's/^[[:space:]]*//')

    echo "{ \"SVendor\": \"$boss_svendor\", \"SDevice\": \"$boss_sdevice\" }" | jq
    log_2_file "$LOG_FILE" "BOSS card controller found: [$boss_slot] $boss_svendor $boss_sdevice"
}

function show_usage() {
    echo "Usage: $0"
    echo "Usage: $0 -c|--controller"
}

function parse_arguments() {
    if ! OPTIONS=$(getopt -o c:: --long controller:: -- "$@"); then
        show_usage
        exit 2
    fi
    eval set -- "$OPTIONS"

    arg_get_boss_controller=false

    while true; do
        case "$1" in
        -c | --controller)
            arg_get_boss_controller=true
            shift 2
            ;;
        --)
            shift
            break
            ;;
        *)
            echo "Invalid option: $1" >&2
            show_usage
            exit 3
            ;;
        esac
    done
}

# If script is sourced from another script, do not run the generate_boss_list function
if [[ "${BASH_SOURCE[0]}" == "$0" ]]; then
    parse_arguments "$@"
    if [[ "$arg_get_boss_controller" == true ]]; then
        get_boss_controller
    else
        generate_boss_list
    fi
fi
