#!/bin/bash
# Copyright (c) 2023 Dell Inc. or its subsidiaries. All Rights Reserved.
#
# This software contains the intellectual property of Dell Inc. or is licensed to Dell Inc. from third parties.
# Use of this software and the intellectual property contained therein is expressly limited to the terms and
# conditions of the License Agreement under which it is provided by or on behalf of Dell Inc. or its subsidiaries.

# To check if the input is empty
# usage: null_to_exit <input> <name>
function null_to_exit() {
    local input="$1"
    local name="$2"
    
    if [ -z "$input" ]; then
        echo "Error: $name is empty. Exit!" >&2
        exit 1
    fi
}
