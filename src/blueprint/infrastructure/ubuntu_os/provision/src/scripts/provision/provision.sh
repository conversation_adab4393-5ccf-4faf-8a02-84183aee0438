#!/bin/bash
# Copyright (c) 2023 Dell Inc. or its subsidiaries. All Rights Reserved.
#
# This software contains the intellectual property of Dell Inc. or is licensed to Dell Inc. from third parties.
# Use of this software and the intellectual property contained therein is expressly limited to the terms and
# conditions of the License Agreement under which it is provided by or on behalf of Dell Inc. or its subsidiaries.

set -eE
PACKAGE_NAME=dsp-os-provision
LOG_FILE_NAME="provision-os.log"
OUTPUT_FILE_NAME="output-provision-os.log"
LOG_FILE_DIR=/var/log/$PACKAGE_NAME
LOG_FILE_PATH=$LOG_FILE_DIR/$LOG_FILE_NAME
OUTPUT_FILE_PATH=$LOG_FILE_DIR/$OUTPUT_FILE_NAME
mkdir -p $LOG_FILE_DIR

exec &>> >(tee -ia $OUTPUT_FILE_PATH)

function provision_error_handler() {
    set +eE
    trap - ERR
    local error_line=${1}
    local exit_code=${2}
    log "Failed to provision OS when executing command: $BASH_COMMAND"
    echo "Failed to provision OS when executing command: $BASH_COMMAND"
    detach_loop_dev
    exit "$exit_code"
}
trap 'provision_error_handler ${LINENO} $?' ERR

SCRIPTS_DIR=/usr/share/$PACKAGE_NAME/scripts
CURRENT_DIR="$( cd -- "$(dirname "$0")" >/dev/null 2>&1 ; pwd -P )"
CURRENT_DIR=/usr/share/$PACKAGE_NAME/scripts/provision
MOUNT_ISO_SCRIPT_PATH="${CURRENT_DIR}/mount-iso-to-2nd-virtual-media.py"
WORK_DIR=/staging/$PACKAGE_NAME


source $SCRIPTS_DIR/common/common.sh
source $SCRIPTS_DIR/common/constants.sh
source $SCRIPTS_DIR/common/get_target_disk.sh
source $SCRIPTS_DIR/common/stage_mgmt.sh
source $SCRIPTS_DIR/common/util.sh
source $SCRIPTS_DIR/provision/prepare.sh

declare -i stage_no
declare -i step_no
stage_no=-1
step_no=-1
stage_just_changed=false
seed_iso_path=

stages=(
  "Initialization"
  "Precheck"
  "Provision"
)

init_steps=(
    "prepare"
    "parse_os_provision_param"
)

precheck_steps=(
    " "
)

provision_steps=(
    "fetch_required_info"
    "download_os_image"
    "convert_qcow2_image_to_raw"
    "mount_os_raw_image"
    "clean_stale_partition"
    "create_os_partitions"
    "alter_partitions"
    "read_partition_info"
    "burn_image"
    "resize_paritions"
    "verify_partitions"
    "detach_loop_dev"
    "alter_boot_entry"
    "generate_cloud_init_seed_iso"
    "upload_seed_iso_to_nfs_server"
    "mount_seed_iso"
)

steps+=("${init_steps[@]}")
steps+=("${precheck_steps[@]}")
steps+=("${provision_steps[@]}")

function main() {
  if [[ -n "$1" ]]; then
      OS_PARAMETER_FILE="$1"
  fi
  init
  precheck
  provision
}

function init(){
  prepare
  init_progress
  next_stage
  next_step

  rm -rf $WORK_DIR
  mkdir -p $WORK_DIR

  stages_num=${#stages[@]}
  steps_num=${#steps[@]}
  set_total_steps_stages

  parse_os_provision_param

  #Backup ECE agent stuff, to be able to revert back to current OS in case
  mkdir -p /staging/bakup/db
  mkdir -p /staging/bakup/crt
  cp /hzpencrypt/ece*.db /staging/bakup/db || true
  cp /hzp/*.crt /staging/bakup/crt || true
  log "Finish init"
}

function fetch_required_info(){
  next_step
  generate_boss_list &> /dev/null || true
}

function detach_loop_dev(){
  if [[ -n $loop_dev ]];then
      # Detach the loop device
      losetup -d "$loop_dev" || true
  fi
}

function parse_os_provision_param(){
    log "Parse OS provision Params"
    next_step
    #====Read GLOBAL Parameter =====
    os_image_url=$(yq e '.. | select(has("os_image_url")) | .os_image_url' $OS_PARAMETER_FILE)
    nfs_server_ip=$(yq e '.. | select(has("nfs_server_ip")) | .nfs_server_ip' $OS_PARAMETER_FILE)
    nfs_server_path=$(yq e '.. | select(has("nfs_server_path")) | .nfs_server_path' $OS_PARAMETER_FILE)
}

function precheck(){
  #TODO To add some precheck actions
  next_stage
  next_step
  return
}

function download_os_image(){
    log "Attempt to download OS image"
    next_step
    os_image_path=""
    # check if os_image_url starts with "/"
    if [[ ${os_image_url:0:7} == "file://" ]];then
        log "OS image url is a local file path: $os_image_url"
        os_image_path="${os_image_url#file://}"
    fi

    if [[ ${os_image_url:0:7} == "file://" ]];then
        log "OS image url is a local file path: $os_image_url"
        os_image_path="${os_image_url#file://}"
    elif [[ "$os_image_url" =~ http* ]];then
        [ -d /staging ] || mkdir /staging
        os_image_name=$(basename "$os_image_url")
        os_image_path="/staging/$os_image_name"
        curl -s -k -L $os_image_url -o /staging/$(basename $os_image_url)
    else
        echo "Unsupported protocol in URL: $image_url" >&2
        exit 1
    fi
    os_image_name=$(basename "$os_image_path")
    os_raw_image_name=${os_image_name}.raw
    os_raw_image_path=${WORK_DIR}/${os_raw_image_name}
}

function convert_qcow2_image_to_raw(){
    log "Convert QCOW2 image to RAW"
    next_step
    qemu-img convert -f qcow2 -O raw $os_image_path $os_raw_image_path
}

function mount_os_raw_image(){
    log "Mount Bootstrap OS image to loop device"
    next_step

    loop_dev=$(losetup --show -f -P "$os_raw_image_path")
    partprobe $loop_dev
    #    loop_dev=/dev/loop4
    sleep 1
}

function clean_stale_partition(){
    log "Removing stale partitions on $TARGET_DISK"
    #Ubuntu use partition number 21 and 22
    #Clean this two partitions ahead before proceeding
    next_step
    sync
    partprobe "$TARGET_DISK"
    sleep 1
  fdisk $TARGET_DISK <<EOF
d
21
d
22
w
EOF
    sync
    partprobe "$TARGET_DISK"
    sleep 1
}

function create_os_partitions(){
    log "Creating new partitions on $TARGET_DISK"
    next_step
    fdisk $TARGET_DISK <<EOF
n
21

$SIZE_EFI
n
22


w
EOF
    sync
    partprobe "$TARGET_DISK"
    sleep 1
}

function alter_partitions(){
    log "Alter partitions"
    next_step
    sgdisk --typecode=21:EF00 --change-name=21:"$LABEL_EFI" "$TARGET_DISK"
    sync
    partprobe "$TARGET_DISK"
    sleep 1
    sgdisk --typecode=22:8300 --change-name=22:"$LABEL_OS" "$TARGET_DISK"
    sync
    partprobe "$TARGET_DISK"
    sleep 1

    # Kernel need about 0.05s to reload partition
    log "Verifing disk $TARGET_DISK"
    sgdisk --verify "$TARGET_DISK"
}

function read_partition_info(){
    log "Getting disk partition info by label"
    next_step
    sync
    partprobe "$TARGET_DISK"
    sleep 1
    target_disk_efi=$(lsblk -l -o PATH,PARTLABEL "$TARGET_DISK" | grep "$LABEL_EFI" | awk '{print $1}')
    target_disk_os=$(lsblk -l -o PATH,PARTLABEL "$TARGET_DISK" | grep "$LABEL_OS" | awk '{print $1}')
}

function burn_image(){
    log "Burning new OS image to $TARGET_DISK"
    next_step
    # copy EFI partition to target disk
    log "Burning EFI partition to disk"
    dd if="${loop_dev}p15" of="$target_disk_efi" bs=4M conv=notrunc iflag=direct oflag=direct status=progress
    sync
    # copy OS partition to target disk
    log "Burning OS partition to disk"
    dd if="${loop_dev}p1" of="$target_disk_os" bs=8M conv=notrunc iflag=direct oflag=direct status=progress
    sync
}

function resize_paritions(){
    log "Resizing partition $target_disk_os"
    next_step
    # To resize the file system
#    log "Resizing partition $target_disk_efi"
#    fatresize "$target_disk_efi" -s max || true
    resize2fs -f "$target_disk_os"
}

function verify_partitions(){
    log "Verifing disk $TARGET_DISK"
    next_step
    sgdisk --verify "$TARGET_DISK"
    sleep 1
}

function alter_boot_entry(){
    log "Alter EFI boot entry"
    next_step
    # setup EFI boot manager
    log "Setting up EFI boot manager: $BOOT_ENTRY_NAME"
    efi_part_num=${target_disk_efi##*[!0-9]}
    existing_dsp_boot_num=$(efibootmgr | grep $BOOT_ENTRY_NAME | awk '{print $1}' | grep -oP 'Boot\K[0-9A-F]{4}') || true
    if [[ -n $existing_dsp_boot_num ]];then
      efibootmgr -B -b $existing_dsp_boot_num
    fi

    efibootmgr -c -d "$TARGET_DISK" -p "$efi_part_num" -L "$BOOT_ENTRY_NAME" -l "\EFI\BOOT\BOOTX64.EFI"
    efibootmgr -v
}

function generate_cloud_init_seed_iso(){
    log "Generate cloud-init config ISO"
    next_step
    generate_cloud_init_meta_data
    generate_cloud_init_network_config
    generate_cloud_init_user_data
    build_cloud_config_iso
    log "Gernated cloud init seed iso: $seed_iso_path"
}

function generate_cloud_init_meta_data(){
  stamp=$(date +'%Y-%m-%d-%H-%M-%S')
  echo "instance-id: outcome-unbuntu-${stamp}" > $WORK_DIR/meta-data
}

function generate_cloud_init_network_config(){
  yq '.. | select(has("network-config")) | .network-config' $OS_PARAMETER_FILE > $WORK_DIR/network-config
#  yq '.ethernets[] |= {"match": {"macaddress": .macaddress}} + with_entries(select(.key != "macaddress"))' $WORK_DIR/network-config -i
}

function generate_cloud_init_user_data(){
  echo -e "#cloud-config" > $WORK_DIR/user-data
  yq '.. | select(has("user-data")) | .user-data' $OS_PARAMETER_FILE >> $WORK_DIR/user-data
}

function build_cloud_config_iso(){
    log "Build cloud config iso"
    local service_tag=$(dmidecode -s system-serial-number)
    local seed_iso_name="seed-$service_tag.iso"
    seed_iso_path="$WORK_DIR/$seed_iso_name"
    genisoimage -output $seed_iso_path -volid cidata -joliet -rock $WORK_DIR/meta-data $WORK_DIR/network-config $WORK_DIR/user-data
}

function upload_seed_iso_to_nfs_server(){
    log "Upload seed ISO to NFS server"
    next_step
    mkdir -p /nfs
    mount -t nfs -o nfsvers=3 $nfs_server_ip:$nfs_server_path /nfs
    mkdir -p /nfs/$PACKAGE_NAME
    cp $seed_iso_path /nfs/$PACKAGE_NAME
}

function mount_seed_iso(){
    log "Mount Seed ISO"
    next_step
    seed_iso_name=$(basename "$seed_iso_path")
    python3 ${MOUNT_ISO_SCRIPT_PATH} --iso-url $nfs_server_ip:$nfs_server_path/$PACKAGE_NAME/$seed_iso_name
}

function provision(){
    log "==================== Provision OS Start ===================="
    next_stage
    log "TARGET_DISK = $TARGET_DISK"
    log "totalStages: $stages_num"
    log "totalSteps: $steps_num"

    detach_loop_dev
    fetch_required_info
    download_os_image
    convert_qcow2_image_to_raw
    mount_os_raw_image

    clean_stale_partition
    create_os_partitions
    alter_partitions
    read_partition_info
    burn_image
    resize_paritions
    verify_partitions

    detach_loop_dev
    alter_boot_entry
    generate_cloud_init_seed_iso
    upload_seed_iso_to_nfs_server
    mount_seed_iso

    log "==================== Provisioning OS Successfully Complete ===================="
}

main "$@"