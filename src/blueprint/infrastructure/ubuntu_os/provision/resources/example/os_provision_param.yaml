os_image_url: http://************:30080/dsp-os-provision/jammy-server-cloudimg-amd64.img
nfs_server_ip: ************
nfs_server_path: /data
dns_server: ***********
---
config:
  network-config:
    version: 2
    ethernets:
      eth1:
        match:
          macaddress: "ec:2a:72:51:2f:a8"
        addresses:
          - **********32/24
        gateway4: **********
        nameservers:
          addresses:
            - ***********
  user-data:
    hostname: hostname-1
    fqdn: hostname-1.fqdn.local
    create_hostname_file: true
    ntp:
      servers:
        - ***********
    users:
      - name: mystic
        sudo: ALL=(ALL:ALL) ALL
        lock_passwd: false
        groups: root,sudo
        plain_text_passwd: mystic
    ssh_pwauth: true
    disable_root: false
    chpasswd:
      list:
        - root:mysitc
      expire: false