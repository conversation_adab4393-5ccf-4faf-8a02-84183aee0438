#!/bin/bash
# Copyright (c) 2023 Dell Inc. or its subsidiaries. All Rights Reserved.
#
# This software contains the intellectual property of Dell Inc. or is licensed to Dell Inc. from third parties.
# Use of this software and the intellectual property contained therein is expressly limited to the terms and
# conditions of the License Agreement under which it is provided by or on behalf of Dell Inc. or its subsidiaries.

UTIL_PKG_NAME=dsp-os-provision
UTIL_SCRIPTS_DIR=/usr/share/$UTIL_PKG_NAME/scripts
CFG_FILE=/var/run/$UTIL_PKG_NAME/config.yaml
OS_PARAMETER_FILE=/var/run/$UTIL_PKG_NAME/os_provision_param.yaml
CONSOLE_FIFO=/var/run/$UTIL_PKG_NAME/console.fifo

BOSS_MODEL=(
    "DELLBOSS VD"
    "Dell BOSS-N1"
)

BUNDLE_PREFIX=DELL_APEX_CLOUD_PLATFORM

LABEL_LEGACY=LEGACY
LABEL_EFI=DSP-BOOT
LABEL_OS=DSP-SYSTEM
LABEL_PAYLOAD=PAYLOAD

SIZE_LEGACY=+4M
SIZE_EFI=+120M
SIZE_OS=+100G
SIZE_PAYLOAD=-100G
SIZE_RESERVED=0

PATH_SYSTEM=/mnt/system
PATH_PAYLOAD=/mnt/payload
PATH_BUNDLE=$PATH_PAYLOAD/factory

BOOT_ENTRY_NAME=DSP_BOOT