#!/bin/bash

PACKAGE_NAME=dsp-os-provision

function install_yq(){
  # install yq
  YQ_VERSION=v4.27.2
  YQ_BINARY=yq_linux_amd64
  wget https://github.com/mikefarah/yq/releases/download/${YQ_VERSION}/${YQ_BINARY}.tar.gz -O - |\
    tar xz && mv ${YQ_BINARY} /usr/bin/yq
}

#TODO This is a workaround that currently the apt source is blokced
# Use tuna mirror instead
function replace_apt_source(){
  cat > /etc/apt/sources.list <<EOF
deb https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ noble main restricted universe multiverse
deb https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ noble-updates main restricted universe multiverse
deb https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ noble-backports main restricted universe multiverse
EOF
}

function apt_update(){
  systemctl unmask packagekit
  systemctl daemon-reload
  apt update
}

function install_fatresize(){
  apt_install fatresize
}

function install_parted(){
  apt_install parted
}

function install_nfs_common(){
  apt_install nfs-common
}

function apt_install(){
  local pkg_name=$1
  DEBIAN_FRONTEND=noninteractive apt install -y $pkg_name
}

function mark_prepared(){
  mkdir -p /var/run/$PACKAGE_NAME
  touch /var/run/$PACKAGE_NAME/prepared.flag
}

function is_prepared(){
  if [[ -f /var/run/$PACKAGE_NAME/prepared.flag ]];then
    echo "true"
  else
    echo "false"
  fi
}

#T
function add_dns(){
  dns_server=$(grep "dns_server" $OS_PARAMETER_FILE | cut -d ' ' -f 2)
  if [[ -z $dns_server ]]; then
    log "Skip adding DNS as no DNS configured"
    return
  else
    log "Attempt to add dns: $dns_server"
  fi

  if ! grep -q "nameserver ${dns_server}" /etc/resolv.conf; then
      sed -i "/nameserver **********/i\nameserver ${dns_server}" /etc/resolv.conf
  else
      echo "nameserver ${dns_server} already exists in /etc/resolv.conf"
  fi
}

function prepare(){
  local is_provision_prepared=$(is_prepared)
  if [[ $is_provision_prepared != "true" ]];then
    log "Not prepared, execute preparation"
  else
    log "Prepared, skip preparation"
    return
  fi

  ufw disable || true
  replace_apt_source
  add_dns
  apt_update
  install_yq
  install_parted
#  install_fatresize
  install_nfs_common
  mkdir -p /var/run/$PACKAGE_NAME
  touch /var/run/$PACKAGE_NAME/prepared.flag
}

if [[ "${BASH_SOURCE[0]}" == "$0" ]]; then
  prepare
fi