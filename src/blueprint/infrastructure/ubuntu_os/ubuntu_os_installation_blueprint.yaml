# Copyright (c) 2023 Dell Inc. or its subsidiaries. All Rights Reserved.
#
# This software contains the intellectual property of Dell Inc. or is licensed to Dell Inc. from third parties.
# Use of this software and the intellectual property contained therein is expressly limited to the terms and
# conditions of the License Agreement under which it is provided by or on behalf of Dell Inc. or its subsidiaries.

tosca_definitions_version: nativeedge_1_0
description: >
  The blueprint is used to create ubuntu 5 node outcome.

imports:
  - nativeedge/types/types.yaml
  - plugin:nativeedge-fabric-plugin
  - plugin:nativeedge-utilities-plugin
  - inputs/ubuntu_os_installation_inputs.yaml
  - nodes/ubuntu_os_installation_definitions.yaml

capabilities:
  secret_name:
    description: "The secret that contains host info, including: service_tag, username, password, node_access_ip"
    value:
      concat:
      - "connection-info-"
      - { get_input: host_servicetag }

labels:
  csys-obj-type:
    values:
      - environment
  target_environment:
    values:
      - ece
  vendor:
    values:
      - canonical
  solution:
    values:
      - ubuntu
  version:
    values:
      - "0.1"

blueprint_labels:
  env:
    values:
      - NED