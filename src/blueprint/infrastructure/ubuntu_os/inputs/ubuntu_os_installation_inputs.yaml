inputs:
  ubuntu_os_version:
    type: string
    display_label: "Ubuntu Image OS version"
    description: "Ubuntu Image OS version"
    required: true
    default: "22.04"

  nfs_server_ip: 
    type: string
    display_label: "NFS Server IP"
    description: "NFS Server IP"
    default: "************"
    required: true
  
  nfs_server_path:
    type: string
    display_label: "NFS Server Path"
    description: "NFS Server Path"
    default: "/data"
    required: true

  # Endpoint 1 host config
  host_servicetag:
    type: deployment_id
    display_label: "Endpoint Service Tag"
    description: Service Tag of the ECE.
    required: true
    constraints:
      - labels:
          - cloud:
              edge-cloud

  deployment_config_host:
    type: dict
    display_label: "Endpoint #1 Deployment Configuration"
    description: "Deployment Configuration about network, user and image link"
    default:
      hostname: host01
      top_level_domain: ubuntu.local
      ntp_servers:
      - ***********
      ethernets:
      - macaddress: ec:2a:72:4c:45:f4
        addresses:
        - ************/24
        gateway: **********
        nameservers:
        - ***********
      users:
      - name: mystic
        groups:
        - sudo
        passwd: Password123!
