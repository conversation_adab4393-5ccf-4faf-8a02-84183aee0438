#!/usr/bin/python3

from nativeedge import ctx
from nativeedge.state import ctx_parameters as inputs
from time import sleep
import paramiko
import sys

ctx.logger.info("start rebooting node")

def establish_ssh_connection(hostname, port, username, password):
    # Create an SSH client
    conn = paramiko.SSHClient()
    # Automatically add the server's host key
    conn.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    # Connect to the server
    conn.connect(hostname, port=port, username=username, password=password, timeout=60)
    return conn

def sudo_execute_command(conn, command):
    # process command
    command = command.replace('"','\"')
    command = 'sudo -s -- "{}"\n'.format(command)
    # set get_pty to True to get a pseudo terminal
    stdin, stdout, stderr = conn.exec_command(command, get_pty=True)
    # write and flush the password to the remote terminal
    stdin.write(password+'\n')
    stdin.flush()
    ctx.logger.error("STDERR:" + stderr.read().decode('utf-8'))

if __name__ == "__main__":
    reboot_success = False
    # Read parameters from env var
    host=inputs['host']
    port=inputs['port']
    user=inputs['user']
    password=inputs['password']

    # Establish ssh connection and execute test command
    try:
        conn = establish_ssh_connection(host,port,user,password)
        sudo_execute_command(conn, "pwd")
        conn.close()
    except Exception as e:
        ctx.logger.error("cannot establish connection to the host. {}".format(str(e)))
        sys.exit(1)

    # Send reboot command to remote host
    for _ in range(10):
        ctx.logger.info("inputs: {}".format(inputs))
        try:
            ctx.logger.info("Executing the reboot command")
            conn = establish_ssh_connection(host,port,user,password)
            sudo_execute_command(conn, "reboot")
            conn.close()
        except Exception:
            # reboot success if connection cannot be establish or the command cannnot be executed.
            ctx.logger.info("host is down => sleep for 10 mins")
            reboot_success = True
            sleep(600)
            break
        sleep(10)

    if not reboot_success:
        # exit with error if the it does not catch a down status during the polling time
        ctx.logger.error("cannot down the node")
        sys.exit(1)