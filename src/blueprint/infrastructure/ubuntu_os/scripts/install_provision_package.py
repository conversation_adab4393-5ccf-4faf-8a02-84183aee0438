#!/usr/bin/env python3
import re
import tempfile
from fabric import Connection

from nativeedge.state import ctx_parameters as inputs
from nativeedge import ctx
from invoke.watchers import FailingResponder

LOCAL_DEB_PATH = "resources/dsp-os-provision-1.0.deb"
REMOTE_DEB_PATH = "/tmp/dsp-os-provision-1.0.deb"


# transport the files to the remote server
def tansport(connsection, local_path, remote_path):
    """
    a function that downloads a file from the manager and uploads it to the remote server
    """
    with tempfile.NamedTemporaryFile(delete=False) as f:
        f.close()
        ctx.download_resource(local_path, target_path=f.name)
        connsection.put(f.name, remote=remote_path)
        ctx.logger.info('source {0} , target {1}'.format(local_path, remote_path))

# eceuser_sudo is a function that returns a sudo command and watcher for
# use with `connection.run()` under a pyinvoke context manager.
def eceuser_sudo(command, user, pwd):
    """
    A function that returns a sudo command and watcher for
    use with `connection.run()` under a pyinvoke context manager.
    """
    prompt = '[sudo] password for {0}:'.format(user)
    password = '{0}'.format(pwd)
    cmd_str = "sudo -S -p '{}' {}".format(prompt, command)
    watcher = FailingResponder(
        pattern=re.escape(prompt),
        response="{}\n".format(password),
        sentinel="Sorry, try again.\n",
    )
    return cmd_str, watcher

if __name__ == "__main__":
    # get inputs
    user = inputs.get('user')
    password = inputs.get('password')
    host = inputs.get('host')
    port = inputs.get('port')

    ctx.logger.info('host {0} , port {1}'.format(host, port))
    # create connection with timeout and banner_timeout
    connection=Connection(host=host, user=user, port=port, connect_kwargs={'password': password, "timeout": 10,'banner_timeout': 60})

    # copy files
    tansport(connection, LOCAL_DEB_PATH, REMOTE_DEB_PATH)

    # install deb
    ctx.logger.info('start install ubuntu provision deb package')
    command, watcher = eceuser_sudo(f'dpkg -i {REMOTE_DEB_PATH}', user, password)
    result = connection.run(command, pty=True, hide=False, watchers=[watcher])
    ctx.logger.info('cmd {0} , result {1}'.format(command, result))
    ctx.logger.info('deb installation complete!')