#!/bin/bash

if [[ ${os_image_url:0:7} == "file://" ]];then
  log "OS image url is a local file path: $os_image_url"
  os_image_path="${os_image_url#file://}"
elif [[ "$os_image_url" =~ http* ]];then
    ufw disable
    [ -d /staging ] || mkdir /staging
    os_image_path="/staging/$(basename $os_image_url)"
    curl -s -k -L $os_image_url -o /staging/$(basename $os_image_url)
else
    echo "Unsupported protocol in URL: $image_url" >&2
    exit 1
fi

# [ -d "/staging" ] || mkdir /staging
# cd /staging

# if [[ "$image_url" =~ file://* ]];then
#     path=${image_url#file://}
#     if [ -f "$path" ];then
#         echo "File exists: $path"
#     else
#         echo "File does not exist: $path" >&2
#         exit 1
#     fi
#     image_path="$image_url"
# elif [[ "$image_url" =~ http* ]];then
#     ufw disable
#     curl -s -LO $image_url
#     image_path="file://$(readlink -f $(basename $image_url))"
# else
#     echo "Unsupported protocol in URL: $image_url" >&2
#     exit 1
# fi
# ctx instance runtime-properties "image_path" "${image_path}"
# echo "image_path: $image_path"