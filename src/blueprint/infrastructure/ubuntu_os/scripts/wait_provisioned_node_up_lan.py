#!/usr/bin/env python3


# wait provisioned node up in lan network
import socket
from nativeedge import ctx
from nativeedge.state import ctx_parameters as inputs
from nativeedge.exceptions import RecoverableError

def check_ssh(server_ip, port=22):
    try:
        test_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        socket.setdefaulttimeout(3)
        test_socket.connect((server_ip, port))
        response = test_socket.recv(1024).decode('utf8')
        if response.startswith("SSH"):
            return True
    except Exception as ex:
        print(ex)
        return False
    else:
        test_socket.close()
    return False

if __name__ == "__main__":
    host_ip = inputs['host_ip']
    if not check_ssh(host_ip):
        raise RecoverableError(f"{host_ip} is not reachable.")
    ctx.logger.info(f"{host_ip} is up")