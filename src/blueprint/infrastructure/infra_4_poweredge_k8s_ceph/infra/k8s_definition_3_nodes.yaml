tosca_definitions_version: nativeedge_1_0


node_templates:
  # cp_node1 G364H24
  cp_node1:
    type: nativeedge.nodes.ServiceComponent
    properties:
      resource_config:
        blueprint:
          external_resource: true
          id: x_ubuntu_os
        deployment:
          display_name:
            concat:
              - { get_sys: [deployment, name] }
              - "-cp_node1-G364H24"
          auto_inc_suffix: true
          inputs:
            host_servicetag: { get_input: host1_servicetag }
            nfs_server_ip: "************"
            nfs_server_path: /data
            ubuntu_os_version: "22.04"
            deployment_config_host: 
              ethernets:
              - addresses:
                - *************/24
                gateway: ***********
                macaddress: a0:88:c2:ff:45:ce
                nameservers:
                - ***********
              hostname: G364H24
              ntp_servers:
              - ***********
              top_level_domain: ubuntu.local
              users:
              - groups:
                - sudo
                name: mystic
                passwd: mystic

  # worker node1 H364H24
  worker_node1:
    type: nativeedge.nodes.ServiceComponent
    properties:
      resource_config:
        blueprint:
          external_resource: true
          id: x_ubuntu_os
        deployment:
          display_name:
            concat:
              - { get_sys: [deployment, name] }
              - "-worker_node1-H364H24"
          auto_inc_suffix: true
          inputs:
            host_servicetag: { get_input: host2_servicetag }
            nfs_server_ip: "************"
            nfs_server_path: /data
            ubuntu_os_version: "22.04"
            deployment_config_host: 
              ethernets:
              - addresses:
                - ***********32/24
                gateway: ***********
                macaddress: a0:88:c2:ff:48:8e
                nameservers:
                - ***********
              hostname: H364H24
              ntp_servers:
              - ***********
              top_level_domain: ubuntu.local
              users:
              - groups:
                - sudo
                name: mystic
                passwd: mystic

  # worker node2 J364H24
  worker_node2:
    type: nativeedge.nodes.ServiceComponent
    properties:
      resource_config:
        blueprint:
          external_resource: true
          id: x_ubuntu_os
        deployment:
          display_name:
            concat:
              - { get_sys: [deployment, name] }
              - "-worker_node2-J364H24"
          auto_inc_suffix: true
          inputs:
            host_servicetag: { get_input: host3_servicetag }
            nfs_server_ip: "************"
            nfs_server_path: /data
            ubuntu_os_version: "22.04"
            deployment_config_host: 
              ethernets:
              - addresses:
                - ***********33/24
                gateway: ***********
                macaddress: a0:88:c2:ff:47:82
                nameservers:
                - ***********
              hostname: J364H24
              ntp_servers:
              - ***********
              top_level_domain: ubuntu.local
              users:
              - groups:
                - sudo
                name: mystic
                passwd: mystic

  # worker node3 1464H24
  worker_node3:
    type: nativeedge.nodes.ServiceComponent
    properties:
      resource_config:
        blueprint:
          external_resource: true
          id: x_ubuntu_os
        deployment:
          display_name:
            concat:
              - { get_sys: [deployment, name] }
              - "-worker_node3-1464H24"
          auto_inc_suffix: true
          inputs:
            host_servicetag: { get_input: host4_servicetag }
            nfs_server_ip: "************"
            nfs_server_path: /data
            ubuntu_os_version: "22.04"
            deployment_config_host: 
              ethernets:
              - addresses:
                - ***********34/24
                gateway: ***********
                macaddress: a0:88:c2:ff:48:0a
                nameservers:
                - ***********
              hostname: 1464H24
              ntp_servers:
              - ***********
              top_level_domain: ubuntu.local
              users:
              - groups:
                - sudo
                name: mystic
                passwd: mystic

  k8s_os_secrets:
    type: nativeedge.nodes.secrets.Writer
    properties:
      entries:
          username: mystic
          password: mystic
      variant: mystic_credential
      do_not_delete: false

  k8s:
    type: nativeedge.nodes.ServiceComponent
    properties:
      resource_config:
        blueprint:
          external_resource: true
          id: x_kubernates
        deployment:
          display_name:
            concat:
              - { get_sys: [deployment, name] }
              - "-k8s"
          auto_inc_suffix: true
          inputs:
            # cp node
            host_ip_01: *************
            # worker 1
            host_ip_02: ***********32
            # worker 2
            host_ip_03: ***********33
            # worker 3
            host_ip_04: ***********34
            # fqnd
            kube_api_server_fqdn: *************
    relationships:
      - type: nativeedge.relationships.depends_on
        target: cp_node1
      - type: nativeedge.relationships.depends_on
        target: worker_node1
      - type: nativeedge.relationships.depends_on
        target: worker_node2
      - type: nativeedge.relationships.depends_on
        target: worker_node3
      - type: nativeedge.relationships.depends_on
        target: k8s_os_secrets
  
  # ceph:
  #   type: nativeedge.nodes.ServiceComponent
  #   properties:
  #     resource_config:
  #       blueprint:
  #         external_resource: true
  #         id: x_ceph
  #       deployment:
  #         display_name:
  #           concat:
  #             - { get_sys: [deployment, name] }
  #             - "-ceph"
  #         auto_inc_suffix: true
  #         inputs:
  #           kubeconfig: &kubeconfig
  #             get_secret:
  #               get_input: kubeconfig-*************
  #           # kubeconfig: {get_secret: [kubeconfig-*************, kubeconfig]}
  #   relationships:
  #     - type: nativeedge.relationships.depends_on
  #       target: k8s
