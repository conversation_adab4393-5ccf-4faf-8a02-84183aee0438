  # Three endpoints, the endpoint is selected from E<PERSON>,
  # <PERSON><PERSON>S Setting, FW catalog, OS release & version, IP addresses, for nodes
  # network configuration for k8s
  # 

inputs:

  # Endpoint 1 host config
  host1_servicetag:
    type: deployment_id
    display_label: "Endpoint 1 Service Tag "
    description: Service Tag of the ECE.
    required: true
    constraints:
      - labels:
          - cloud:
              edge-cloud
  
  # Endpoint 2 host config
  host2_servicetag:
    type: deployment_id
    display_label: "Endpoint 2 Service Tag"
    description: Service Tag of the ECE.
    required: true
    constraints:
      - labels:
          - cloud:
              edge-cloud

  # Endpoint 3 host config
  host3_servicetag:
    type: deployment_id
    display_label: "Endpoint 3 Service Tag"
    description: Service Tag of the ECE.
    required: true
    constraints:
      - labels:
          - cloud:
              edge-cloud

  # Endpoint 3 host config
  host4_servicetag:
    type: deployment_id
    display_label: "Endpoint 4 Service Tag"
    description: Service Tag of the ECE.
    required: true
    constraints:
      - labels:
          - cloud:
              edge-cloud

  nfs_server_ip: 
    type: string
    display_label: "NFS Server IP"
    description: "NFS Server IP"
    default: "************"
    required: true
  
  nfs_server_path:
    type: string
    display_label: "NFS Server Path"
    description: "NFS Server Path"
    default: "/data"
    required: true


  hardware_config:
    type: string
    allow_update: true
    hidden: false
    default: default
    description: hardware configuration for poweredge servers

  firmware_catelog:
    type: string
    allow_update: true
    hidden: false
    default: default
    description: firmware catelog for poweredge servers

  os_release:
    type: string
    allow_update: true
    hidden: false
    # TODO change to a list to selected
    default: ubuntu
    description: os release 

  os_version:
    type: string
    allow_update: true
    hidden: false
    default: 22.04
    description: os version

  ip_addresses:
    type: list
    allow_update: true
    hidden: false
    description: ip addresses can be used to assign to k8s nodes

  


