#!/bin/bash
## Parameters this script accept:
## $1: The Blueprint version you want to build

BP_VERSION=${1:-1.0.0}
SCRIPT_DIR=$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )
PROVISION_DEB_BUILD_DIR=$SCRIPT_DIR/provision/deb/.build

set -e

function build_provosion_deb(){
  bash $SCRIPT_DIR/provision/build-db.sh $BP_VERSION
}

function build_bp(){
  mkdir -p $SCRIPT_DIR/resources
  cp $PROVISION_DEB_BUILD_DIR/*.deb $SCRIPT_DIR/resources

  zip -r ubuntu-os-bp-$BP_VERSION.zip $SCRIPT_DIR

  echo "Successfully build BP: ubuntu-os-bp-$BP_VERSION.zip"
}

function main(){
  build_provosion_deb
  build_bp
}