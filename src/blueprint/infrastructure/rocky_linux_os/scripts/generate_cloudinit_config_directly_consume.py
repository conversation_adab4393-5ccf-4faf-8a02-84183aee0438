# #!/usr/bin/env python3
# from nativeedge import ctx
# from fabric import Connection
# from nativeedge.state import ctx_parameters as inputs
# from nativeedge.exceptions import NonRecoverableError, RecoverableError
import tempfile
import yaml

inputs = {"image_url": "file:///staging/jammy-server-cloudimg-amd64.img",
    "nfs_server_ip": "************",
    "nfs_server_path": "/data",
    "deployment_config": {
    "hostname": "xxxxxx",
    "fqdn": "xxxxxx",
    "ntp_servers": ["***********"],
    "ethernets": [
        {
            "macaddress": "xx:xx:xx:xx",
            "addresses": ["**********32/24"],
            "gateway4": "**********",
            "nameservers": ["***********"],
        }
    ],
    "users": [
        {"name": "xxx", "groups": ["users", "admin"], "passwd": "plain_text_password"}
    ],
}}

CONFIG_PATH = "/tmp/cloudinit_config.yaml"

def parse_cidr(cidr: str):
    ip, digit = cidr.split("/")
    return ip, digit

def external_resource(image_url: str, nfs_server_ip: str, nfs_server_path: str) -> str:
    return yaml.safe_dump(
        {
            "os_image_url": image_url,
            "nfs_server_ip": nfs_server_ip,
            "nfs_server_path": nfs_server_path,
        },
        sort_keys=False,
    )


def network_data(networks: list) -> dict:
    config = {"network": {"version": 2, "ethernets": {}}}
    for idx, item in enumerate(networks, start=1):
        config["network"]["ethernets"].update(
            {
                f"eth{idx}": {
                    "match": {"macaddress": item["macaddress"]},
                    "addresses": item["addresses"],
                    "gateway4": item["gateway4"],
                    "nameservers": {"addresses": item["nameservers"]},
                }
            }
        )
    return config


def hostname_data(hostname: str, fqdn: str) -> dict:
    config = {"hostname": hostname, "fqdn": fqdn, "create_hostname_file": True}
    return config


def ntp_data(ntp_servers: list) -> dict:
    config = {"ntp": {"servers": []}}
    for item in ntp_servers:
        config["ntp"]["servers"].append(item)
    return config


def user_data(users: list) -> dict:
    config = {"users": []}
    for item in users:
        config["users"].append(
            {
                "name": item["name"],
                "sudo": "ALL=(ALL:ALL) ALL",
                "lock_passwd": False,
                "groups": ", ".join(item["groups"]),
                "plain_text_passwd": item["passwd"],
                # "ssh_authorized_keys": [],
            }
        )
    return config


def cloudinit_config(hostname_data, network_data, ntp_data, user_data):
    header = "#cloud-config\nversion: v1\n"
    return header + yaml.safe_dump(
        {"config": {**hostname_data, **network_data, **ntp_data, **user_data}},
        sort_keys=False,
    )


def generate_yaml_config(*configs):
    config_yaml = "---\n".join(configs)
    print(config_yaml)
    with tempfile.NamedTemporaryFile(
        mode="w",
        prefix="cloudinit_config_",
        encoding="utf8",
        suffix=".yaml",
        delete=False,
    ) as f:
        f.write(config_yaml)
        return f.name


if __name__ == "__main__":
    endpoint = inputs.get("endpoint")
    port = inputs.get("port")
    username = inputs.get("user")
    password = inputs.get("password")
    deployment_config = inputs.get("deployment_config")
    local_config_path = generate_yaml_config(
        external_resource(
            inputs["image_url"], inputs["nfs_server_ip"], inputs["nfs_server_path"]
        ),
        cloudinit_config(
            hostname_data(deployment_config["hostname"], deployment_config["fqdn"]),
            network_data(deployment_config["ethernets"]),
            ntp_data(deployment_config["ntp_servers"]),
            user_data(
                deployment_config["users"],
            ),
        ),
    )
    # with Connection(
    #     host=endpoint,
    #     port=port,
    #     user=username,
    #     connect_kwargs={"key_filename": password, "timeout": 10, "banner_timeout": 60},
    # ) as connection:
    #     connection.put(local_config_path, CONFIG_PATH)

    # ctx.instance.runtime_properties["config_path"] = CONFIG_PATH
    # ctx.instance.runtime_properties["host_ip"] = parse_cidr(inputs["deployment_config"]["ethernets"][0]["addresses"][0])[0]
    # ctx.instance.runtime_properties["username"] = inputs["user"][0]["name"]
    # ctx.instance.runtime_properties["password"] = inputs["user"][0]["passwd"]
    # ctx.instance.runtime_properties["deployment_config"] = deployment_config