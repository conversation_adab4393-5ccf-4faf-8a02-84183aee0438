#!/usr/bin/env python3
from nativeedge import ctx
from fabric import Connection
from nativeedge.state import ctx_parameters as inputs
from nativeedge.exceptions import NonRecoverableError, RecoverableError
import tempfile
import yaml

# inputs = {
#     "ubuntu_os_version": "22.04",
#     "nfs_server_ip": "************",
#     "nfs_server_path": "/data",
#     "deployment_config": {
#         "hostname": "xxxxxx",
#         "top_level_domain": "rackxxx.local",
#         "fqdn": "xxxxxx",
#         "ntp_servers": ["***********"],
#         "ethernets": [
#             {
#                 "macaddress": "xx:xx:xx:xx",
#                 "addresses": ["**********32/24"],
#                 "gateway": "**********",
#                 "nameservers": ["***********"],
#             }
#         ],
#         "users": [
#             {
#                 "name": "xxx",
#                 "groups": ["users", "admin"],
#                 "passwd": "plain_text_password",
#             }
#         ],
#     },
# }

CONFIG_PATH = "/tmp/cloudinit_config.yaml"
OS_IMAGE_URL = "http://************:30080/dsp-os-provision/jammy-server-cloudimg-amd64.img"


def parse_cidr(cidr: str):
    ip, digit = cidr.split("/")
    return ip, digit


def external_resource(
    ubuntu_os_version: str, nfs_server_ip: str, nfs_server_path: str, dns_server: str
) -> str:
    return yaml.safe_dump(
        {
            "ubuntu_os_version": ubuntu_os_version,
            "os_image_url": OS_IMAGE_URL,
            "nfs_server_ip": nfs_server_ip,
            "nfs_server_path": nfs_server_path,
            "dns_server": dns_server,
        },
        sort_keys=False,
    )


def network_data(networks: list) -> dict:
    config = {"network-config": {"version": 2, "ethernets": {}}}
    for idx, item in enumerate(networks, start=1):
        config["network-config"]["ethernets"].update(
            {
                f"eth{idx}": {
                    "match": {"macaddress": item["macaddress"]},
                    "addresses": item["addresses"],
                    "gateway4": item["gateway"],
                    "nameservers": {"addresses": item["nameservers"]},
                }
            }
        )
    return config


def hostname_data(hostname: str, top_level_domain: str) -> dict:
    config = {"hostname": hostname, "fqdn": f"{hostname}.{top_level_domain}", "create_hostname_file": True}
    return config


def ntp_data(ntp_servers: list) -> dict:
    config = {"ntp": {"servers": []}}
    for item in ntp_servers:
        config["ntp"]["servers"].append(item)
    return config


def users(users: list) -> dict:
    config = {"users": []}
    for item in users:
        config["users"].append(
            {
                "name": item["name"],
                "sudo": "ALL=(ALL:ALL) ALL",
                "shell": "/bin/bash",
                "lock_passwd": False,
                "groups": ", ".join(item["groups"]),
                "plain_text_passwd": item["passwd"],
                # "ssh_authorized_keys": [],
            }
        )
    return config


def user_data(**kwargs):
    return {"user-data": {**kwargs}}


def cloudinit_config(network_data, user_data):
    return yaml.safe_dump(
        {"config": {**network_data, **user_data}},
        sort_keys=False,
    )


def generate_yaml_config(*configs):
    config_yaml = "---\n".join(configs)
    print(config_yaml)
    with tempfile.NamedTemporaryFile(
        mode="w",
        prefix="cloudinit_config_",
        encoding="utf8",
        suffix=".yaml",
        delete=False,
    ) as f:
        f.write(config_yaml)
        return f.name


if __name__ == "__main__":
    endpoint = inputs.get("endpoint")
    port = inputs.get("port")
    username = inputs.get("user")
    password = inputs.get("password")
    deployment_config = inputs.get("deployment_config")
    local_config_path = generate_yaml_config(
        external_resource(
            inputs["ubuntu_os_version"],
            inputs["nfs_server_ip"],
            inputs["nfs_server_path"],
            deployment_config["ethernets"][0]["nameservers"][0],
        ),
        cloudinit_config(
            network_data(deployment_config["ethernets"]),
            user_data(
                **hostname_data(
                    deployment_config["hostname"], deployment_config["top_level_domain"]
                ),
                **ntp_data(deployment_config["ntp_servers"]),
                **users(
                    deployment_config["users"],
                ),
                **{
                    "ssh_pwauth": True,
                    "disable_root": False,
                    "package_update": False,
                    "package_upgrade": False
                    # "chpasswd": {
                    #     "list": ["root:mystic", "ubuntu:mystic"],
                    #     "expire": False,
                    # },
                },
            ),
        ),
    )
    with Connection(
        host=endpoint,
        port=port,
        user=username,
        connect_kwargs={"password": password, "timeout": 10, "banner_timeout": 60},
    ) as connection:
        connection.put(local_config_path, CONFIG_PATH)

    ctx.instance.runtime_properties["config_path"] = CONFIG_PATH
    ctx.instance.runtime_properties["host_ip"] = parse_cidr(
        inputs["deployment_config"]["ethernets"][0]["addresses"][0]
    )[0]
    ctx.logger.info("deployment_config: {}".format(deployment_config))
    ctx.logger.info("deployment_config type: {}".format(type(deployment_config)))
    ctx.instance.runtime_properties["username"] = deployment_config["users"][0]["name"]
    ctx.instance.runtime_properties["password"] = deployment_config["users"][0][
        "passwd"
    ]
    ctx.instance.runtime_properties["deployment_config"] = deployment_config
