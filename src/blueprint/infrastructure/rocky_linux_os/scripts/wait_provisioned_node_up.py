#!/usr/bin/env python3
import requests
from nativeedge import ctx
from nativeedge.state import ctx_parameters as inputs
from nativeedge.exceptions import RecoverableError


def main():
    response = requests.get('http://jetstream.hzp.svc.cluster.local:8222/connz', verify=False)
    if response.status_code != 200:
        ctx.logger.error(f'Failed to get nats connections: {response.content}')
        raise RecoverableError("Cannot get nats connections")
    connections = response.json().get('connections')
    conn_name = inputs['conn_name']
    if conn_name.startswith("ece-"):
        conn_name = conn_name[len("ece-"):]
    for conn in connections:
        if conn.get('name') == conn_name:
            ctx.logger.info(f'Found nats connection: {conn_name}')
            return
    ctx.logger.error(f'Not found connection {conn_name} in the {connections}')
    raise RecoverableError(f"Cannot find {conn_name} in the nats connection list")

if __name__ == "__main__":
    main()
