#!/bin/bash
# Copyright (c) 2023 Dell Inc. or its subsidiaries. All Rights Reserved.
#
# This software contains the intellectual property of Dell Inc. or is licensed to Dell Inc. from third parties.
# Use of this software and the intellectual property contained therein is expressly limited to the terms and
# conditions of the License Agreement under which it is provided by or on behalf of Dell Inc. or its subsidiaries.

# Log a message
# usage: log <message>
# usage: log -f <log_file> <message>
# Example: log "Example message"
# Example: log -f "LOG_FILE" "Example message"

# Excpect global variable LOG_FILE_PATH

function log() {
   local timestamp=$(date +'%Y-%m-%d %H:%M:%S.%3N %Z')
   if [[ -n $LOG_FILE_PATH ]];then
     echo "$timestamp $*" | tee -a $LOG_FILE_PATH
   else
     echo "$timestamp $*"
   fi
 }

# Log a message to a file without stdout
# usage: log_2_file <file> <message>
# Example: log_2_file "/var/log/example.log" "Example message"
function log_2_file()
{
    log $*
}
