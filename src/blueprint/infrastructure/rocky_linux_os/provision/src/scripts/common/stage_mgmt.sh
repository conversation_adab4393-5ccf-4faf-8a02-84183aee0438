#!/bin/bash
# Copyright (c) 2023 Dell Inc. or its subsidiaries. All Rights Reserved.
#
# This software contains the intellectual property of Dell Inc. or is licensed to Dell Inc. from third parties.
# Use of this software and the intellectual property contained therein is expressly limited to the terms and
# conditions of the License Agreement under which it is provided by or on behalf of Dell Inc. or its subsidiaries.

UTIL_PKG_NAME=dsp-os-provision

# For stage management
YAML_PATH="/var/run/$UTIL_PKG_NAME"
# YAML for other scripts to read
PROGRESS_YAML="$YAML_PATH/progress.yaml"
# YAML for this script to write (for atomic write)
PROGRESS_YAML_WRITE="$YAML_PATH/.progress.yaml.tmp"

function yaml_atomic_write() {
    mv -f "$PROGRESS_YAML_WRITE" "$PROGRESS_YAML"
}

function init_progress() {
    [ -d "$YAML_PATH" ] || mkdir -p "$YAML_PATH"
    tee "$PROGRESS_YAML_WRITE" >/dev/null <<EOF
totalStages:
totalSteps:
stages:
errors:

EOF
    yaml_atomic_write
}

function set_error_message() {
    local error_message
    error_message="$1"
    yq eval ".errors += [\"$error_message\"]" "$PROGRESS_YAML" > "$PROGRESS_YAML_WRITE"
    yaml_atomic_write
}

function set_total_steps_stages() {
    yq eval ".totalSteps = $steps_num | .totalStages = $stages_num" "$PROGRESS_YAML" > "$PROGRESS_YAML_WRITE"
    yaml_atomic_write
}

function next_stage() {
    local stage_name
    ((++stage_no)) || true
    stage_name=${stages[$stage_no]}
    yq eval ".stages += [{\"stageName\": \"$stage_name\", \"steps\": null}]" "$PROGRESS_YAML" > "$PROGRESS_YAML_WRITE"
    yaml_atomic_write
    if [[ $stage_no -gt 0 ]];then
        stage_just_changed=true
    fi
}

function next_step() {
    local stage_name
    local step_name
    if [[ "$step_no" -ge 0 ]]; then
        if [[ $stage_just_changed == true ]];then
            stage_name=${stages[$stage_no-1]}
            stage_just_changed=false
        else
            stage_name=${stages[$stage_no]}
        fi
        step_name=${steps[$step_no]}
        yq eval ".stages[] |= (select(.stageName == \"$stage_name\") | .steps[] |= (select(.stepName == \"$step_name\") | .endTime = \"$(date --iso-8601=seconds)\"))" "$PROGRESS_YAML" > "$PROGRESS_YAML_WRITE"
        yaml_atomic_write
    fi
    ((++step_no)) || true
    if [[ "$step_no" -lt "$steps_num" ]]; then
        stage_name=${stages[$stage_no]}
        step_name=${steps[$step_no]}
        yq eval ".stages[] |= select(.stageName == \"$stage_name\").steps += [{\"stepName\": \"$step_name\", \"startTime\": \"$(date --iso-8601=seconds)\", \"endTime\": \"\"}]" "$PROGRESS_YAML" > "$PROGRESS_YAML_WRITE"
        yaml_atomic_write
    fi
}
