#!/usr/bin/python3
# Copyright (c) 2023 Dell Inc. or its subsidiaries. All Rights Reserved.
#
# This software contains the intellectual property of Dell Inc. or is licensed to Dell Inc. from third parties.
# Use of this software and the intellectual property contained therein is expressly limited to the terms and
# conditions of the License Agreement under which it is provided by or on behalf of Dell Inc. or its subsidiaries.

# This script will mount the ece-agent.iso to 2nd virtual media on idrac
# Usage Example:
# python3 mount-agent-iso.py --iso-url ************:/data/highland/ece-agent.iso


import argparse
import logging
import logging.handlers
import os
import subprocess
import sys

import requests
import urllib3

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

EjectSuffix = "/redfish/v1/Systems/System.Embedded.1/VirtualMedia/2/Actions/VirtualMedia.EjectMedia"
MountSuffix = "/redfish/v1/Systems/System.Embedded.1/VirtualMedia/2/Actions/VirtualMedia.InsertMedia"

CURRENT_DIR = os.path.dirname(os.path.realpath(__file__))
REQUEST_CONN_TIMEOUT = 10
REQUEST_READ_TIMEOUT = 60 * 60

args = None

LOGGER_NAME = "idrac-mount-iso"

def createLogger(name):
    formatter = "%(asctime)s [%(levelname)s]\t%(pathname)s:%(lineno)d\t%(message)s"
    logger = logging.getLogger(name)
    logger.propagate = False
    handler = logging.StreamHandler(sys.stdout)
    handler.setFormatter(logging.Formatter(formatter))
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)
    return logger

LOG = createLogger(LOGGER_NAME)

def run_command(command_list):
    try:
        lines = subprocess.check_output(command_list, shell=False)
        return_code = 0
        output = lines.decode('utf-8', errors='ignore')
    except subprocess.CalledProcessError as err:
        return_code = err.returncode
        output = err.output.decode('utf-8', errors='ignore')
    return return_code, output


def call_idrac_rest_api(url, method, accpeted_status_code=[200, 202, 204], **kwargs):
    LOG.info("--------------------------------------------------------------\n")
    # GET Call to read data at iDRAC Attribute: ConvergedInfra.1.AppRawData
    output = exec_cmd("ismrfutil --createrfssn --username EMC-Root --priv root --jwt")
    output_array = output.split("|")
    idrac_ip = output_array[0]
    idrac_port = output_array[1]
    jwt_token = output_array[2]
    fullUrl = f'https://{idrac_ip}:{idrac_port}{url}'

    headers = {
        "Authorization": f"Bearer {jwt_token}",
        "Content-Type": "application/json"
    }

    # idrac_ip="***********"
    # idrac_port=443
    # headers = {
    #     "Content-Type": "application/json"
    # }
    # fullUrl = f'https://{idrac_ip}:{idrac_port}{url}'


    if method.upper() == "GET":
        res = httpGet(fullUrl, headers=headers, allowedStatusCodes=accpeted_status_code, **kwargs)
    elif method.upper() == "PATCH":
        res = httpPatch(fullUrl, headers=headers, allowedStatusCodes=accpeted_status_code, **kwargs)
    elif method.upper() == "POST":
        res = httpPost(fullUrl, headers=headers, allowedStatusCodes=accpeted_status_code, **kwargs)
    return res


def httpGet(url, proxy=None, allowedStatusCodes=[], **kwargs):
    return __httpCall(requests.get, url, None, proxy=proxy, allowedStatusCodes=allowedStatusCodes, **kwargs)


def httpPost(url, json, proxy=None, allowedStatusCodes=[], **kwargs):
    return __httpCall(requests.post, url, json, proxy=proxy, allowedStatusCodes=allowedStatusCodes, **kwargs)


def httpDelete(url, json, proxy=None, allowedStatusCodes=[], **kwargs):
    return __httpCall(requests.delete, url, json, proxy=proxy, allowedStatusCodes=allowedStatusCodes, **kwargs)


def httpPut(url, json, proxy=None, allowedStatusCodes=[], **kwargs):
    return __httpCall(requests.put, url, json, proxy=proxy, allowedStatusCodes=allowedStatusCodes, **kwargs)


def httpPatch(url, json, proxy=None, allowedStatusCodes=[], **kwargs):
    return __httpCall(requests.patch, url, json, proxy=proxy, allowedStatusCodes=allowedStatusCodes, **kwargs)


def __httpCall(requestsFunc, url, json, proxy=None, allowedStatusCodes=[], **kwargs):
    fkwargs = {"proxy": proxy, "allowedStatusCodes": allowedStatusCodes}
    if kwargs is not None:
        fkwargs.update(kwargs)
    return __httpSend(requestsFunc, url, json, proxy, allowedStatusCodes, **kwargs)


def __httpSend(requestsFunc, url, json, proxy=None, allowedStatusCodes=[], **kwargs):
    if json is None:
        res = requestsFunc(url, verify=False, timeout=(REQUEST_CONN_TIMEOUT, REQUEST_READ_TIMEOUT), proxies=proxy,
                           **kwargs)
    else:
        if kwargs.get("headers", None) is not None:
            headers = kwargs.pop("headers")
        else:
            headers = {}
        if headers.get("Content-Type", None) is None:
            headers.update({"Content-Type": "application/json"})
        res = requestsFunc(url, json=json, headers=headers, verify=False,
                           timeout=(REQUEST_CONN_TIMEOUT, REQUEST_READ_TIMEOUT), proxies=proxy, **kwargs)
    if len(allowedStatusCodes) > 0:
        if res.status_code not in allowedStatusCodes:
            raise Exception(
                f"Response status code is {res.status_code}, not in the allowed list: {allowedStatusCodes}. Method :{requestsFunc}, url: {url}, json={json}, Response: {res.text}")
    return res


def exec_cmd(cmd):
    try:
        LOG.info('cmd=' + cmd)
        p = subprocess.Popen(cmd,
                             stdout=subprocess.PIPE,
                             stderr=subprocess.STDOUT,
                             shell=True,
                             universal_newlines=True
                             )
        process_output, stderr_data = p.communicate()
        if process_output:
            LOG.info(process_output)
        if stderr_data:
            LOG.error(stderr_data)
        if p.returncode != 0:
            LOG.info(f'error code: {p.returncode}')
            LOG.error(process_output.strip())
            raise Exception('cmd failure: ' + cmd, process_output.strip())
        if isinstance(process_output, str):
            return process_output.strip()

    except Exception as e:
        raise e


def eject_idrac_media():
    eject_payload = {}
    call_idrac_rest_api(EjectSuffix, "POST", json=eject_payload, accpeted_status_code=[200, 202, 204, 500])


def mount_idrac_media():
    mount_payload = {"Image": args.iso_url, "Inserted": True, "WriteProtected": True}
    call_idrac_rest_api(MountSuffix, "POST", json=mount_payload)


def parse_args(sys_args):
    parser = argparse.ArgumentParser()
    parser.add_argument('-url', '--iso-url', metavar="iso_url", required=True,
                        help="iso download url")
    # parser.add_argument('--idrac_ip', required=True,
    #                     help="idrac ip")
    # parser.add_argument('--idrac_user', required=True,
    #                     help="idrac user")
    # parser.add_argument('--idrac_password', required=True,
    #                     help="idrac password")
    args = parser.parse_args(sys_args)

    return args

def main(sys_args):
    global args
    args = parse_args(sys_args)
    LOG.info("input args: %s" % str(args))

    eject_idrac_media()
    mount_idrac_media()


if __name__ == "__main__":
    try:
        main(sys.argv[1:])
    except Exception as e:
        LOG.error("Error: %s" % str(e))
        sys.exit(3)
