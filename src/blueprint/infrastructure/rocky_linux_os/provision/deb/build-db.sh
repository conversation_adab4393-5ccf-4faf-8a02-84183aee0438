#!/bin/sh
set -e

SCRIPT_DIR=$(dirname "$(readlink -f "$0")")
SRC_DIR=$SCRIPT_DIR/../src
WORKSPACE=$SCRIPT_DIR/.build
DEBIAN_DIR=${SCRIPT_DIR}/DEBIAN
BUILD_VERSION=$1
DEBIAN_PACKAGE_NAME="dsp-os-provision-${BUILD_VERSION}.deb"

rm -rf ${WORKSPACE}
mkdir -p ${WORKSPACE}

# Scripts
scripts_target_folder=${WORKSPACE}/usr/share/dsp-os-provision
mkdir -p ${scripts_target_folder}
cp -rf ${SRC_DIR}/* ${scripts_target_folder}

# Debian
cp -rf ${DEBIAN_DIR} ${WORKSPACE}
sed -i "s/Version:.*/Version: ${BUILD_VERSION}/g" ${WORKSPACE}/DEBIAN/control

#mkdir -p ${WORKSPACE}/.build/package/etc/ece/config/
chmod +x ${WORKSPACE}/DEBIAN/postinst
chmod +x ${WORKSPACE}/DEBIAN/postrm

dpkg-deb -b ${WORKSPACE} "${WORKSPACE}/${DEBIAN_PACKAGE_NAME}"
echo "Successfully generated deb: ${WORKSPACE}/${DEBIAN_PACKAGE_NAME}"