# define which components should install on the kubernetes cluster
tosca_definitions_version: nativeedge_1_0


description: >
  The blueprint deploys AI components on the kubernetes cluster.


imports:
  - nativeedge/types/types.yaml
  - inputs/common.yaml

node_templates:

  gpu_operator:
    type: nativeedge.nodes.ServiceComponent
    properties:
      resource_config:
        blueprint:
          external_resource: true
          id: x_gpu_driver_for_k8s
        deployment:
          display_name:
            concat:
              - "GPU__Driver-"
              - { get_sys: [deployment, name] }
          auto_inc_suffix: true
          inputs:
            kubeconfig: &kubeconfig
              get_secret: 
                get_input: kubeconfig_secret_name

  vllm:
    type: nativeedge.nodes.ServiceComponent
    properties:
      resource_config:
        blueprint:
          external_resource: true
          id: x_vllm_for_k8s
        deployment:
          display_name:
            concat:
              - "VLLM-"
              - { get_sys: [deployment, name] }
          auto_inc_suffix: true
          inputs:
            kubeconfig: *kubeconfig
    relationships:
      - type: nativeedge.relationships.depends_on
        target: gpu_operator

  gpu_telemetry:
    type: nativeedge.nodes.ServiceComponent
    properties:
      resource_config:
        blueprint:
          external_resource: true
          id: x_gpu_telemetry_for_k8s
        deployment:
          display_name:
            concat:
              - "GPU_Telemetry-"
              - { get_sys: [deployment, name] }
          auto_inc_suffix: true
          inputs:
            kubeconfig: *kubeconfig
    relationships:
      - type: nativeedge.relationships.depends_on
        target: gpu_operator
  
  kube_dashboard:
    type: nativeedge.nodes.ServiceComponent
    properties:
      resource_config:
        blueprint:
          external_resource: true
          id: x_kube_dashboard_for_k8s
        deployment:
          display_name:
            concat:
              - "Kube_Dashboard-"
              - { get_sys: [deployment, name] }
          auto_inc_suffix: true
          inputs:
            kubeconfig: *kubeconfig

  pytorch:
    type: nativeedge.nodes.ServiceComponent
    properties:
      resource_config:
        blueprint:
          external_resource: true
          id: x_pytorch_for_k8s
        deployment:
          display_name:
            concat:
              - "PyTorch-"
              - { get_sys: [deployment, name] }
          auto_inc_suffix: true

  
  roce_device_plugin:
    type: nativeedge.nodes.ServiceComponent
    properties:
      resource_config:
        blueprint:
          external_resource: true
          id: x_roce_device_plugin_for_k8s
        deployment:
          display_name:
            concat:
              - "RoCE_Device_Plugin-"
              - { get_sys: [deployment, name] }
          auto_inc_suffix: true
    relationships:
      - type: nativeedge.relationships.depends_on
        target: gpu_operator
  
  istio:
    type: nativeedge.nodes.ServiceComponent
    properties:
      resource_config:
        blueprint:
          external_resource: true
          id: x_istio_for_k8s
        deployment:
          display_name:
            concat:
              - "Istio-"
              - { get_sys: [deployment, name] }
          auto_inc_suffix: true
    relationships:
      - type: nativeedge.relationships.depends_on
        target: gpu_operator
  
  kubeflow:
    type: nativeedge.nodes.ServiceComponent
    properties:
      resource_config:
        blueprint:
          external_resource: true
          id: x_kubeflow
        deployment:
          display_name:
            concat:
              - "Kubeflow-"
              - { get_sys: [deployment, name] }
          auto_inc_suffix: true
          inputs:
            kubeconfig: *kubeconfig
    relationships:
      - type: nativeedge.relationships.depends_on
        target: gpu_operator
      - type: nativeedge.relationships.depends_on
        target: ceph
  
  ceph:
    type: nativeedge.nodes.ServiceComponent
    properties:
      resource_config:
        blueprint:
          external_resource: true
          id: x_ceph
        deployment:
          display_name:
            concat:
              - "Ceph-"
              - { get_sys: [deployment, name] }
          auto_inc_suffix: true
          inputs:
            kubeconfig: *kubeconfig
  ldap:
    type: nativeedge.nodes.ServiceComponent
    properties:
      resource_config:
        blueprint:
          external_resource: true
          id: x_ldap_for_k8s
        deployment:
          display_name:
            concat:
              - "LDAP-"
              - { get_sys: [deployment, name] }
          auto_inc_suffix: true
  
  networking_postconfig:
    type: nativeedge.nodes.ServiceComponent
    properties:
      resource_config:
        blueprint:
          external_resource: true
          id: x_networking_postconfig_for_k8s
        deployment:
          display_name:
            concat:
              - "Networking_Postconfig-"
              - { get_sys: [deployment, name] }
          auto_inc_suffix: true
    
  nfs_storage_class:
    type: nativeedge.nodes.ServiceComponent
    properties:
      resource_config:
        blueprint:
          external_resource: true
          id: x_nfs_storage_class_for_k8s
        deployment:
          display_name:
            concat:
              - "NFS_Storage_Class-"
              - { get_sys: [deployment, name] }
          auto_inc_suffix: true
    relationships:
      - type: nativeedge.relationships.depends_on
        target: switch_storage
  
  switch_storage:
    type: nativeedge.nodes.ServiceComponent
    properties:
      resource_config:
        blueprint:
          external_resource: true
          id: x_switch_storage_for_k8s
        deployment:
          display_name:
            concat:
              - "Switch_Storage-"
              - { get_sys: [deployment, name] }
          auto_inc_suffix: true

  