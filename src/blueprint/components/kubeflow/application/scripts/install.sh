#!/usr/bin/env bash

repo=${REPO_URL}
release=${RELEASE_NAME}
kubectl_binary=${KUBECTL_BINARY}
kustomize_binary=${KUSTOMIZE_BINARY}
kubeconfig=${KUBECONFIG}

chmod +x ${kubectl_binary}
workdir=$(pwd)
kubeconfigfile=${workdir}/kubeconfig
echo "${kubeconfig}" >${kubeconfigfile}
kubectl="${kubectl_binary} --kubeconfig ${kubeconfigfile}"

cloned_dir=$(basename "${repo}" .git)
rm -rf "${cloned_dir}"
git clone --depth 1 --single-branch --branch "${release}" "${repo}"
pushd manifests || exit
while ! ${kustomize_binary} build example | ${kubectl} apply -f -; do
    echo "Retrying to apply resources"
    sleep 20
done

sleep 30
ns="kubeflow"
${kubectl} wait --for=condition=ready --timeout=5m pod --all -n ${ns}
popd || exit
