#!/usr/bin/env bash

# deployment_path="/opt/manager/resources/deployments/${TENANT}/${DEPLOYMENT_ID}"
# cd "${deployment_path}" || exit
# kustomize_bin="${deployment_path}/kustomize"

workdir=$(pwd)
kustomize_bin="${workdir}/kustomize"
KUSTOMIZE_SOURCE="https://raw.githubusercontent.com/kubernetes-sigs/kustomize/master/hack/install_kustomize.sh"

if [ -e "${kustomize_bin}" ]; then
    ctx logger info "kustomize already installed"
else
    ctx logger info "Installing kustomize..."
    curl -s "${KUSTOMIZE_SOURCE}" | bash || {
        ctx logger info "Failed to download kustomize"
        exit 1
    }
fi
${kustomize_bin} version
ctx logger info "Installed kustomize_bin"
ctx instance runtime-properties kustomize "${kustomize_bin}"
