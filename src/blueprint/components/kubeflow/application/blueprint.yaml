tosca_definitions_version: cloudify_dsl_1_5

imports:
  - cloudify/types/types.yaml
  - inputs/common.yaml
  - inputs/kubeflow.yaml

node_templates:

  kubectl_binary:  
    type: cloudify.nodes.SoftwareComponent
    interfaces:
      cloudify.interfaces.lifecycle: 
        create:
          implementation: scripts/kubectl_binary.sh
          executor: central_deployment_agent
          inputs:
            KUBECTL_SOURCE:
              get_input: kubectl_binary_url
            TENANT:
              get_sys: [tenant, name]
            DEPLOYMENT_ID:
              get_sys: [deployment, id]
  
  kustomize_binary:  
    type: cloudify.nodes.SoftwareComponent
    interfaces:
      cloudify.interfaces.lifecycle: 
        create:
          implementation: scripts/kustomize_binary.sh
          executor: central_deployment_agent
          inputs:
            TENANT:
              get_sys: [tenant, name]
            DEPLOYMENT_ID:
              get_sys: [deployment, id]

  kubeflow:
    type: cloudify.nodes.ApplicationModule
    relationships:
      - type: cloudify.relationships.depends_on
        target: kubectl_binary
      - type: cloudify.relationships.depends_on
        target: kustomize_binary
    interfaces:
      cloudify.interfaces.lifecycle: 
        
        create:
          implementation: scripts/install.sh
          executor: central_deployment_agent
          inputs:
            REPO_URL: 
              get_input: kubeflow_repo_url
            RELEASE_NAME:
              get_input: kubeflow_release_name
            KUBECTL_BINARY:
              { get_attribute: [kubectl_binary, kubectl] }
            KUSTOMIZE_BINARY:
              { get_attribute: [kustomize_binary, kustomize] }
            KUBECONFIG:
              get_input: kubeconfig
              
          