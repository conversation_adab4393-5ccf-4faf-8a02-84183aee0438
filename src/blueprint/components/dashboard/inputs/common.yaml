inputs:
  environment_type:
    type: string
    hidden: false
    allow_update: false
    display_label: Air-Gapped
    description: |
      Choose "airgapped" - all the binaries will be downloaded from the local
      artifact store, or "internet_connected" for binaries that are available
      on the Internet with a public URL.
    default: internet_connected
    constraints:
    - valid_values:
      - airgapped
      - internet_connected
  
  # the content of kubeconfig
  kubeconfig:
    type: string
    hidden: false
    allow_update: false
    display_label: Kubeconfig
    description: The content of the kubeconfig file.
    default: |
      apiVersion: v1
      clusters:
      - cluster:
          certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUJkekNDQVIyZ0F3SUJBZ0lCQURBS0JnZ3Foa2pPUFFRREFqQWpNU0V3SHdZRFZRUUREQmhyTTNNdGMyVnkKZG1WeUxXTmhRREUyT1RRd05qZzRPVFF3SGhjTk1qTXdPVEEzTURZME1UTTBXaGNOTXpNd09UQTBNRFkwTVRNMApXakFqTVNFd0h3WURWUVFEREJock0zTXRjMlZ5ZG1WeUxXTmhRREUyT1RRd05qZzRPVFF3V1RBVEJnY3Foa2pPClBRSUJCZ2dxaGtqT1BRTUJCd05DQUFTZStkd0Z1aWROc3RyRk14M3U2bk5MUVEvR3ptNmpnNk5FY2VwNGpHd3kKWkMyU1lxOEhkSjQwV005SCtIQ2lERVZDR3BXZzgrWmJocmxBWmlFdHlMVVpvMEl3UURBT0JnTlZIUThCQWY4RQpCQU1DQXFRd0R3WURWUjBUQVFIL0JBVXdBd0VCL3pBZEJnTlZIUTRFRmdRVU4yYmkybTVDVHVCWEdtZUdzWUlRCmxtNHhqVkF3Q2dZSUtvWkl6ajBFQXdJRFNBQXdSUUlnZTFSRy9QYWlOZnZVcU1TdTRJaDBRc096THgxSHgwSEwKcHJKT1NKcHppcVFDSVFDbEd4OW9WUEVhOExNWjRtYUtyK1VHdS82SGs2dkR6YkU3YmFoNTltQnI5UT09Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
          server: https://************:6443
        name: default
      contexts:
      - context:
          cluster: default
          user: default
        name: default
      current-context: default
      kind: Config
      preferences: {}
      users:
      - name: default
        user:
          client-certificate-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUJrakNDQVRlZ0F3SUJBZ0lJZVlrcmh1VXdTaVF3Q2dZSUtvWkl6ajBFQXdJd0l6RWhNQjhHQTFVRUF3d1kKYXpOekxXTnNhV1Z1ZEMxallVQXhOamswTURZNE9EazBNQjRYRFRJek1Ea3dOekEyTkRFek5Gb1hEVEkwTURrdwpOakEyTkRFek5Gb3dNREVYTUJVR0ExVUVDaE1PYzNsemRHVnRPbTFoYzNSbGNuTXhGVEFUQmdOVkJBTVRESE41CmMzUmxiVHBoWkcxcGJqQlpNQk1HQnlxR1NNNDlBZ0VHQ0NxR1NNNDlBd0VIQTBJQUJMamM1R29xSUpLVVRvL2oKMVpMTlFMUzFRbloydmk3ZVdsSlFsRFJUNUJPNWNOeHFYSnFKdjVZeWlwb05DcXZJQ01QenQ1dUs3d3RCcElydgp4WlY3dmx5alNEQkdNQTRHQTFVZER3RUIvd1FFQXdJRm9EQVRCZ05WSFNVRUREQUtCZ2dyQmdFRkJRY0RBakFmCkJnTlZIU01FR0RBV2dCU3NIZTZUeThoZnFpVmFWNHF2M0tWUXlIQmZlREFLQmdncWhrak9QUVFEQWdOSkFEQkcKQWlFQXFESnRIK2VJdGZmYjJYbWwzYzBDMWRra1BBQmF4R2FTeFFqMlpmQXhDck1DSVFDemwwRXo2TU8ycFVHTAp5M3hWRDBOdnVXci8rKzlKaVhRVUM0cnVwcW1rdHc9PQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCi0tLS0tQkVHSU4gQ0VSVElGSUNBVEUtLS0tLQpNSUlCZGpDQ0FSMmdBd0lCQWdJQkFEQUtCZ2dxaGtqT1BRUURBakFqTVNFd0h3WURWUVFEREJock0zTXRZMnhwClpXNTBMV05oUURFMk9UUXdOamc0T1RRd0hoY05Nak13T1RBM01EWTBNVE0wV2hjTk16TXdPVEEwTURZME1UTTAKV2pBak1TRXdId1lEVlFRRERCaHJNM010WTJ4cFpXNTBMV05oUURFMk9UUXdOamc0T1RRd1dUQVRCZ2NxaGtqTwpQUUlCQmdncWhrak9QUU1CQndOQ0FBVFlIZks0azlVVjN1aUExaEg3ZU4rb0tmeGJmSmNCMGlmOWUvM2dnejVQCnhCVEs4VDMyYjMwcGF6aFZMZTY1d0k3Y0FZRWlEck5jVlViK3hHK011Umg1bzBJd1FEQU9CZ05WSFE4QkFmOEUKQkFNQ0FxUXdEd1lEVlIwVEFRSC9CQVV3QXdFQi96QWRCZ05WSFE0RUZnUVVyQjN1azh2SVg2b2xXbGVLcjl5bApVTWh3WDNnd0NnWUlLb1pJemowRUF3SURSd0F3UkFJZ1JNaDRGS3hzT0xRc2hHL1lYVmIrVXd5VUYwSWcwSzBICnRHcm52V1E2b2o0Q0lHSjVKaGhITXUrejZiVXV5aWFGbFhZbWcxMHh6emxOTU8xUnZ1cFVuM0RiCi0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
          client-key-data: ****************************************************************************************************************************************************************************************************************************************************************************************************************
      