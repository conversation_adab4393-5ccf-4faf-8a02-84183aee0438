inputs:

  namespace:
    type: string
    hidden: false
    allow_update: false
    display_label: Namespace
    description: Namespace to be created for Helm release.
    default: prometheus 

  version:
    type: string
    hidden: false
    allow_update: false
    display_label: Chart Version
    description: Version of the Helm chart to be deployed.
    default: v0.76.0

  repo_name:
    type: string
    hidden: true
    allow_update: false
    display_label: Helm Repo Name
    description: Name of the Helm repo.
    default: "prometheus-community"


  repo_url:
    type: string
    hidden: true
    allow_update: false
    display_label: Helm Repo URL
    description: |
      URL to download the Helm repo
    default: "https://prometheus-community.github.io/helm-charts"
  
  chart_name:
    type: string
    hidden: true
    allow_update: false
    display_label: Helm Chart Name
    description: Name of the Helm Chart.
    default: 'kube-prometheus-stack'

  helm_binary_filename:
    type: string
    hidden: true
    allow_update: false
    display_label: Helm Binary Filename
    description: Filename of the Helm binary.
    default: helm-v3.12.1-linux-amd64.tar.gz

  helm_binary_url:
    type: string
    hidden: true
    allow_update: false
    display_label: Helm Binary URL
    description: |
      URLs to download Helm binary with and without internet connection.
    default:
        concat:
        - https://get.helm.sh/
        - get_input: helm_binary_filename
  
  values_path:
    description: >
      Values file path relative inside the blueprint.
    display_label: Values Path
    required: false
    hidden: true
    allow_update: false
    type: string
    default: 'application/chart/values.yaml'
