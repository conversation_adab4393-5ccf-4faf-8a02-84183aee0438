#!/bin/bash

if [ "$ENVIRONMENT_TYPE" = "airgapped" ]; then
  # Install Helm offline
  deployment_path="/opt/manager/resources/deployments/${TENANT}/${DEPLOYMENT_ID}"
  cd "${deployment_path}" || exit
  ctx logger info "Downloading Helm binary from ${HELM_SOURCE}"
  IFS=' ' read -ra CURL_CONFIG_ARRAY <<< "$CURL_CONFIG"
  curl "${CURL_CONFIG_ARRAY[@]}" -O "${HELM_SOURCE}" || {
    ctx logger info "Failed to download ${HELM_SOURCE}"
    exit 1
  }
  ctx instance runtime-properties helm_installation_source "file://${deployment_path}/${HELM_BINARY_FILENAME}"
else
  # Install Helm from the Internet
  ctx instance runtime-properties helm_installation_source "${HELM_SOURCE}"
fi
