imports:
  - plugin:nativeedge-kubernetes-plugin
  - plugin:nativeedge-helm-plugin
  - plugin:nativeedge-fabric-plugin


dsl_definitions:
  client_config: &k8s_client_config
    configuration:
      file_content: { get_input: kubeconfig }


node_templates:

  helm_binary_location:
    type: nativeedge.nodes.SoftwareComponent
    interfaces:
      nativeedge.interfaces.lifecycle:
        create:
          max_retries: 3
          retry_interval: 60
          implementation: application/scripts/helm_binary.sh
          executor: central_deployment_agent
          inputs:
            ENVIRONMENT_TYPE:
              get_input: environment_type
            HELM_SOURCE:
              get_input: helm_binary_url
            TENANT:
              get_sys: [tenant, name]
            DEPLOYMENT_ID:
              get_sys: [deployment, id]

  helm_binary:
    type: nativeedge.nodes.helm.Binary
    properties:
      use_existing_resource:
        false
      installation_source:
        get_attribute: [helm_binary_location, helm_installation_source]
    relationships:
      - type: nativeedge.relationships.depends_on
        target: helm_binary_location

  helm_repo:
    type: nativeedge.nodes.helm.Repo
    properties:
      resource_config:
        name: { get_input: repo_name }
        repo_url: { get_input: repo_url }
    interfaces:
      nativeedge.interfaces.lifecycle:
        start:
          max_retries: 3
          retry_interval: 60
          implementation: helm.ne_helm.tasks.add_repo
          inputs:
            flags: 
              - name: "insecure-skip-tls-verify"
                value: ""
    relationships:
      - type: nativeedge.relationships.helm.run_on_host
        target: helm_binary

  # comment out due to a bug of the helm plugin
  # helm_release:
  #   type: nativeedge.nodes.helm.Release
  #   properties:
  #     client_config: *k8s_client_config
  #     resource_config:
  #       name: prometheus
  #       chart:
  #         concat:
  #           - { get_input: repo_name }
  #           - '/'
  #           - { get_input: chart_name }
  #       flags:
  #         - name: namespace
  #           value: { get_input: namespace }
  #         - name: create-namespace
  #       values_file: { get_input: values_path }
  #   interfaces:
  #     cloudify.interfaces.lifecycle:
  #       start:
  #         implementation: helm.cloudify_helm.tasks.install_release
  #         inputs:
  #           flags:
  #             - name: debug

  #   relationships:
  #     - type: nativeedge.relationships.helm.run_on_host
  #       target: helm_binary
  #     - type: nativeedge.relationships.depends_on
  #       target: helm_repo
