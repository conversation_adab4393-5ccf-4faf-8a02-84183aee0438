tosca_definitions_version: nativeedge_1_0

imports:
  - plugin:nativeedge-kubernetes-plugin
  - plugin:nativeedge-utilities-plugin


node_templates:
  namespace:
    type: nativeedge.nodes.kubernetes.resources.FileDefinedResource
    properties:
      client_config: &kubeconfig_content
        configuration:
          file_content: { get_input: kubeconfig }
      file:
        resource_path: application/manifests/namespace.yaml
        template_variables:
          name: { get_input: name }
  
  deployment:
    type: nativeedge.nodes.kubernetes.resources.FileDefinedResource
    properties:
      client_config: *kubeconfig_content
      file:
        resource_path: application/manifests/deployment.yaml
        template_variables:
          name: { get_input: name }
          model: { get_input: model_name }
          token: { get_input: hf_token }
    relationships:
      - type: nativeedge.relationships.depends_on
        target: namespace
