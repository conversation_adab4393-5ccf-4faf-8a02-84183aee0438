#!/usr/bin/python3

from nativeedge import ctx
from nativeedge.state import ctx_parameters as inputs
import os
import sys
import re
from fabric import Connection
from invoke.watchers import FailingResponder

ctx.logger.info("start install digital human")

def user_sudo(command, user, pwd):
    """
    A function that returns a sudo command and watcher for
    use with `connection.run()` under a pyinvoke context manager.
    """
    prompt = '[sudo] password for {0}:'.format(user)
    password = '{0}'.format(pwd)
    cmd_str = "sudo -S -p '{}' {}".format(prompt, command)
    watcher = FailingResponder(
        pattern=re.escape(prompt),
        response="{}\n".format(password),
        sentinel="Sorry, try again.\n",
    )
    return cmd_str, watcher

def exec_remote_cmd(cmd):
    command, watcher = user_sudo(cmd, username, password)
    result = connection.run(cmd, pty=True, hide=False, watchers=[watcher])
    ctx.logger.info(f'cmd: {command} \n result: \n {result}')
    return result


if __name__ == "__main__":
    global connection, username, password

    # Read parameters from env var
    host = inputs['NODE_IP']
    port = inputs['NODE_PORT']
    username = inputs['NODE_USR']
    password = inputs['NODE_PWD']
    api_key = inputs['API_KEY']
    repo_url = inputs['REPO_URL']

    # create connection with timeout and banner_timeout
    connection=Connection(host=host, port=port ,user=username, 
                          connect_kwargs={'password': password,
                                           "timeout": 10,
                                           'banner_timeout': 60})

    try:
        repo_path = os.path.join('/tmp', os.path.basename(repo_url).replace('.git', ''))
        exec_remote_cmd(f'cd /tmp && git clone {repo_url} || true')

        # prepare env
        cmd = f'''cat << EOF > {repo_path}/deploy/.env
NGC_API_KEY={api_key}
MODEL_DIRECTORY={repo_path}/deploy
LLM_MODEL_NAME=meta/llama3-8b-instruct
NIM_PEFT_SOURCE=/home/<USER>/loras
NIM_PEFT_REFRESH_INTERVAL=3600
LOCAL_PEFT_DIRECTORY=../customize/loras
EOF'''
        exec_remote_cmd(cmd)
        exec_remote_cmd(f"sed -i 's/8081:8081/8085:8081/' {repo_path}/deploy/docker-compose.yaml")
        exec_remote_cmd(f'cd {repo_path}/deploy && USERID=$(id -u) docker compose --profile local-nim --profile milvus up -d --force-recreate')
    except Exception as e:
        ctx.logger.error("cannot execute command: {}".format(str(e)))
        sys.exit(1)
