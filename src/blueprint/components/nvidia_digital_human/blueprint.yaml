tosca_definitions_version: cloudify_dsl_1_5

imports:
  - cloudify/types/types.yaml
  - inputs/common.yaml
  - inputs/digital_human.yaml

node_templates:

  X_NVIDIA_NIM_Agent_Blueprints_Digital_Human:  
    type: cloudify.nodes.SoftwareComponent
    interfaces:
      cloudify.interfaces.lifecycle: 
        create:
          implementation: scripts/digital_human.py
          executor: central_deployment_agent
          inputs:
            NODE_IP:
              get_input: node_ip
            NODE_PORT:
              get_input: node_port
            NODE_USR:
              get_input: node_username
            NODE_PWD:
              get_input: node_password
            API_KEY:
              get_input: api_key
            REPO_URL:
              get_input: repo_url
          