apiVersion: v1
kind: Pod
metadata:
  name: vllm-openai
spec:
  runtimeClassName: nvidia
  containers:
    - name: vllm-openai
      image: vllm/vllm-openai:v0.5.4
      args: ["--model", "/models/Qwen2-0.5B"]
      ports:
        - containerPort: 8000
          hostPort: 8000
      volumeMounts:
        - mountPath: /root/.cache/huggingface
          name: huggingface-cache
        - mountPath: /models
          name: models
      env:
        - name: HUGGING_FACE_HUB_TOKEN
          value: hf_bfCI
  volumes:
    - name: huggingface-cache
      hostPath:
        path: /home/<USER>/.cache/huggingface
    - name: models
      hostPath:
        path: /home/<USER>/vllm/vllm-wsl/models
  hostIPC: true
  hostNetwork: true
