[build-system]
requires = ["setuptools"]
build-backend = "setuptools.build_meta"

[project]
name = "smolagents"
version = "1.15.0.dev0"
description = "🤗 smolagents: a barebones library for agents. Agents write python code to call tools or orchestrate other agents."
authors = [
  { name="Aymeric Roucher", email="<EMAIL>" },
]
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
  "huggingface-hub>=0.28.0",
  "requests>=2.32.3",
  "rich>=13.9.4",
  "jinja2>=3.1.4",
  "pillow>=11.0.0",
  "markdownify>=0.14.1",
  "duckduckgo-search>=6.3.7",
  "python-dotenv"
]

[project.optional-dependencies]
bedrock = [
  "boto3>=1.36.18"
]
torch = [
  "torch",
  "torchvision",
  "numpy>=1.21.2",
]
audio = [
  "soundfile",
  "smolagents[torch]",
]
docker = [
  "docker>=7.1.0",
  "websocket-client",
]
e2b = [
  "e2b-code-interpreter>=1.0.3",
  "python-dotenv>=1.0.1",
]
gradio = [
  "gradio>=5.13.2",
]
litellm = [
  "litellm>=1.60.2",
]
mcp = [
  "mcpadapt>=0.0.19",  # Security fix
  "mcp",
]
mlx-lm = [
  "mlx-lm"
]
openai = [
  "openai>=1.58.1"
]
telemetry = [
  "arize-phoenix",
  "opentelemetry-sdk", 
  "opentelemetry-exporter-otlp",
  "openinference-instrumentation-smolagents>=0.1.4"
]
transformers = [
  "accelerate",
  "transformers>=4.0.0",
  "smolagents[torch]",
]
vision = [
  "helium",
  "selenium",
]
vllm = [
  "vllm",
  "torch"
]
all = [
  "smolagents[audio,docker,e2b,gradio,litellm,mcp,mlx-lm,openai,telemetry,transformers,vision,bedrock]",
]
quality = [
  "ruff>=0.9.0",
]
test = [
  "ipython>=8.31.0", # for interactive environment tests
  "pandas>=2.2.3",
  "pytest>=8.1.0",
  "pytest-datadir",
  "python-dotenv>=1.0.1", # For test_all_docs
  "smolagents[all]",
  "rank-bm25", # For test_all_docs
  "Wikipedia-API>=0.8.1",
]
dev = [
  "smolagents[quality,test]",
  "sqlalchemy", # for ./examples
]

[tool.pytest.ini_options]
# Add the specified `OPTS` to the set of command line arguments as if they had been specified by the user.
addopts = "-sv --durations=0"

[tool.ruff]
line-length = 119
lint.ignore = [
  "F403", # undefined-local-with-import-star
  "E501", # line-too-long
]
lint.select = ["E", "F", "I", "W"]

[tool.ruff.lint.per-file-ignores]
"examples/*" = [
  "E402", # module-import-not-at-top-of-file
]

[tool.ruff.lint.isort]
known-first-party = ["smolagents"]
lines-after-imports = 2

[tool.setuptools.package-data]
"smolagents.prompts" = ["*.yaml"]

[project.scripts]
smolagent = "smolagents.cli:main"
webagent = "smolagents.vision_web_browser:main"
