import os
import asyncio

from autogen_agentchat.agents import Assistant<PERSON><PERSON>, UserProxyAgent
from autogen_agentchat.base import TaskR<PERSON>ult
from autogen_agentchat.conditions import ExternalTermination, TextMentionTermination
from autogen_agentchat.teams import RoundRobinGroupChat
from autogen_agentchat.ui import <PERSON>sole
from autogen_ext.models.openai import OpenA<PERSON><PERSON>CompletionClient
from prompt import PYTHON_DEVELOPER_PROMPT
from task import TASK1, TASK2
from tools.file_op_tools import create_file, read_file, update_file, delete_file
from tools.git_op_tools import git_clone, git_commit


os.environ["http_proxy"] = "socks5://***********:3389"
# os.environ["https_proxy"] = "http://your_proxy_address:port"

# local model
model = "DeepSeek-R1-Distill-Llama-70B"
# model = "Qwen2.5-Coder-32B-Instruct"
api_key = "OPENAI_API_KEY"
base_url = "http://***********:4000/v1"

# Create an OpenAI model client.
model_client = OpenAIChatCompletionClient(
    model=model,
    api_key=api_key,
    base_url=base_url,
)

tools = [create_file, read_file, update_file, git_clone, git_commit, delete_file]

# Create the primary agent.
develper_agent = AssistantAgent(
    "developer",
    model_client = model_client,
    tools=tools, 
    system_message = PYTHON_DEVELOPER_PROMPT,
)

# Create the critic agent.
user_proxy = UserProxyAgent("user_proxy", input_func=input)

# Define a termination condition that stops the task if the critic approves.
text_termination = TextMentionTermination("TERMINATE")

# Create a team with the primary and critic agents.
team = RoundRobinGroupChat([develper_agent, user_proxy], termination_condition=text_termination)

result = team.run_stream(task=TASK2)
asyncio.run(Console(result))
