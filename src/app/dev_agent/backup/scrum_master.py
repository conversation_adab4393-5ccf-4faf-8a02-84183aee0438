from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_agentchat.agents import AssistantAgent, UserProxyAgent
from autogen_agentchat.messages import TextMessage
from autogen_core import CancellationToken
from autogen_agentchat.ui import Console
from autogen_agentchat.conditions import TextMentionTermination
from autogen_agentchat.teams import RoundRobinGroupChat
import asyncio
from github import Github
import os

os.environ["http_proxy"] = "socks5://***********:3389"
# os.environ["https_proxy"] = "http://your_proxy_address:port"


# Create an OpenAI model client.
model_client = OpenAIChatCompletionClient(
    model="gpt-4o-2024-08-06",
    api_key="OPENAI_API_KEY",
    base_url="http://***************:30122/v1",
    # proxy="socks5://***********:3389",
)

async def get_github_issues(repo_url: str, label: str = "task") -> list:
    """Fetch GitHub issues with specific label."""
    g = Github(os.getenv("GITHUB_TOKEN"))
    repo = g.get_repo(repo_url)
    issues = repo.get_issues(labels=[label], state="open")
    return [{"title": issue.title, "body": issue.body, "number": issue.number} 
            for issue in issues]

# Create the Scrum Master agent
scrum_master = AssistantAgent(
    name="ScrumMaster",
    system_message="""You are an experienced Scrum Master and requirements analyst.
    Your responsibilities:
    1. Review task descriptions from GitHub issues
    2. Verify if tasks are clear and implementable
    3. Identify missing information or ambiguities
    4. Refine and structure the task description using the following format:
        - User Story
        - Acceptance Criteria
        - Technical Requirements
        - Dependencies (if any)
        - Definition of Done
    
    Always ensure tasks are SMART:
    - Specific
    - Measurable
    - Achievable
    - Relevant
    - Time-bound
    
    If a task is unclear or missing critical information, provide specific questions that need to be answered.
    """,
    model_client=model_client
)

user_proxy = UserProxyAgent("user_proxy", input_func=input)
termination = TextMentionTermination("Task Analysis Complete")
team = RoundRobinGroupChat([scrum_master, user_proxy], termination_condition=termination)

async def analyze_task(repo_url: str) -> None:
    """Main function to analyze GitHub issues and refine tasks."""
    issues = await get_github_issues(repo_url)
    
    for issue in issues:
        task_message = f"""
        Please analyze and refine the following task:
        
        Issue #{issue['number']}: {issue['title']}
        
        Description:
        {issue['body']}
        
        Please provide a refined task description that is clear and implementable.
        """
        
        stream = team.run_stream(task=task_message)
        await Console(stream)

if __name__ == "__main__":
    repo_url = "owner/repository-name"  # Replace with your repository
    asyncio.run(analyze_task(repo_url))

