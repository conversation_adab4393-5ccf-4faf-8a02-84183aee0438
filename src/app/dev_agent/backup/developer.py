from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_agentchat.agents import AssistantAgent, UserProxyAgent
from autogen_agentchat.messages import TextMessage
from autogen_core import CancellationToken
from autogen_agentchat.ui import Console
import asyncio
from autogen_agentchat.conditions import TextMentionTermination
from autogen_agentchat.teams import RoundRobinGroupChat

from tempfile import TemporaryDirectory

from langchain_community.agent_toolkits import FileManagementToolkit
from autogen_ext.tools.langchain import Lang<PERSON>hainTool<PERSON>dapter
from langchain_community.tools import ShellTool

import logging
import os
from autogen_core.tools import FunctionTool
from typing_extensions import Annotated

from autogen_core import TRACE_LOGGER_NAME

logging.basicConfig(level=logging.WARNING)
logger = logging.getLogger(TRACE_LOGGER_NAME)
logger.setLevel(logging.DEBUG)


os.environ["http_proxy"] = "socks5://***********:3389"
# os.environ["https_proxy"] = "http://your_proxy_address:port"


# Create an OpenAI model client.
model_client = OpenAIChatCompletionClient(
    model="gpt-4o-2024-08-06",
    api_key="OPENAI_API_KEY",
    base_url="http://***************:30122/v1",
    # proxy="socks5://***********:3389",
)

# working_directory = TemporaryDirectory()
# toolkit = FileManagementToolkit(
#     root_dir='./workspace'
# )  # If you don't provide a root_dir, operations will default to the current working directory
# tools = toolkit.get_tools()
# autogen_tools = []

# shell_tool = ShellTool()

# for tool in tools:
#     autogen_tools.append(LangChainToolAdapter(tool))
# autogen_tools.append(LangChainToolAdapter(shell_tool))

async def create_directory_in_workspace(directory_name: str) -> str:
    workspace_path = os.path.join(".", "workspace", directory_name)
    os.makedirs(workspace_path, exist_ok=True)
    return f"Created directory {workspace_path}"

async def write_file_to_workspace(file_path: str, file_content: str) -> str:
    workspace_path = os.path.join(".", "workspace", file_path)
    with open(workspace_path, "w") as file:
        file.write(file_content)
    return f"Created file {workspace_path}"

async def clone_repository(repository_url: str) -> str:
    workspace_path = os.path.join(".", "workspace")
    os.system(f"git clone {repository_url} {workspace_path}")
    return f"Cloned repository {repository_url} to {workspace_path}"

async def run_command(command: str) -> str:
    workspace_path = os.path.join(".", "workspace")
    os.system(f"cd {workspace_path} && {command}")
    return f"Ran command {command} in {workspace_path}"

async def commit_and_push_changes(message: str) -> str: 
    workspace_path = os.path.join(".", "workspace")
    os.system(f"cd {workspace_path} && git add . && git commit -m {message} && git push")
    return f"Committed and pushed changes to {workspace_path}"

create_directory_in_workspace_tool = FunctionTool(create_directory_in_workspace, description="Create a directory in the workspace.")
write_file_to_workspace_tool = FunctionTool(write_file_to_workspace, description="Write a file to the workspace.")
clone_repository_tool = FunctionTool(clone_repository, description="Clone a repository to the workspace.")
run_command_tool = FunctionTool(run_command, description="Run a command in the workspace.")
commit_and_push_changes_tool = FunctionTool(commit_and_push_changes, description="Commit and push changes to the repository.")

# Create the developer agent
developer_agent = AssistantAgent(
    name="Developer",
    system_message="""You are an expert software developer. 
    For any development task:
    1. Analyze requirements and create a plan
    2. Design the appropriate folder structure
    3. Write the necessary code files
    4. Provide clear documentation
    
    Always write code that follows best practices and is well-documented.
    When creating files, specify the full path including folders.

    Use markdown code blocks with the file path to specify where code should be saved.
    If you can use tools to create files and folders, use them and return the result.
    If you need to clone a repository, use the clone_repository tool.
    If you need to run a command, use the run_command tool.
    If you need to commit and push changes, use the commit_and_push_changes tool.
    Do it step by step. One step at a time. Get confirmation from the user before moving on to the next step.
    """,
    #     Use markdown code blocks with the file path to specify where code should be saved.
    model_client=model_client,
    tools=[create_directory_in_workspace_tool, 
           write_file_to_workspace_tool, 
           clone_repository_tool, 
           run_command_tool, 
           commit_and_push_changes_tool]
    # reflect_on_tool_use=True
)

user_proxy = UserProxyAgent("user_proxy", input_func=input) 
termination = TextMentionTermination("Done")
team = RoundRobinGroupChat([developer_agent, user_proxy], termination_condition=termination)



async def assistant_run(task_description) -> None:
    stream = team.run_stream(task=task_description)
    await Console(stream)
    # await Console(
    #     developer_agent.on_messages_stream(
    #         [TextMessage(content=task_description, source="user")],
    #         cancellation_token=CancellationToken(),
    #     )
    # )

# Create the user proxy agent that can execute code
# user_proxy = UserProxyAgent(
#     name="CodeExecutor",
#     system_message="I execute code and create files/folders as needed. I provide feedback on execution results.",
#     code_execution_config=code_execution_config,
#     human_input_mode="NEVER"
# )


# Example usage
if __name__ == "__main__":
    task = """
    Create a simple Flask web application that:
    - Has a homepage showing "Hello World"
    - Has an API endpoint that returns current time
    - Uses proper project structure with templates
    - Create Dockerfile for the application
    - Create makefile for the application
    - Create README.md file for the application
    """
    asyncio.run(assistant_run(task))

