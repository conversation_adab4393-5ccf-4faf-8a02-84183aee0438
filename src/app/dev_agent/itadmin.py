import os
import async<PERSON>

from autogen_agentchat.agents import <PERSON><PERSON><PERSON>, UserProxyAgent
from autogen_agentchat.base import TaskResult
from autogen_agentchat.conditions import ExternalTermination, TextMentionTermination
from autogen_agentchat.teams import RoundRobinGroupChat
from autogen_agentchat.ui import Console
from autogen_ext.models.openai import OpenAIChatCompletionClient
from prompt import IT_ADMIN_PROMPT
from task import TASK1
from tools.file_op_tools import create_file
from tools.git_op_tools import git_clone, git_commit
from tools.terminal_op_tools import run_command


os.environ["http_proxy"] = "socks5://***********:3389"
# os.environ["https_proxy"] = "http://your_proxy_address:port"

# local model
model = "DeepSeek-R1-Distill-Llama-70B"
api_key = "OPENAI_API_KEY"
base_url = "http://***********:4000/v1"

# Create an OpenAI model client.
model_client = OpenAIChatCompletionClient(
    model=model,
    api_key=api_key,
    base_url=base_url,
)

# tools = [create_file, git_clone, git_commit]
tools = [run_command]

# Create the primary agent.
itadmin_agent = AssistantAgent(
    "developer",
    model_client = model_client,
    tools=tools, 
    system_message = IT_ADMIN_PROMPT,
)

# Create the critic agent.
user_proxy = UserProxyAgent("user_proxy", input_func=input)

# Define a termination condition that stops the task if the critic approves.
text_termination = TextMentionTermination("TERMINATE")

# Create a team with the primary and critic agents.
team = RoundRobinGroupChat([develper_agent, user_proxy], termination_condition=text_termination)

result = team.run_stream(task=TASK1)
asyncio.run(Console(result))
