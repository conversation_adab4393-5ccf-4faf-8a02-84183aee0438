playgroud - https://eos2git.cec.lab.emc.com/wux13/POC6
using this playgroud to create a python application to steup a k8s cluster from baremetal


Use case:
- Human: design the application, create tasks (git issue in future). 
- Human: setup the local coding/runtime environment for the agent.
- Agent: get the task, create the code, test it.
- Agent: create PR to git after it is ready
- Human: review the code, comments
- Agent: get comments, resolve the comments, commit
- Human: Approve PR

