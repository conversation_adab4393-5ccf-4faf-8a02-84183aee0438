import os
from typing import Dict
from pathlib import Path

WORK_DIR = Path("./workspace").resolve()
WORK_DIR.mkdir(exist_ok=True)

def create_file(file_path: str, content: str = "", overwrite: bool = False) -> Dict:
    """Create or update a file with content"""
    try:
        full_path = os.path.join(WORK_DIR, file_path)
        with open(full_path, "w" if overwrite else "x") as f:
            f.write(content)
        return {"success": True, "path": str(full_path)}
    except Exception as e:
        return {"success": False, "error": str(e)}

def update_file(file_path: str, new_content: str, overwrite: bool = True) -> Dict:
    """
    Update the content of an existing file.
    
    Args:
        file_path (str): Path to the file to update.
        new_content (str): New content to write into the file.
        overwrite (bool): Whether to overwrite the existing content. Defaults to True.
        
    Returns:
        Dict: A dictionary containing success status and details.
    """
    try:
        full_path = os.path.join(WORK_DIR, file_path)
        if not os.path.exists(full_path):
            return {"success": False, "error": "File not found"}
            
        with open(full_path, "w") as f:
            f.write(new_content)
        return {"success": True, "path": str(full_path)}
    except Exception as e:
        return {"success": False, "error": str(e)}

def read_file(file_path: str) -> Dict:
    """
    Read the content of an existing file.
    
    Args:
        file_path (str): Path to the file to read.
        
    Returns:
        Dict: A dictionary containing success status, file content, and details.
    """
    try:
        full_path = os.path.join(WORK_DIR, file_path)
        if not os.path.exists(full_path):
            return {"success": False, "error": "File not found"}
            
        with open(full_path, "r") as f:
            content = f.read()
        return {"success": True, "content": content, "path": str(full_path)}
    except Exception as e:
        return {"success": False, "error": str(e)}

def delete_file(file_path: str) -> Dict:
    """
    Delete an existing file.
    
    Args:
        file_path (str): Path to the file to delete.
        
    Returns:
        Dict: A dictionary containing success status and details.
    """
    try:
        full_path = os.path.join(WORK_DIR, file_path)
        if not os.path.exists(full_path):
            return {"success": False, "error": "File not found"}
            
        os.remove(full_path)
        return {"success": True, "path": str(full_path)}
    except Exception as e:
        return {"success": False, "error": str(e)}
