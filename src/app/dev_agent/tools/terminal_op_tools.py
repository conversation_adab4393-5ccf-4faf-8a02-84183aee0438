import subprocess
from typing import Dict

def run_command(command: str) -> Dict:
    """Run a shell command and return the result, result example - {'success': True, 'output': 'Hello, World!\n', 'error': ''} """
    try:
        result = subprocess.run(command, shell=True, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        return {
            "success": True,
            "output": result.stdout,
            "error": result.stderr
        }
    except subprocess.CalledProcessError as e:
        return {
            "success": False,
            "output": e.stdout,
            "error": e.stderr
        }

# # 示例用法
# command_result = run_command("echo Hello, World!")
# print(command_result)