from pathlib import Path
import subprocess
import requests
import re
from typing import Dict

WORK_DIR = Path("./workspace").resolve()
WORK_DIR.mkdir(exist_ok=True)

def git_clone(repo_url: str, branch: str = "main") -> Dict:
    """Clone a repository of a specific branch into a directory named as the repo name under WORKDIR. Returns command output."""
    try:
        # Extract repo name from URL
        repo_name = Path(repo_url).name.replace('.git', '')
        clone_dir = WORK_DIR / repo_name
        
        # Clone into the specific directory
        result = subprocess.run(
            ["git", "clone", "-b", branch, repo_url, clone_dir],
            capture_output=True,
            text=True,
            cwd=WORK_DIR
        )
        return {
            "success": result.returncode == 0,
            "output": result.stdout,
            "error": result.stderr
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

def git_commit(message: str, files: str = ".", remote: str = "origin", branch: str = "main", github_token: str = "") -> Dict:
    """Add, commit, and push changes to a specific remote and branch. Returns command output."""
    try:
        # Check if the branch exists
        branch_exists_result = subprocess.run(
            ["git", "show-ref", "--verify", f"refs/heads/{branch}"],
            capture_output=True,
            text=True,
            cwd=WORK_DIR
        )
        if branch_exists_result.returncode != 0:
            # Branch does not exist, create it
            create_branch_result = subprocess.run(
                ["git", "checkout", "-b", branch],
                capture_output=True,
                text=True,
                cwd=WORK_DIR
            )
            if create_branch_result.returncode != 0:
                return {"success": False, "error": create_branch_result.stderr}
        else:
            # Branch exists, check it out
            checkout_result = subprocess.run(
                ["git", "checkout", branch],
                capture_output=True,
                text=True,
                cwd=WORK_DIR
            )
            if checkout_result.returncode != 0:
                return {"success": False, "error": checkout_result.stderr}
            
        # First stage changes
        add_result = subprocess.run(
            ["git", "add", files],
            capture_output=True,
            text=True,
            cwd=WORK_DIR
        )
        if add_result.returncode != 0:
            return {"success": False, "error": add_result.stderr}

        # Then commit
        commit_result = subprocess.run(
            ["git", "commit", "-m", message],
            capture_output=True,
            text=True,
            cwd=WORK_DIR
        )
        if commit_result.returncode != 0:
            return {"success": False, "error": commit_result.stderr}

        # Use remote URL directly if it's a URL
        remote_url = remote
        if not remote_url.startswith(("http://", "https://", "git@")):
            # Fallback to getting URL from git config
            remote_url_result = subprocess.run(
                ["git", "config", "--get", f"remote.{remote}.url"],
                capture_output=True,
                text=True,
                cwd=WORK_DIR
            )
            
            if remote_url_result.returncode != 0:
                return {"success": False, "error": f"Remote {remote} not configured"}
            
            remote_url = remote_url_result.stdout.strip()

        # Configure credentials if using HTTPS
        if remote_url.startswith("https://") and github_token:
            subprocess.run(
                ["git", "config", "credential.helper", "store"],
                cwd=WORK_DIR
            )
            with open(WORK_DIR / ".git-credentials", "a") as f:
                f.write(f"https://{github_token}:<EMAIL>\n")

        # Execute push
        push_result = subprocess.run(
            ["git", "push", "-u", remote, branch],
            capture_output=True,
            text=True,
            cwd=WORK_DIR
        )
        
        return {
            "success": push_result.returncode == 0,
            "output": push_result.stdout,
            "error": push_result.stderr
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

# TODO not tested
def create_pr(title: str, body: str, base: str, head: str, github_token: str) -> Dict:
    """Create GitHub pull request using API. Returns PR URL."""
    try:
        # Get remote URL
        remote_url_result = subprocess.run(
            ["git", "config", "--get", "remote.origin.url"],
            capture_output=True,
            text=True,
            cwd=WORK_DIR
        )
        
        if remote_url_result.returncode != 0:
            return {"success": False, "error": "Git remote origin not configured"}

        remote_url = remote_url_result.stdout.strip()
        
        # Extract owner/repo from both SSH and HTTPS URLs
        match = re.search(r"git@[^:/](.+?)/(.+?)(\.git)?$", remote_url)
        if not match:
            return {"success": False, "error": "Could not parse GitHub repository from remote URL"}
            
        owner, repo = match.groups()[:2]

        # Create PR via GitHub API
        headers = {
            "Authorization": f"Bearer {github_token}",
            "Accept": "application/vnd.github+json"
        }
        data = {
            "title": title,
            "body": body,
            "head": head,
            "base": base
        }
        
        response = requests.post(
            f"https://api.github.com/repos/{owner}/{repo}/pulls",
            headers=headers,
            json=data
        )
        
        if response.status_code != 201:
            return {"success": False, "error": f"GitHub API error: {response.json().get('message', 'Unknown error')}"}

        return {
            "success": True,
            "output": response.json()["html_url"]
        }
    except Exception as e:
        return {"success": False, "error": str(e)}
