TASK1 = """
Pull code from <NAME_EMAIL>:wux13/x.git. Branch name is main.
Write python code to print hello world to console.
Save the code a local file hello_world.py after user accept the code.
Commit and push code <NAME_EMAIL>:yangq5/x.git. Branch name is test_hello.
"""

TASK2 = """
Pull code from <NAME_EMAIL>:vxrail/ch-desired-data-service.git. Branch name is master.
Modify file desired_data_service/consts.py, add mapping 8.0.400 to 8.0.320 to dictionary VERSION_MAP, append item 8.0.400 to VERSION_ALL_SUPPORTED list.
Save the modification to local file desired_data_service/consts.py after user accept.
Commit and push code <NAME_EMAIL>:yangq5/ch-desired-data-service.git. Branch name is add_8.0.400.
"""