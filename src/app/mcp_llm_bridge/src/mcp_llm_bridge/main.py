# src/mcp_llm_bridge/main.py
import os
import sys

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', 'src')))

import asyncio
from dotenv import load_dotenv
from mcp import StdioServerParameters
from mcp_llm_bridge.config import BridgeConfig, LLMConfig, MCPServerConfig
from mcp_llm_bridge.bridge import BridgeManager
from mcp_llm_bridge.system_prompt import system_prompt
import colorlog
import logging

handler = colorlog.StreamHandler()
handler.setFormatter(colorlog.ColoredFormatter(
    "%(log_color)s%(levelname)s%(reset)s:     %(cyan)s%(name)s%(reset)s - %(message)s",
    datefmt=None,
    reset=True,
    log_colors={
        'DEBUG': 'cyan',
        'INFO': 'green',
        'WARNING': 'yellow',
        'ERROR': 'red',
        'CRITICAL': 'red,bg_white',
    },
    secondary_log_colors={},
    style='%'
))

logger = colorlog.getLogger(__name__)
logger.addHandler(handler)
logger.setLevel(logging.INFO)

os.environ["http_proxy"] = "socks5://100.80.20.5:3389"

async def main():
    # Load environment variables
    load_dotenv()

    # Get the project root directory (where test.db is located)
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
    
    # Configure bridge
    config = BridgeConfig(
        mcp_server_configs=[
            # MCPServerConfig(
            #     name="time",
            #     params=StdioServerParameters(
            #         command="python",
            #         args=["-m", "mcp_server_time", "--local-timezone", "Asia/Shanghai"],
            #         env=None)
            # ),
            MCPServerConfig(
                name="time",
                params=StdioServerParameters(
                    command="docker",
                    args=["run", "-i", "--rm", "mcp/time:yq"],
                    env=None)
            ),
            MCPServerConfig(
                name="fetch",
                params=StdioServerParameters(
                    command="python",
                    args=["-m", "mcp_server_fetch"],
                    env=None
                )
            )
        ],
        # llm_config=LLMConfig(
        #     api_key=os.getenv("OPENAI_API_KEY"),
        #     model=os.getenv("OPENAI_MODEL", "gpt-4o"),
        #     base_url=None
        # ),
        llm_config=LLMConfig(
            api_key="OPENAI_API_KEY",  # Can be any string for local testing
            # model="Qwen2.5-Coder-32B-Instruct",
            model="DeepSeek-R1-Distill-Llama-70B",
            base_url="http://100.80.20.5:4000/v1",  # Point to your local model's endpoint
        ),
        system_prompt=system_prompt
    )
    
    logger.info(f"Starting bridge with model: {config.llm_config.model}")
    
    # Use bridge with context manager
    async with BridgeManager(config) as bridge:
        while True:
            try:
                user_input = input("\nEnter your prompt (or 'quit' to exit): ")
                if user_input.lower() in ['quit', 'exit', 'q']:
                    break
                    
                response = await bridge.process_message(user_input)
                print(f"\nResponse: {response}")
                
            except KeyboardInterrupt:
                logger.info("\nExiting...")
                break
            except Exception as e:
                logger.error(f"\nError occurred: {e}")

if __name__ == "__main__":
    asyncio.run(main())