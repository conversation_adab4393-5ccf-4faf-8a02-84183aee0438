from typing import List, Dict, Optional

class AssistantMessageContent:
    def __init__(self, type: str, content: Optional[str] = None, name: Optional[str] = None, params: Optional[Dict[str, str]] = None, partial: bool = False):
        self.type = type
        self.content = content
        self.name = name
        self.params = params if params is not None else {}
        self.partial = partial

class TextContent(AssistantMessageContent):
    def __init__(self, content: str, partial: bool = False):
        super().__init__(type="text", content=content, partial=partial)

class ToolUse(AssistantMessageContent):
    def __init__(self, name: str, params: Dict[str, str], partial: bool = False):
        super().__init__(type="tool_use", name=name, params=params, partial=partial)

ToolParamName = str
ToolUseName = str

toolParamNames = [
	"command",
	"requires_approval",
	"path",
    "server_name",
	"tool_name",
    "arguments",
]
toolUseNames = [
	"execute_command",
	"read_file",
    "use_mcp_tool",
]

def parseAssistantMessage(assistantMessage: str) -> List[AssistantMessageContent]:
    contentBlocks: List[AssistantMessageContent] = []
    currentTextContent: Optional[TextContent] = None
    currentTextContentStartIndex = 0
    currentToolUse: Optional[ToolUse] = None
    currentToolUseStartIndex = 0
    currentParamName: Optional[ToolParamName] = None
    currentParamValueStartIndex = 0
    accumulator = ""

    for i in range(len(assistantMessage)):
        char = assistantMessage[i]
        accumulator += char

        # there should not be a param without a tool use
        if currentToolUse and currentParamName:
            currentParamValue = accumulator[currentParamValueStartIndex:]
            paramClosingTag = f"</{currentParamName}>"
            if currentParamValue.endswith(paramClosingTag):
                # end of param value
                currentToolUse.params[currentParamName] = currentParamValue[:-len(paramClosingTag)].strip()
                currentParamName = None
                continue
            else:
                # partial param value is accumulating
                continue

        # no currentParamName

        if currentToolUse:
            currentToolValue = accumulator[currentToolUseStartIndex:]
            toolUseClosingTag = f"</{currentToolUse.name}>"
            if currentToolValue.endswith(toolUseClosingTag):
                # end of a tool use
                currentToolUse.partial = False
                contentBlocks.append(currentToolUse)
                currentToolUse = None
                continue
            else:
                possibleParamOpeningTags = [f"<{name}>" for name in toolParamNames]
                for paramOpeningTag in possibleParamOpeningTags:
                    if accumulator.endswith(paramOpeningTag):
                        # start of a new parameter
                        currentParamName = paramOpeningTag[1:-1]
                        currentParamValueStartIndex = len(accumulator)
                        break

                # there's no current param, and not starting a new param

                # special case for write_to_file where file contents could contain the closing tag, in which case the param would have closed and we end up with the rest of the file contents here. To work around this, we get the string between the starting content tag and the LAST content tag.
                contentParamName: ToolParamName = "content"
                if currentToolUse.name == "write_to_file" and accumulator.endswith(f"</{contentParamName}>"):
                    toolContent = accumulator[currentToolUseStartIndex:]
                    contentStartTag = f"<{contentParamName}>"
                    contentEndTag = f"</{contentParamName}>"
                    contentStartIndex = toolContent.find(contentStartTag) + len(contentStartTag)
                    contentEndIndex = toolContent.rfind(contentEndTag)
                    if contentStartIndex != -1 and contentEndIndex != -1 and contentEndIndex > contentStartIndex:
                        currentToolUse.params[contentParamName] = toolContent[contentStartIndex:contentEndIndex].strip()

                # partial tool value is accumulating
                continue

        # no currentToolUse

        didStartToolUse = False
        possibleToolUseOpeningTags = [f"<{name}>" for name in toolUseNames]
        for toolUseOpeningTag in possibleToolUseOpeningTags:
            if accumulator.endswith(toolUseOpeningTag) and \
                not accumulator.endswith(f"`{toolUseOpeningTag}"): # temporarily workaround for `<use_mcp_tool>` in text
                # start of a new tool use
                currentToolUse = ToolUse(name=toolUseOpeningTag[1:-1], params={})
                currentToolUseStartIndex = len(accumulator)
                # this also indicates the end of the current text content
                if currentTextContent:
                    currentTextContent.partial = False
                    # remove the partially accumulated tool use tag from the end of text (<tool)
                    currentTextContent.content = currentTextContent.content[:-len(toolUseOpeningTag[:-1])].strip()
                    contentBlocks.append(currentTextContent)
                    currentTextContent = None

                didStartToolUse = True
                break

        if not didStartToolUse:
            # no tool use, so it must be text either at the beginning or between tools
            if currentTextContent is None:
                currentTextContentStartIndex = i
            currentTextContent = TextContent(content=accumulator[currentTextContentStartIndex:].strip(), partial=True)

    if currentToolUse:
        # stream did not complete tool call, add it as partial
        if currentParamName:
            # tool call has a parameter that was not completed
            currentToolUse.params[currentParamName] = accumulator[currentParamValueStartIndex:].strip()
        contentBlocks.append(currentToolUse)
    elif currentTextContent: 
        # temprory workaround for strings that are not tool calls
        currentTextContent.partial = False

    # Note: it doesnt matter if check for currentToolUse or currentTextContent, only one of them will be defined since only one can be partial at a time
    if currentTextContent:
        # stream did not complete text content, add it as partial
        contentBlocks.append(currentTextContent)

    return contentBlocks

def main():
#     test_message = """
# Alright, the user is asking, "what is the time now?" I need to figure out how to provide the current time. I remember that there are tools available, specifically the MCP tools. Let me check the available servers and tools.\n\nLooking at the MCP server named "time," it has two tools: get_current_time and convert_time. Since the user just wants the current time, get_current_time seems appropriate. This tool requires a timezone parameter. The user didn\'t specify a timezone, so I should default to \'Asia/Shanghai\' as per the tool\'s input schema.\n\nI\'ll structure the tool use with the server name, tool name, and the arguments. The arguments need to be in JSON format, so I\'ll make sure to include the timezone correctly. I\'ll set it to \'Asia/Shanghai\' since that\'s the default if not provided.\n\nNow, I\'ll format the tool use using the specified XML-style tags. I\'ll include the server_name, tool_name, and the arguments with the timezone. Double-checking the syntax to ensure there are no errors.\n\nOnce I send this, the user will execute the tool and provide the result. I should wait for their response before proceeding further, just in case there\'s an issue or if more information is needed.\n</think>\n\nI\'ll use the `get_current_time` tool from the time server to get the current time. Since no specific timezone is requested, I\'ll use \'Asia/Shanghai\' as the default.\n\n<use_mcp_tool>\n<server_name>time</server_name>\n<tool_name>get_current_time</tool_name>\n<arguments>\n{\n  "timezone": "Asia/Shanghai"\n}\n</arguments>\n</use_mcp_tool>
# """

#     test_message = """
# Okay, \n</think>\n\n<use_mcp_tool>\n<server_name>time</server_name>\n<tool_name>get_current_time</tool_name>\n<arguments>\n{\n  "timezone": "Asia/Shanghai"\n}\n</arguments>\n</use_mcp_tool>
# """
    test_message = """
Alright, so the user asked, "What is the time now?" and I used the MCP tool to get the current time in Shanghai. The tool responded with the time as "2025-02-24T16:52:47+08:00". \n\nHmm, I need to make sure I format this correctly. The user probably wants the time in a more readable format rather than the ISO string. Maybe I should extract the time part from the datetime field.\n\nLooking at the result, the time is "16:52:47" in 24-hour format. I can convert that to 12-hour format for better readability. So, that would be 4:52 PM.\n\nI should present this in a clear and friendly way, letting the user know the current time in Shanghai. I\'ll phrase it naturally, ensuring the user understands the information easily.\n</think>\n\nThe current time in Shanghai is **4:52 PM**.
"""

    result = parseAssistantMessage(test_message)
    for block in result:
        print(f"Type: {block.type}, Content: {block.content}, Name: {block.name}, Params: {block.params}, Partial: {block.partial}")

if __name__ == "__main__":
    main()
