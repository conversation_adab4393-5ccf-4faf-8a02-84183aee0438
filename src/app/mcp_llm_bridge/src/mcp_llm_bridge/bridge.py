# src/mcp_llm_bridge/bridge.py
from typing import Dict, List, Any, Optional
from mcp_llm_bridge.mcp_client import MCPClient
from mcp_llm_bridge.llm_client import LLMClient
import json
from mcp_llm_bridge.config import BridgeConfig
import logging
import colorlog
from parse_assistant_message import ToolUse, toolUseNames
import importlib
from system_prompt import mcp_tool_result_user_prompt, local_tool_result_user_prompt

handler = colorlog.StreamHandler()
handler.setFormatter(colorlog.ColoredFormatter(
    "%(log_color)s%(levelname)s%(reset)s:     %(cyan)s%(name)s%(reset)s - %(message)s",
    datefmt=None,
    reset=True,
    log_colors={
        'DEBUG': 'cyan',
        'INFO': 'green',
        'WARNING': 'yellow',
        'ERROR': 'red',
        'CRITICAL': 'red,bg_white',
    },
    secondary_log_colors={},
    style='%'
))

logger = colorlog.getLogger(__name__)
logger.addHandler(handler)
logger.setLevel(logging.INFO)

class MCPLLMBridge:
    """Bridge between MCP protocol and LLM client"""
    
    def __init__(self, config: BridgeConfig):
        self.config = config
        # self.mcp_clients = [MCPClient(server_config.name, server_config.params) for server_config in config.mcp_server_configs]
        self.mcp_clients = {server_config.name: MCPClient(server_config.name, server_config.params) for server_config in config.mcp_server_configs}
        self.llm_client = LLMClient(config.llm_config)
        
        # if config.system_prompt:
        #     self.llm_client.system_prompt = f"{config.system_prompt}\n\n{schema_prompt}"
        # else:
        #     self.llm_client.system_prompt = schema_prompt
        
            
        self.mcp_tools: Dict[str, List[Any]] = {}

        self.local_tools: Any = None

    async def initialize(self):
        """Initialize both clients and set up tools"""
        try:
            # Connect MCP client
            for mcp_client in list(self.mcp_clients.values()):
                await mcp_client.connect()
            
                # Get available tools from MCP and add our database tool
                mcp_tools = await mcp_client.get_available_tools()
                if hasattr(mcp_tools, 'tools'):
                    self.mcp_tools = {mcp_client.server_name: [*mcp_tools.tools]}
                else:
                    self.mcp_tools = {mcp_client.server_name: [*mcp_tools]}
            
            logger.debug(f"MCP Tools received: {self.mcp_tools}")
            
            # Initialize local tools
            # self.local_tools = sys.modules["local_tools"]
            self.local_tools = importlib.import_module("local_tools")

            # Convert MCP tools and local tools to system prompt
            mcp_tools_system_prompt = self._convert_mcp_tools_to_string()
            local_tools_system_prompt = self._convert_local_tools_to_string()
            logger.debug(f"MCP Tools prompt: \n{mcp_tools_system_prompt}")
            logger.debug(f"Local Tools prompt: \n{local_tools_system_prompt}")
            
            # set llm client system prompt
            tool_data = {
                "Tools": local_tools_system_prompt,
                "MCP_Tools": mcp_tools_system_prompt
            }
            self.llm_client.system_prompt = self.config.system_prompt.format(**tool_data)

            return True
        except Exception as e:
            logger.error(f"Bridge initialization failed: {str(e)}", exc_info=True)
            return False

    def _convert_mcp_tools_to_string(self) -> str:
        tool_str = "# Availabe MCP server and tools\n"
        mcp_server_tools = []
        
        logger.debug(f"Input mcp_tools type: {type(self.mcp_tools)}")
        logger.debug(f"Input mcp_tools: {self.mcp_tools}")

        for server_name, tools_list in self.mcp_tools.items():
            # Extract tools from the response
            if hasattr(tools_list, 'tools'):
                tools_list = tools_list.tools
                logger.debug("Found ListToolsResult, extracting tools attribute")
            elif isinstance(tools_list, dict):
                tools_list = tools_list.get('tools', [])
                logger.debug("Found dict, extracting 'tools' key")
            else:
                tools_list = tools_list
                logger.debug("Using mcp_tools directly as list")
                
            logger.debug(f"Tools list type: {type(tools_list)}")
            logger.debug(f"Tools list: {tools_list}")
            
            # Process each tool in the list
            if isinstance(tools_list, list):
                logger.debug(f"Processing {len(tools_list)} tools")
                converted_tool_list = []
                for tool in tools_list:
                    logger.debug(f"Processing tool: {tool}, type: {type(tool)}")
                    if hasattr(tool, 'name') and hasattr(tool, 'description'):
                        logger.debug(f"Tool has required attributes. Name: {tool.name}")
                        
                        tool_schema = getattr(tool, 'inputSchema', {
                            "type": "object",
                            "properties": {},
                            "required": []
                        })
                        
                        converted_tool_list.append({
                            "name": tool.name,
                            "description": tool.description,
                            "inputSchema": tool_schema
                        })
                        
                        logger.debug(f"Converted tool {tool.name} to prompt format")
                    else:
                        logger.debug(f"Tool missing required attributes: has name = {hasattr(tool, 'name')}, has description = {hasattr(tool, 'description')}")
                
                mcp_server_tools.append({
                    "server_name": server_name,
                    "tools": converted_tool_list
                })
            else:
                logger.debug(f"Tools list is not a list, it's a {type(tools_list)}")
        
        tool_str += json.dumps(mcp_server_tools, indent=4)
        return tool_str

    def _convert_local_tools_to_string(self) -> str:
        tools_str = ""
        for tool_name in toolUseNames:
            if hasattr(self.local_tools, tool_name):
                tool = getattr(self.local_tools, tool_name)
                tools_str += f"##{tool_name}\n{tool.__doc__}\n"
        return tools_str

    async def process_message(self, message: str) -> str:
        """Process a user message through the bridge"""
        try:
            # Send message to LLM
            logger.debug(f"Sending message to LLM: {message}")
            response = await self.llm_client.invoke_with_prompt(message)
            logger.debug(f"LLM Response: {response}")
            
            # Keep processing tool calls until we get a final response
            while response.is_tool_call:
                if not response.tool_calls:
                    break
                    
                logger.debug(f"Tool calls detected: {response.tool_calls}")
                tool_responses = await self._handle_tool_calls(response.tool_calls)
                logger.debug(f"Tool responses: {tool_responses}")
                
                # Continue the conversation with tool results
                response = await self.llm_client.invoke(tool_responses)
                logger.debug(f"Next LLM response: {response}")
            
            return response.content
        except Exception as e:
            logger.error(f"Error processing message: {str(e)}", exc_info=True)
            return f"Error processing message: {str(e)}"

    async def _handle_tool_calls(self, tool_calls: List[ToolUse]) -> List[Dict[str, Any]]:
        """Handle tool calls through MCP"""
        tool_responses = []
        
        for tool_call in tool_calls:
            try:
                logger.debug(f"Processing tool call: {tool_call}")
                if tool_call.name == "use_mcp_tool": 
                    # Get original MCP tool name
                    params = tool_call.params
                    server_name = params.get("server_name")
                    tool_name = params.get("tool_name")
                    arguments = json.loads(params.get("arguments"))
                    logger.debug(f"Tool arguments: {arguments}")
                    
                    # Execute through MCP
                    mcp_client = self.mcp_clients[server_name]
                    result = await mcp_client.call_tool(tool_name, arguments)
                    logger.debug(f"Raw MCP result: {result}")
                else:
                    # local tool
                    if hasattr(self.local_tools, tool_call.name):
                        result = getattr(self.local_tools, tool_call.name)(**tool_call.params)
                    else:
                        raise Exception(f"Tool not found: {tool_call.name}")
                
                logger.debug(f"Tool result: {result}")
                
                # Format response - handle both string and structured results
                if isinstance(result, str):
                    output = result
                elif hasattr(result, 'content') and isinstance(result.content, list):
                    # Handle MCP CallToolResult format
                    output = " ".join(
                        content.text for content in result.content 
                        if hasattr(content, 'text')
                    )
                else:
                    output = str(result)  # Use str() instead of json.dumps()
                
                logger.debug(f"Formatted output: {output}")
                
                if tool_call.name == "use_mcp_tool":
                    # Format mcp tool response
                    tool_result_data = {
                        "Server_Name": server_name,
                        "Tool_Name": tool_name,
                        "Tool_Result": output
                    }
                    user_prompt = mcp_tool_result_user_prompt.format(**tool_result_data)
                    
                    tool_responses.append({
                        "tool_call_id": f"{server_name}.{tool_name}",
                        "output": output,
                        "formated_output": user_prompt
                    })
                else:
                    # Format local tool response
                    tool_result_data = {
                        "Tool_Name": tool_call.name,
                        "Tool_Result": output
                    }
                    user_prompt = local_tool_result_user_prompt.format(**tool_result_data)

                    tool_responses.append({
                        "tool_call_id": tool_call.name,
                        "output": output,
                        "formated_output": user_prompt
                    })
                
            except Exception as e:
                logger.error(f"Tool execution failed: {str(e)}", exc_info=True)
                # TODO: Format error message
                tool_responses.append({
                    "tool_call_id": f"{server_name}.{tool_name}",
                    "output": f"Error: {str(e)}",
                    "formated_output": mcp_tool_result_user_prompt.format({
                        "Server_Name": server_name,
                        "Tool_Name": tool_name,
                        "Tool_Result": f"Error: {str(e)}"
                    })
                })
        
        return tool_responses

    async def close(self):
        """Clean up resources"""
        for mcp_client in list(self.mcp_clients.values()):
            await mcp_client.__aexit__(None, None, None)

class BridgeManager:
    """Manager class for handling the bridge lifecycle"""
    
    def __init__(self, config: BridgeConfig):
        self.config = config
        self.bridge: Optional[MCPLLMBridge] = None

    async def __aenter__(self) -> MCPLLMBridge:
        """Context manager entry"""
        self.bridge = MCPLLMBridge(self.config)
        await self.bridge.initialize()
        return self.bridge
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        if self.bridge:
            await self.bridge.close()