import streamlit as st
from datetime import datetime
from langchain_community.document_loaders import PyPDFLoader
from langchain_chroma import Chroma
from langchain_ollama import ChatOllama
from langchain_ollama import OllamaEmbeddings
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain_core.runnables import RunnablePassthrough
import os

class PDFRag:
    pdf_path = "./uploaded"
    vectorstore_path = "./vectorstore"
    model = ChatOllama(model="llama3.1:8b",)
    local_embeddings = OllamaEmbeddings(model="nomic-embed-text")
    system_prompt = (
        "You are an assistant for question-answering tasks. "
        "Use the following pieces of retrieved context to answer "
        "the question. If you don't know the answer, say that you "
        "don't know. Use three sentences maximum and keep the "
        "answer concise."
        "\n\n"
        "{context}"
    )

    prompt = ChatPromptTemplate.from_messages(
        [
            ("system", system_prompt),
            ("human", "{question}"),
        ]
    )

    if not os.path.exists(pdf_path):
        os.mkdir(pdf_path)

    if not os.path.exists(vectorstore_path):
        os.mkdir(vectorstore_path)

    def __init__(self, pdf_file):
        if not os.path.exists(pdf_file):
            raise Exception(f"{pdf_file} not exists!!!")

        self.pdf_file = pdf_file
        self.init()
    
    def format_docs(self, docs):
        return "\n\n".join(doc.page_content for doc in docs)

    def init(self):
        try:
            # load file
            loader = PyPDFLoader(self.pdf_file)
            docs = loader.load()
            print("load file done")           

            # split document
            text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)
            all_splits = text_splitter.split_documents(docs)
            print("get all splits")
            
            # Store in vectorstore and get a retriever
            Chroma.from_documents(documents=all_splits, embedding=PDFRag.local_embeddings, persist_directory=PDFRag.vectorstore_path)
            print("stored in vectorstore")
            
            print("Init pdf rag done!!!")        
        except Exception as ex:
            print("failed to init chain with exception: ", ex)

    def generate_response(self, input):
        vectorstore = Chroma(persist_directory=PDFRag.vectorstore_path, embedding_function=PDFRag.local_embeddings)
        retriever = vectorstore.as_retriever()
        chain = (
            {"context": retriever | self.format_docs, "question": RunnablePassthrough()}
            | PDFRag.prompt
            | PDFRag.model
            | StrOutputParser()
        )
        return chain.invoke(input) 

st.title('Learning From PDF')
info_placeholder = st.empty()
uploaded_file = st.file_uploader("Choose a file", type=["pdf"])
input_text = st.text_input(label="Ask me something")
submitted = st.button("Submit")

if 'chain_obj' not in st.session_state:
    st.session_state.chain_obj = None

def get_response(input):
    resp = datetime.now().strftime("%Y-%m-%d %H:%M:%S") + "\n\n"
    resp += st.session_state.chain_obj.generate_response(input)
    return resp

if uploaded_file is not None:
    # save file
    target_file = f"{PDFRag.pdf_path}/{uploaded_file.name}"
    if st.session_state.chain_obj is None:
        with open(target_file, "wb") as f:
            f.write(uploaded_file.getbuffer())
        # init chain
        info_placeholder.warning("Waiting for init...")
        st.session_state.chain_obj = PDFRag(target_file)
        info_placeholder.info(f"Init done for {target_file}, you could start asking questions")
    else:
        info_placeholder.info(f"{target_file} already loaded, you could start asking questions")
else:
    st.session_state.chain_obj = None
    info_placeholder.info("Waiting for upload file...")

# show response
if submitted:
    if st.session_state.chain_obj is None:
        info_placeholder.error("Has not initialized!!!")
    elif not input_text:
        info_placeholder.error("Input something!!!")
    else:
        st.text_area("Answer", value=get_response(input_text), height=200, key="readonly_text")
