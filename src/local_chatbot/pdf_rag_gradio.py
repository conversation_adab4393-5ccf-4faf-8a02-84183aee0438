import gradio as gr
import os
import shutil
from langchain_community.document_loaders import PyPDFLoader
from langchain_chroma import Chroma
from langchain_ollama import ChatOllama
from langchain_ollama import OllamaEmbeddings
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain_core.runnables import RunnablePassthrough

chain_obj = None

class PDFRag:
    pdf_path = "./uploaded"
    vectorstore_path = "./vectorstore"
    model = ChatOllama(model="llama3.1:8b",)
    local_embeddings = OllamaEmbeddings(model="nomic-embed-text")
    system_prompt = (
        "You are an assistant for question-answering tasks. "
        "Use the following pieces of retrieved context to answer "
        "the question. If you don't know the answer, say that you "
        "don't know. Use three sentences maximum and keep the "
        "answer concise."
        "\n\n"
        "{context}"
    )

    prompt = ChatPromptTemplate.from_messages(
        [
            ("system", system_prompt),
            ("human", "{question}"),
        ]
    )

    if not os.path.exists(pdf_path):
        os.mkdir(pdf_path)

    if not os.path.exists(vectorstore_path):
        os.mkdir(vectorstore_path)

    def __init__(self, pdf_file):
        if not os.path.exists(pdf_file):
            raise Exception(f"{pdf_file} not exists!!!")

        self.pdf_file = pdf_file
        self.init()
    
    def format_docs(self, docs):
        return "\n\n".join(doc.page_content for doc in docs)

    def init(self):
        try:
            # load file
            loader = PyPDFLoader(self.pdf_file)
            docs = loader.load()
            print("load file done")           

            # split document
            text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)
            all_splits = text_splitter.split_documents(docs)
            print("get all splits")
            
            # Store in vectorstore and get a retriever
            Chroma.from_documents(documents=all_splits, embedding=PDFRag.local_embeddings, persist_directory=PDFRag.vectorstore_path)
            print("stored in vectorstore")
            
            print("Init pdf rag done!!!")        
        except Exception as ex:
            print("failed to init chain with exception: ", ex)

    def generate_response(self, input):
        vectorstore = Chroma(persist_directory=PDFRag.vectorstore_path, embedding_function=PDFRag.local_embeddings)
        retriever = vectorstore.as_retriever()
        chain = (
            {"context": retriever | self.format_docs, "question": RunnablePassthrough()}
            | PDFRag.prompt
            | PDFRag.model
            | StrOutputParser()
        )
        return chain.invoke(input)

def learn_and_chat(message, history):
    global chain_obj

    text_input = message['text']
    files = message["files"]
    
    if len(files) > 0:
        uploaded_file = files[0]
        if uploaded_file.lower().endswith(".pdf"):
            filename = os.path.basename(uploaded_file)
            target_file = f"{PDFRag.pdf_path}/{filename}"
            shutil.copy2(uploaded_file, target_file)
            print(f"target_file saved.")
            chain_obj = PDFRag(target_file)
            print(f"chain obj initialized.")
            return f"You could ask me anything about {filename}"
        else:
            return "Only support PDF format."

    if text_input:
        if chain_obj:
            if text_input.lower() == "restart":
                chain_obj = None
                return "Uploaded file is cleared, upload a new file."
            else:
                print("generating response...")
                return chain_obj.generate_response(text_input)
        else:
            return "Upload a PDF file first"

demo = gr.ChatInterface(fn=learn_and_chat, type="messages", \
                        examples=[{"text": "Upload a PDF file and I can answer questions about it! Say 'restart' to clear.", "files": []}], \
                        title="Learn and Chat", multimodal=True)
demo.launch()