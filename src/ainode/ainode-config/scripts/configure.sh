#!/bin/bash

set -e

echo "Starting AI node configuration..."

configure_system() {
    log "Configuring Operating System settings..."
    sudo sysctl -w fs.inotify.max_user_watches=2099999999
    sudo sysctl -w fs.inotify.max_user_instances=2099999999
    sudo sysctl -w fs.inotify.max_queued_events=2099999999

    for BDF in `lspci -d "*:*:*" | awk '{print $1}'`; do
        # skip if it doesn't support ACS
        sudo setpci -v -s ${BDF} ECAP_ACS+0x6.w > /dev/null 2>&1
        if [ $? -ne 0 ]; then
            continue
        fi
        sudo setpci -v -s ${BDF} ECAP_ACS+0x6.w=0000
    done
}

configure_system

log "AI node configuration completed successfully" 

