/*
Copyright 2025.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// EDIT THIS FILE!  THIS IS SCAFFOLDING FOR YOU TO OWN!
// NOTE: json tags are required.  Any new fields you add must have json tags for the fields to be serialized.

// AINodeSpec defines the desired state of AINode.
type AINodeSpec struct {
	// INSERT ADDITIONAL SPEC FIELDS - desired state of cluster
	// Important: Run "make" to regenerate code after modifying this file

	// NodeRef references the Kubernetes Node to be configured
	NodeRef NodeReference `json:"nodeRef"`
}

// NodeReference contains information to reference a Kubernetes Node
type NodeReference struct {
	// Name of the node
	Name string `json:"name"`
}

// AINodeStatus defines the observed state of AINode.
type AINodeStatus struct {
	// Phase represents the current phase of node configuration
	// +kubebuilder:validation:Enum=Pending;Configuring;Configured;Failed
	Phase string `json:"phase"`

	// LastConfigurationAttempt contains information about the last configuration attempt
	LastConfigurationAttempt *ConfigurationAttempt `json:"lastConfigurationAttempt,omitempty"`
}

// Constants for AINode phases
const (
	PhasePending     = "Pending"
	PhaseConfiguring = "Configuring"
	PhaseConfigured  = "Configured"
	PhaseFailed      = "Failed"
)

// ConfigurationAttempt contains information about a configuration attempt
type ConfigurationAttempt struct {
	// Time of the configuration attempt
	Time metav1.Time `json:"time"`

	// Status of the configuration attempt
	Status string `json:"status"`

	// Message provides additional information about the configuration attempt
	Message string `json:"message"`
}

// +kubebuilder:object:root=true
// +kubebuilder:subresource:status
//+kubebuilder:printcolumn:name="Node",type=string,JSONPath=`.spec.nodeRef.name`
//+kubebuilder:printcolumn:name="Phase",type=string,JSONPath=`.status.phase`
//+kubebuilder:printcolumn:name="Age",type="date",JSONPath=".metadata.creationTimestamp"

// AINode is the Schema for the ainodes API.
type AINode struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   AINodeSpec   `json:"spec,omitempty"`
	Status AINodeStatus `json:"status,omitempty"`
}

// +kubebuilder:object:root=true

// AINodeList contains a list of AINode.
type AINodeList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []AINode `json:"items"`
}

func init() {
	SchemeBuilder.Register(&AINode{}, &AINodeList{})
}
