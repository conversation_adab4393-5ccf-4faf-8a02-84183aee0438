//go:build !ignore_autogenerated

/*
Copyright 2025.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by controller-gen. DO NOT EDIT.

package v1

import (
	runtime "k8s.io/apimachinery/pkg/runtime"
)

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AINode) DeepCopyInto(out *AINode) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	out.Spec = in.Spec
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AINode.
func (in *AINode) DeepCopy() *AINode {
	if in == nil {
		return nil
	}
	out := new(AINode)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *AINode) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AINodeList) DeepCopyInto(out *AINodeList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]AINode, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AINodeList.
func (in *AINodeList) DeepCopy() *AINodeList {
	if in == nil {
		return nil
	}
	out := new(AINodeList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *AINodeList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AINodeSpec) DeepCopyInto(out *AINodeSpec) {
	*out = *in
	out.NodeRef = in.NodeRef
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AINodeSpec.
func (in *AINodeSpec) DeepCopy() *AINodeSpec {
	if in == nil {
		return nil
	}
	out := new(AINodeSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AINodeStatus) DeepCopyInto(out *AINodeStatus) {
	*out = *in
	if in.LastConfigurationAttempt != nil {
		in, out := &in.LastConfigurationAttempt, &out.LastConfigurationAttempt
		*out = new(ConfigurationAttempt)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AINodeStatus.
func (in *AINodeStatus) DeepCopy() *AINodeStatus {
	if in == nil {
		return nil
	}
	out := new(AINodeStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ConfigurationAttempt) DeepCopyInto(out *ConfigurationAttempt) {
	*out = *in
	in.Time.DeepCopyInto(&out.Time)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ConfigurationAttempt.
func (in *ConfigurationAttempt) DeepCopy() *ConfigurationAttempt {
	if in == nil {
		return nil
	}
	out := new(ConfigurationAttempt)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *NodeReference) DeepCopyInto(out *NodeReference) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new NodeReference.
func (in *NodeReference) DeepCopy() *NodeReference {
	if in == nil {
		return nil
	}
	out := new(NodeReference)
	in.DeepCopyInto(out)
	return out
}
