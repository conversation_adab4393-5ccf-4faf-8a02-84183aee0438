---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.4
  name: ainodes.x.x.com
spec:
  group: x.x.com
  names:
    kind: AINode
    listKind: AINodeList
    plural: ainodes
    singular: ainode
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .spec.nodeRef.name
      name: Node
      type: string
    - jsonPath: .status.phase
      name: Phase
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1
    schema:
      openAPIV3Schema:
        description: AINode is the Schema for the ainodes API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: AINodeSpec defines the desired state of AINode.
            properties:
              nodeRef:
                description: NodeRef references the Kubernetes Node to be configured
                properties:
                  name:
                    description: Name of the node
                    type: string
                required:
                - name
                type: object
            required:
            - nodeRef
            type: object
          status:
            description: AINodeStatus defines the observed state of AINode.
            properties:
              lastConfigurationAttempt:
                description: LastConfigurationAttempt contains information about the
                  last configuration attempt
                properties:
                  message:
                    description: Message provides additional information about the
                      configuration attempt
                    type: string
                  status:
                    description: Status of the configuration attempt
                    type: string
                  time:
                    description: Time of the configuration attempt
                    format: date-time
                    type: string
                required:
                - message
                - status
                - time
                type: object
              phase:
                description: Phase represents the current phase of node configuration
                enum:
                - Pending
                - Configuring
                - Configured
                - Failed
                type: string
            required:
            - phase
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
