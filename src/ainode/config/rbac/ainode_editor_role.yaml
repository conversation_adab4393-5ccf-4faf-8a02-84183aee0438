# permissions for end users to edit ainodes.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: ainode
    app.kubernetes.io/managed-by: kustomize
  name: ainode-editor-role
rules:
- apiGroups:
  - x.x.com
  resources:
  - ainodes
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - x.x.com
  resources:
  - ainodes/status
  verbs:
  - get
