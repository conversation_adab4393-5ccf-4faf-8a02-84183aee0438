---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: manager-role
rules:
- apiGroups:
  - ""
  resources:
  - nodes
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - apps
  resources:
  - daemonsets
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - x.x.com
  resources:
  - ainodes
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - x.x.com
  resources:
  - ainodes/finalizers
  verbs:
  - update
- apiGroups:
  - x.x.com
  resources:
  - ainodes/status
  verbs:
  - get
  - patch
  - update
