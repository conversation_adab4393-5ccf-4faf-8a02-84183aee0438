/*
Copyright 2025.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package controller

import (
	"context"
	"fmt"
	"time"

	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/log"

	xv1 "x.com/ainode/api/v1"
)

// AINodeReconciler reconciles a AINode object
type AINodeReconciler struct {
	client.Client
	Scheme *runtime.Scheme
}

// +kubebuilder:rbac:groups=x.x.com,resources=ainodes,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=x.x.com,resources=ainodes/status,verbs=get;update;patch
// +kubebuilder:rbac:groups=x.x.com,resources=ainodes/finalizers,verbs=update
//+kubebuilder:rbac:groups=apps,resources=daemonsets,verbs=get;list;watch;create;update;patch;delete
//+kubebuilder:rbac:groups=core,resources=nodes,verbs=get;list;watch

func (r *AINodeReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	_ = log.FromContext(ctx)

	// Fetch the AINode instance
	aiNode := &xv1.AINode{}
	err := r.Get(ctx, req.NamespacedName, aiNode)
	if err != nil {
		if errors.IsNotFound(err) {
			return ctrl.Result{}, nil
		}
		return ctrl.Result{}, err
	}

	// Check if the referenced node exists
	node := &corev1.Node{}
	err = r.Get(ctx, types.NamespacedName{Name: aiNode.Spec.NodeRef.Name}, node)
	if err != nil {
		if errors.IsNotFound(err) {
			r.updateStatus(ctx, aiNode, xv1.PhaseFailed, "NodeNotFound", "Referenced node not found")
			return ctrl.Result{RequeueAfter: time.Minute}, nil
		}
		return ctrl.Result{}, err
	}

	// Update phase to Configuring if currently Pending
	if aiNode.Status.Phase == "" || aiNode.Status.Phase == xv1.PhasePending {
		r.updateStatus(ctx, aiNode, xv1.PhaseConfiguring, "Configuration", "Starting node configuration")
	}

	// Create or update the configuration DaemonSet
	if err := r.reconcileConfigurationDaemonSet(ctx, aiNode); err != nil {
		r.updateStatus(ctx, aiNode, xv1.PhaseFailed, "ConfigurationFailed", fmt.Sprintf("Failed to configure node: %v", err))
		return ctrl.Result{RequeueAfter: time.Minute}, nil
	}

	// Check DaemonSet status
	ds := &appsv1.DaemonSet{}
	dsName := fmt.Sprintf("ainode-config-%s", aiNode.Name)
	err = r.Get(ctx, types.NamespacedName{Name: dsName, Namespace: "kube-system"}, ds)
	if err != nil {
		return ctrl.Result{}, err
	}

	// Update status based on DaemonSet status
	if ds.Status.NumberReady > 0 {
		r.updateStatus(ctx, aiNode, xv1.PhaseConfigured, "ConfigurationComplete", "Node configuration completed")
		return ctrl.Result{RequeueAfter: time.Hour}, nil
	}

	return ctrl.Result{RequeueAfter: time.Minute}, nil
}

func (r *AINodeReconciler) reconcileConfigurationDaemonSet(ctx context.Context, aiNode *xv1.AINode) error {
	ds := &appsv1.DaemonSet{
		ObjectMeta: metav1.ObjectMeta{
			Name:      fmt.Sprintf("ainode-config-%s", aiNode.Name),
			Namespace: "kube-system",
		},
	}

	op, err := ctrl.CreateOrUpdate(ctx, r.Client, ds, func() error {
		if err := ctrl.SetControllerReference(aiNode, ds, r.Scheme); err != nil {
			return err
		}

		ds.Spec = appsv1.DaemonSetSpec{
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app": fmt.Sprintf("ainode-config-%s", aiNode.Name),
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app": fmt.Sprintf("ainode-config-%s", aiNode.Name),
					},
				},
				Spec: corev1.PodSpec{
					NodeSelector: map[string]string{
						"kubernetes.io/hostname": aiNode.Spec.NodeRef.Name,
					},
					Containers: []corev1.Container{
						{
							Name:  "config",
							Image: "ai-node-daemon:latest",
							SecurityContext: &corev1.SecurityContext{
								Privileged: &[]bool{true}[0],
							},
							VolumeMounts: []corev1.VolumeMount{
								{
									Name:      "host",
									MountPath: "/host",
								},
							},
						},
					},
					Volumes: []corev1.Volume{
						{
							Name: "host",
							VolumeSource: corev1.VolumeSource{
								HostPath: &corev1.HostPathVolumeSource{
									Path: "/",
								},
							},
						},
					},
				},
			},
		}
		return nil
	})

	if err != nil {
		return err
	}

	log.FromContext(ctx).Info("DaemonSet reconciled", "operation", op)
	return nil
}

func (r *AINodeReconciler) updateStatus(ctx context.Context, aiNode *xv1.AINode, phase, status, message string) {
	aiNode.Status.Phase = phase
	aiNode.Status.LastConfigurationAttempt = &xv1.ConfigurationAttempt{
		Time:    metav1.Now(),
		Status:  status,
		Message: message,
	}

	if err := r.Status().Update(ctx, aiNode); err != nil {
		log.FromContext(ctx).Error(err, "Failed to update AINode status")
	}
}

// SetupWithManager sets up the controller with the Manager.
func (r *AINodeReconciler) SetupWithManager(mgr ctrl.Manager) error {
	return ctrl.NewControllerManagedBy(mgr).
		For(&xv1.AINode{}).
		Owns(&appsv1.DaemonSet{}).
		Complete(r)
}
