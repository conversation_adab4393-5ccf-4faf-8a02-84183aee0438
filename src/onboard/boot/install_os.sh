#!/bin/bash
set -e

# Default values
default_idrac_ips="************,************,************,************"
default_http_server="http://************"
default_os_iso_url="http://************/ubuntu-22.04.5-live-server-amd64.iso"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --idrac-ips)
            input_idrac_ips="$2"
            shift 2
            ;;
        --http-server)
            http_server_addr="$2"
            shift 2
            ;;
        --os-iso-url)
            os_iso_url="$2"
            shift 2
            ;;
        *)
            echo "Unknown parameter: $1"
            echo "Usage: $0 --idrac-ips ip1,ip2,ip3 --http-server http://server-addr --os-iso-url http://server/os.iso"
            exit 1
            ;;
    esac
done

# Set default values if not provided
http_server_addr=${http_server_addr:-$default_http_server}
os_iso_url=${os_iso_url:-$default_os_iso_url}
input_idrac_ips=${input_idrac_ips:-$default_idrac_ips}

# Convert comma-separated IPs to array
IFS=',' read -r -a idrac_ip_list <<< "$input_idrac_ips"

workdir=$(pwd)
autoinstall_config_dir=$(pwd)/autoinstall

for idrac_ip in "${idrac_ip_list[@]}"; do
    echo "prepar installing $idrac_ip..."
    # gen iso
    meta_data_file=$autoinstall_config_dir/meta-data
    user_data_file=$autoinstall_config_dir/user-data-${idrac_ip}
    seed_iso_file=$workdir/seed-${idrac_ip}.iso

    if [ ! -f $meta_data_file ]; then
        touch $meta_data_file
    fi
    if [ ! -f $user_data_file ]; then
        echo "NO user data file ${user_data_file}, exit"
        exit
    fi
    if [ -f $seed_iso_file ]; then
        rm $seed_iso_file
    fi

    if ! command -v genisoimage &>/dev/null; then
        # If genisoimage doesn't exist, install it
        apt install -y genisoimage
    fi

    genisoimage -output "$seed_iso_file" -volid cidata -joliet -rock -graft-points meta-data=$meta_data_file user-data=$user_data_file

    # upload seed_iso_file to http server
    curl -T ${seed_iso_file} ${http_server_addr}/$(basename ${seed_iso_file})
    echo "Seed ISO file uploaded to: ${http_server_addr}/$(basename ${seed_iso_file})"
    
    # remove local seed iso file after upload
    rm -f ${seed_iso_file}
    echo "Local seed ISO file removed: ${seed_iso_file}"

    # mount iso and reboot to install
    seed_iso_url=${http_server_addr}/$(basename ${seed_iso_file})
    python3 mount-iso-to-2nd-virtual-media.py --iso-url ${seed_iso_url} --idrac-ip ${idrac_ip}
    python3 mount-iso-to-1st-virtual-media.py --iso-url ${os_iso_url} --idrac-ip ${idrac_ip}
    echo "start to install ${idrac_ip}!!!"
done
