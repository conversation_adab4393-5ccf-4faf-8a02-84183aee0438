#cloud-config
# See the autoinstall documentation at:
# https://canonical-subiquity.readthedocs-hosted.com/en/latest/reference/autoinstall-reference.html
autoinstall:
  apt:
    disable_components: []
    fallback: offline-install
    geoip: false
    mirror-selection:
      primary:
      - country-mirror
      - arches: &id001
        - amd64
        - i386
        uri: http://archive.ubuntu.com/ubuntu/
      - arches: &id002
        - s390x
        - arm64
        - armhf
        - powerpc
        - ppc64el
        - riscv64
        uri: http://ports.ubuntu.com/ubuntu-ports
    preserve_sources_list: false
    security:
    - arches: *id001
      uri: http://security.ubuntu.com/ubuntu/
    - arches: *id002
      uri: http://ports.ubuntu.com/ubuntu-ports
  codecs:
    install: false
  drivers:
    install: false
  identity:
    hostname: node114
    password: $6$LBuWnkbGEqdzeBE9$GwzCvDRwkVq7Hzx54V4M/J.o2sqIEyODUZ4klveWZn4XbfmCc2cXOndms3Fj8IRVBptLoi2KoSaMqNlGJyrcC0
    realname: mystic
    username: mystic
  kernel:
    package: linux-generic
  keyboard:
    layout: us
    toggle: null
    variant: ''
  locale: en_US.UTF-8
  network:
    ethernets:
      eno12399np0:
        dhcp4: false
        addresses:
          - ***************/24
        routes:
          - to: default
            via: **************
        nameservers:
          addresses: [***********]
      other-interfaces:
        match:
          name: en*  # Adjust as needed (e.g., `eth*`, `ens*`, etc.)
        dhcp4: true
        optional: true  # Avoid boot delays if DHCP fails
    version: 2
  oem:
    install: auto
  source:
    id: ubuntu-server
    search_drivers: false
  ssh:
    allow-pw: true
    authorized-keys: []
    install-server: true
  user-data:
    chpasswd:
      list: |
        root:$6$uchRh6qdZbzFKRKV$HXE3hUl7YtnSNfb/3qJRnCTIX6Mmuv3/sW0ZCZNEgv6Jwnao7OTeET.70ZVMMhDNl9YPWAGLrZG1bhLH5nLvU/
      expire: False
    runcmd: 
      - sed -i 's/#PermitRootLogin prohibit-password/PermitRootLogin yes/' /etc/ssh/sshd_config
      - sed -i 's/#PasswordAuthentication yes/PasswordAuthentication yes/' /etc/ssh/sshd_config
      - systemctl restart sshd
  storage:
    layout:
      name: lvm
      match:
        path: /dev/nvme0n1
  updates: security
  version: 1
runcmd:
- - eval
  - echo $(cat /proc/cmdline) "autoinstall ds=nocloud-net;" > /root/cmdline
- - eval
  - mount -n --bind -o ro /root/cmdline /proc/cmdline
- - eval
  - snap restart subiquity.subiquity-server
- - eval
  - snap restart subiquity.subiquity-service