#cloud-config
# See the autoinstall documentation at:
# https://canonical-subiquity.readthedocs-hosted.com/en/latest/reference/autoinstall-reference.html
autoinstall:
  apt:
    disable_components: []
    fallback: offline-install
    geoip: true
    mirror-selection:
      primary:
      - country-mirror
      - arches: &id001
        - amd64
        - i386
        uri: http://archive.ubuntu.com/ubuntu/
      - arches: &id002
        - s390x
        - arm64
        - armhf
        - powerpc
        - ppc64el
        - riscv64
        uri: http://ports.ubuntu.com/ubuntu-ports
    preserve_sources_list: false
    security:
    - arches: *id001
      uri: http://security.ubuntu.com/ubuntu/
    - arches: *id002
      uri: http://ports.ubuntu.com/ubuntu-ports
  codecs:
    install: false
  drivers:
    install: false
  identity:
    hostname: node1
    password: $6$SSiixFOTIXKmHB9y$NhnUX.A1uPpdEiv4JUSUlsQ1WgVEm.GTF/7OCAySAhEcZFlIAH07iWDKgta0gRsKf.6d6xmhaAy/duheG5AsF/
    realname: mystic
    username: mystic
  kernel:
    package: linux-generic
  keyboard:
    layout: us
    toggle: null
    variant: ''
  locale: en_US.UTF-8
  network:
    ethernets:
      eno12399np0:
        dhcp4: true
      eno12409np1:
        dhcp4: true
      eno8303:
        dhcp4: true
      eno8403:
        dhcp4: true
    version: 2
  oem:
    install: auto
  source:
    id: ubuntu-server
    search_drivers: false
  ssh:
    allow-pw: true
    authorized-keys: []
    install-server: true
  user-data:
    chpasswd:
      list: |
        root:$6$uchRh6qdZbzFKRKV$HXE3hUl7YtnSNfb/3qJRnCTIX6Mmuv3/sW0ZCZNEgv6Jwnao7OTeET.70ZVMMhDNl9YPWAGLrZG1bhLH5nLvU/
      expire: False
    runcmd: 
      - sed -i 's/#PermitRootLogin prohibit-password/PermitRootLogin yes/' /etc/ssh/sshd_config
      - sed -i 's/#PasswordAuthentication yes/PasswordAuthentication yes/' /etc/ssh/sshd_config
      - systemctl restart sshd
      - ip link add link eno12399np0 dev eno12399.1010 type vlan id 1010
      - ip link set eno12399.1010 up 
      - ip addr add *************/24 dev eno12399.1010
      - ip route add default via ************* dev eno12399.1010
      - echo "DNS=**************" >> /etc/systemd/resolved.conf
      - systemctl restart systemd-resolved.service
  storage:
    layout:
      name: lvm
      match:
        path: /dev/nvme0n1
  updates: security
  version: 1
runcmd:
- - eval
  - echo $(cat /proc/cmdline) "autoinstall ds=nocloud-net;" > /root/cmdline
- - eval
  - mount -n --bind -o ro /root/cmdline /proc/cmdline
- - eval
  - snap restart subiquity.subiquity-server
- - eval
  - snap restart subiquity.subiquity-service