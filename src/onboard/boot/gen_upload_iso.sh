#!/bin/bash

# usage 1 generate and upload all user data:
#     ./gen_upload_iso.sh http://************

# usage 2 generate and upload specific user data:
#     ./gen_upload_iso.sh http://************ ************ ***********

set -e

http_server_addr=$1
shift
idrac_ips="$@"

default_http_server_addr="http://************"
http_server_addr=${http_server_addr:-$default_http_server_addr}

if ! command -v genisoimage &>/dev/null; then
    # If genisoimage doesn't exist, install it
    apt install -y genisoimage
fi

workdir=$(pwd)
autoinstall_config_dir=$(pwd)/autoinstall

userdatas=()
if [ -n "$idrac_ips" ]; then
    for idrac_ip in $idrac_ips; do
        userdatas+=("${autoinstall_config_dir}/user-data-${idrac_ip}")
    done
else
    found=$(find ${autoinstall_config_dir} -type f -name "user-data-*" -exec basename {} \;)
    for element in $found; do
        userdatas+=("$element")
    done
fi

for userdata in "${userdatas[@]}"; do
    idrac_ip=$(echo "$userdata" | grep -oE '[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}')
    # gen iso
    meta_data_file=$autoinstall_config_dir/meta-data
    user_data_file=$autoinstall_config_dir/user-data-${idrac_ip}
    seed_iso_file=$workdir/seed-${idrac_ip}.iso

    if [ ! -f $meta_data_file ]; then
        touch $meta_data_file
    fi
    if [ ! -f $user_data_file ]; then
        echo "NO user data file ${user_data_file}, exit"
        exit
    fi
    if [ -f $seed_iso_file ]; then
        rm $seed_iso_file
    fi

    genisoimage -output "$seed_iso_file" -volid cidata -joliet -rock -graft-points meta-data=$meta_data_file user-data=$user_data_file

    curl -T ${seed_iso_file} ${http_server_addr}/$(basename ${seed_iso_file})
    rm $seed_iso_file

    echo "${http_server_addr}/$(basename ${seed_iso_file}) uploaded."
done

echo "all iso uploaded!!!"
