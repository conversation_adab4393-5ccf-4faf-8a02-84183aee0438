#!/bin/bash
set -ex

if ! command -v livefs-edit &>/dev/null; then
    # If livefs-edit doesn't exist, install it
    git clone https://github.com/mwhudson/livefs-editor
    cd livefs-editor/
    python3 -m pip install .
fi
apt install -y xorriso

src_iso="/home/<USER>/ubuntu-22.04.4-live-server-amd64.iso"

workdir=$(pwd)/autoinstall

datanames=$(find ${workdir} -type f -name "user-data-*" -exec basename {} \;)
for dataname in $datanames; do
    content=$(cat ${workdir}/${dataname})
    hostname=$(echo "$content" | grep -oP 'hostname:\s*\K.*')
    target_iso="ubuntu-${hostname}.iso"
    livefs-edit ${src_iso} ${target_iso} \
        --cp ${workdir}/grub.cfg new/iso/boot/grub/grub.cfg \
        --cp ${workdir}/${dataname} new/iso/server/user-data \
        --cp ${workdir}/meta-data new/iso/server/meta-data
done
