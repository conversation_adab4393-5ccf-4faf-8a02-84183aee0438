#!/bin/bash
set -ex

sudo snap install kubectl --classic
apt install -y python3-pip sshpass
python3 -m pip install jmespath ruamel.yaml netaddr

ansible_pkg="https://files.pythonhosted.org/packages/74/36/36e8ea966064b625b8edc3812a405ba59fe5da490187dd6c28091ec8abfb/ansible_core-2.16.8-py3-none-any.whl"
wget $ansible_pkg
python3 -m pip install $(basename $ansible_pkg)
ansible-galaxy collection install ansible.posix
ansible-galaxy collection install ansible.utils
ansible-galaxy collection install community.general
ansible-galaxy collection install kubernetes.core

kubespray_repo="https://github.com/kubernetes-sigs/kubespray"
rm kubespray/ -r || true
git clone $kubespray_repo

echo "install kubespray env done!!!"
