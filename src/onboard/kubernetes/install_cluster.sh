#!/bin/bash
set -ex

config_abs_path=$1

ENV_FILE="$1/.env"

# Check if the .env file exists
if [ -f "$ENV_FILE" ]; then
    # Export all variables in the .env file to the current shell environment
    export $(grep -v '^#' "$ENV_FILE" | xargs)
else
    echo "Error: .env file not found!"
    exit 1
fi

# Read IPS from .env file
IFS=',' read -r -a IPS <<<"$IPS"
echo "IPS: $IPS"
echo "USERNAME: $USERNAME"
echo "USERPWD: $USERPWD"
echo "ROOTPWD: $ROOTPWD"
echo "LOCAL_CERT_NAME: $LOCAL_CERT_NAME"

if [[ ! -f ~/.ssh/ansible ]]; then
    ssh-keygen -f ~/.ssh/ansible -t ed25519
fi
for ip in "${IPS[@]}"; do
    ssh-keygen -f "$HOME/.ssh/known_hosts" -R "${ip}"
    sshpass -p ${USERPWD} ssh-copy-id -o StrictHostKeyChecking=no -i ~/.ssh/ansible ${USERNAME}@${ip}
    sshpass -p ${ROOTPWD} ssh-copy-id -o StrictHostKeyChecking=no -i ~/.ssh/ansible root@${ip}

    # copy registry cert to node if path and cert name are provided
    if [[ -n "${LOCAL_CERT_NAME}" ]]; then
        sshpass -p ${ROOTPWD} scp -o StrictHostKeyChecking=no $1/${LOCAL_CERT_NAME} root@${ip}:/usr/local/share/ca-certificates/
        sshpass -p ${ROOTPWD} ssh -o StrictHostKeyChecking=no root@${ip} "update-ca-certificates"
    fi

done

cd kubespray
cp -r ${config_abs_path} inventory

# reset nodes
ansible-playbook -i inventory/$(basename ${config_abs_path})/hosts.yaml --become --become-user=root reset.yml --extra-vars 'ansible_sudo_pass=mystic' --extra-vars 'reset_confirmation=yes'
# install cluster
ansible-playbook -i inventory/$(basename ${config_abs_path})/hosts.yaml --become --become-user=root cluster.yml --extra-vars 'ansible_sudo_pass=mystic'

mkdir ~/.kube || true
if [ -f ~/.kube/config ]; then
    rm -rf ~/.kube/config
fi
cp_ip=${IPS[0]}
sshpass -p ${ROOTPWD} scp -o StrictHostKeyChecking=no root@${cp_ip}:/etc/kubernetes/admin.conf ~/.kube/config
sed -i "s^server:.*^server: https://${cp_ip}:6443^" ~/.kube/config
kubectl get nodes

echo "install kubernetes cluster done!!!"

# install nfd and wait for its ready
echo "Installing Node Feature Discovery..."
kubectl apply -k "https://github.com/kubernetes-sigs/node-feature-discovery/deployment/overlays/default?ref=v0.17.0"

echo "Waiting for NFD pods to be ready..."
kubectl wait --for=condition=ready pod -l app=nfd-worker -n node-feature-discovery --timeout=300s

# verify NFD installation
kubectl -n node-feature-discovery get all

# check NFD worker pods status
echo "Checking NFD worker pods status..."
if kubectl get pods -n node-feature-discovery | grep -q "nfd-worker-" && \
   ! kubectl get pods -n node-feature-discovery | grep -q "node-feature-discovery.*0/"; then
    echo "NFD installation completed successfully!"
else
    echo "Error: NFD pods are not running properly!"
    kubectl get pods -n node-feature-discovery
    exit 1
fi