## Values for the external Huawei Cloud Controller
# external_huaweicloud_lbaas_subnet_id: "Neutron subnet ID to create LBaaS VIP"
# external_huaweicloud_lbaas_network_id: "Neutron network ID to create LBaaS VIP"

## Credentials to authenticate against Keystone API
## All of them are required Per default these values will be
## read from the environment.
# external_huaweicloud_auth_url: "{{ lookup('env','OS_AUTH_URL')  }}"
# external_huaweicloud_access_key: "{{ lookup('env','OS_ACCESS_KEY')  }}"
# external_huaweicloud_secret_key: "{{ lookup('env','OS_SECRET_KEY')  }}"
# external_huaweicloud_region: "{{ lookup('env','OS_REGION_NAME')  }}"
# external_huaweicloud_project_id: "{{ lookup('env','OS_TENANT_ID')| default(lookup('env','OS_PROJECT_ID'),true) }}"
# external_huaweicloud_cloud: "{{ lookup('env','OS_CLOUD') }}"

## The repo and tag of the external Huawei Cloud Controller image
# external_huawei_cloud_controller_image_repo: "swr.ap-southeast-1.myhuaweicloud.com"
# external_huawei_cloud_controller_image_tag: "v0.26.8"
