# See roles/network_plugin/kube-router/defaults/main.yml

# Kube router version
# Default to v2
# kube_router_version: "v2.0.0"
# Uncomment to use v1 (Deprecated)
# kube_router_version: "v1.6.0"

# Enables Pod Networking -- Advertises and learns the routes to Pods via iBGP
# kube_router_run_router: true

# Enables Network Policy -- sets up iptables to provide ingress firewall for pods
# kube_router_run_firewall: true

# Enables Service Proxy -- sets up IPVS for Kubernetes Services
# see docs/kube-router.md "Caveats" section
# kube_router_run_service_proxy: false

# Add Cluster IP of the service to the RIB so that it gets advertises to the BGP peers.
# kube_router_advertise_cluster_ip: false

# Add External IP of service to the RIB so that it gets advertised to the BGP peers.
# kube_router_advertise_external_ip: false

# Add LoadBalancer IP of service status as set by the LB provider to the RIB so that it gets advertised to the BGP peers.
# kube_router_advertise_loadbalancer_ip: false

# Enables BGP graceful restarts
# kube_router_bgp_graceful_restart: true

# Adjust manifest of kube-router daemonset template with DSR needed changes
# kube_router_enable_dsr: false

# Array of arbitrary extra arguments to kube-router, see
# https://github.com/cloudnativelabs/kube-router/blob/master/docs/user-guide.md
# kube_router_extra_args: []

# ASN number of the cluster, used when communicating with external BGP routers
# kube_router_cluster_asn: ~

# ASN numbers of the BGP peer to which cluster nodes will advertise cluster ip and node's pod cidr.
# kube_router_peer_router_asns: ~

# The ip address of the external router to which all nodes will peer and advertise the cluster ip and pod cidr's.
# kube_router_peer_router_ips: ~

# The remote port of the external BGP to which all nodes will peer. If not set, default BGP port (179) will be used.
# kube_router_peer_router_ports: ~

# Setups node CNI to allow hairpin mode, requires node reboots, see
# https://github.com/cloudnativelabs/kube-router/blob/master/docs/user-guide.md#hairpin-mode
# kube_router_support_hairpin_mode: false

# Select DNS Policy ClusterFirstWithHostNet, ClusterFirst, etc.
# kube_router_dns_policy: ClusterFirstWithHostNet

# Array of annotations for master
# kube_router_annotations_master: []

# Array of annotations for every node
# kube_router_annotations_node: []

# Array of common annotations for every node
# kube_router_annotations_all: []

# Enables scraping kube-router metrics with Prometheus
# kube_router_enable_metrics: false

# Path to serve Prometheus metrics on
# kube_router_metrics_path: /metrics

# Prometheus metrics port to use
# kube_router_metrics_port: 9255
