all:
  hosts:
    node1:
      ansible_host: *************
      ansible_ssh_private_key_file: ~/.ssh/ansible
      ip: *************
      access_ip: *************
    node2:
      ansible_host: *************
      ansible_ssh_private_key_file: ~/.ssh/ansible
      ip: *************
      access_ip: *************
    node3:
      ansible_host: *************
      ansible_ssh_private_key_file: ~/.ssh/ansible
      ip: *************
      access_ip: *************
    node4:
      ansible_host: *************
      ansible_ssh_private_key_file: ~/.ssh/ansible
      ip: *************
      access_ip: *************
  children:
    kube_control_plane:
      hosts:
        node1:
    kube_node:
      hosts:
        node2:
        node3:
        node4:
    etcd:
      hosts:
        node1:
    k8s_cluster:
      children:
        kube_control_plane:
        kube_node:
    calico_rr:
      hosts: {}
