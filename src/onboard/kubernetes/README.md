# Setup Kubernetes Cluster via Kubespray

## Kubespray env info
ESXi host: ************/root/sds-123  
VM name: ubuntu 22.04 for kubespray  
**VM OS: ************/root/mystic**  

## Install kubespray running env on Ubuntu 22.04
```
./install_env.sh
```

## Install kubernetes cluster with GPU
Prepare kubernetes configuration folder for kubespray and .env file for install_cluster.sh. Run install_cluster.sh with absolute path.
```
./install_cluster /root/h100gpucluster
```

## Install kubernetes cluster with one master node
Prepare kubernetes configuration folder for kubespray and .env file for install_cluster.sh. Run install_cluster.sh with absolute path.
```
./install_cluster /root/h100controlplane
```

## Install kubernetes cluster on L40S
Prepare kubernetes configuration folder for kubespray and .env file for install_cluster.sh. Run install_cluster.sh with absolute path.
```
./install_cluster /root/l40scluster
```