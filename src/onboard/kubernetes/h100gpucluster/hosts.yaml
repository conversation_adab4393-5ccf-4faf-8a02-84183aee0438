all:
  hosts:
    node114:
      ansible_host: ***************
      ansible_ssh_private_key_file: ~/.ssh/ansible
      ip: ***************
      access_ip: ***************
    node115:
      ansible_host: ***************
      ansible_ssh_private_key_file: ~/.ssh/ansible
      ip: ***************
      access_ip: ***************
    node116:
      ansible_host: ***************
      ansible_ssh_private_key_file: ~/.ssh/ansible
      ip: ***************
      access_ip: ***************
    node117:
      ansible_host: ***************
      ansible_ssh_private_key_file: ~/.ssh/ansible
      ip: ***************
      access_ip: ***************
    node118:
      ansible_host: ***************
      ansible_ssh_private_key_file: ~/.ssh/ansible
      ip: ***************
      access_ip: ***************
  children:
    kube_control_plane:
      hosts:
        node114:
    kube_node:
      hosts:
        node114:
        node115:
        node116:
        node117:
        node118:
    etcd:
      hosts:
        node114:
    k8s_cluster:
      children:
        kube_control_plane:
        kube_node:
    calico_rr:
      hosts: {}
