---

# geneve or vlan
kube_ovn_network_type: geneve

# geneve, vxlan or stt. ATTENTION: some networkpolicy cannot take effect when using vxlan and stt need custom compile ovs kernel module
kube_ovn_tunnel_type: geneve

## The nic to support container network can be a nic name or a group of regex separated by comma e.g: 'enp6s0f0,eth.*', if empty will use the nic that the default route use.
# kube_ovn_iface: eth1
## The MTU used by pod iface in overlay networks (default iface MTU - 100)
# kube_ovn_mtu: 1333

## Enable hw-offload, disable traffic mirror and set the iface to the physical port. Make sure that there is an IP address bind to the physical port.
kube_ovn_hw_offload: false
# traffic mirror
kube_ovn_traffic_mirror: false

# kube_ovn_pool_cidr_ipv6: fd85:ee78:d8a6:8607::1:0000/112
# kube_ovn_default_interface_name: eth0

kube_ovn_external_address: *******
kube_ovn_external_address_ipv6: 2400:3200::1
kube_ovn_external_dns: alauda.cn

# kube_ovn_default_gateway: ***********,fd85:ee78:d8a6:8607::1:0
kube_ovn_default_gateway_check: true
kube_ovn_default_logical_gateway: false
# kube_ovn_default_exclude_ips: *********
kube_ovn_node_switch_cidr: **********/16
kube_ovn_node_switch_cidr_ipv6: fd00:100:64::/64

## vlan config, set default interface name and vlan id
# kube_ovn_default_interface_name: eth0
kube_ovn_default_vlan_id: 100
kube_ovn_vlan_name: product

## pod nic type, support: veth-pair or internal-port
kube_ovn_pod_nic_type: veth_pair

## Enable load balancer
kube_ovn_enable_lb: true

## Enable network policy support
kube_ovn_enable_np: true

## Enable external vpc support
kube_ovn_enable_external_vpc: true

## Enable checksum
kube_ovn_encap_checksum: true

## enable ssl
kube_ovn_enable_ssl: false

## dpdk
kube_ovn_dpdk_enabled: false

## enable interconnection to an existing IC database server.
kube_ovn_ic_enable: false
kube_ovn_ic_autoroute: true
kube_ovn_ic_dbhost: "127.0.0.1"
kube_ovn_ic_zone: "kubernetes"
