## When OpenStack is used, Cinder version can be explicitly specified if autodetection fails (Fixed in 1.9: https://github.com/kubernetes/kubernetes/issues/50461)
# openstack_blockstorage_version: "v1/v2/auto (default)"
# openstack_blockstorage_ignore_volume_az: yes
## When OpenStack is used, if LBaaSv2 is available you can enable it with the following 2 variables.
# openstack_lbaas_enabled: True
# openstack_lbaas_subnet_id: "Neutron subnet ID (not network ID) to create LBaaS VIP"
## To enable automatic floating ip provisioning, specify a subnet.
# openstack_lbaas_floating_network_id: "Neutron network ID (not subnet ID) to get floating IP from, disabled by default"
## Override default LBaaS behavior
# openstack_lbaas_use_octavia: False
# openstack_lbaas_method: "ROUND_ROBIN"
# openstack_lbaas_provider: "haproxy"
# openstack_lbaas_create_monitor: "yes"
# openstack_lbaas_monitor_delay: "1m"
# openstack_lbaas_monitor_timeout: "30s"
# openstack_lbaas_monitor_max_retries: "3"

## Values for the external OpenStack Cloud Controller
# external_openstack_lbaas_enabled: true
# external_openstack_lbaas_floating_network_id: "Neutron network ID to get floating IP from"
# external_openstack_lbaas_floating_subnet_id: "Neutron subnet ID to get floating IP from"
# external_openstack_lbaas_method: ROUND_ROBIN
# external_openstack_lbaas_provider: amphora
# external_openstack_lbaas_subnet_id: "Neutron subnet ID to create LBaaS VIP"
# external_openstack_lbaas_network_id: "Neutron network ID to create LBaaS VIP"
# external_openstack_lbaas_manage_security_groups: false
# external_openstack_lbaas_create_monitor: false
# external_openstack_lbaas_monitor_delay: 5s
# external_openstack_lbaas_monitor_max_retries: 1
# external_openstack_lbaas_monitor_timeout: 3s
# external_openstack_lbaas_internal_lb: false
# external_openstack_network_ipv6_disabled: false
# external_openstack_network_internal_networks: []
# external_openstack_network_public_networks: []
# external_openstack_metadata_search_order: "configDrive,metadataService"

## Application credentials to authenticate against Keystone API
## Those settings will take precedence over username and password that might be set your environment
## All of them are required
# external_openstack_application_credential_name:
# external_openstack_application_credential_id:
# external_openstack_application_credential_secret:

## The tag of the external OpenStack Cloud Controller image
# external_openstack_cloud_controller_image_tag: "v1.28.2"

## Tags for the Cinder CSI images
## registry.k8s.io/sig-storage/csi-attacher
# cinder_csi_attacher_image_tag: "v4.4.2"
## registry.k8s.io/sig-storage/csi-provisioner
# cinder_csi_provisioner_image_tag: "v3.6.2"
## registry.k8s.io/sig-storage/csi-snapshotter
# cinder_csi_snapshotter_image_tag: "v6.3.2"
## registry.k8s.io/sig-storage/csi-resizer
# cinder_csi_resizer_image_tag: "v1.9.2"
## registry.k8s.io/sig-storage/livenessprobe
# cinder_csi_livenessprobe_image_tag: "v2.11.0"

## To use Cinder CSI plugin to provision volumes set this value to true
## Make sure to source in the openstack credentials
# cinder_csi_enabled: true
# cinder_csi_controller_replicas: 1
# storage_classes:
#   - name: "cinder-csi"
#     provisioner: "kubernetes.io/cinder"
#     mount_options:
#       - "discard"
#     parameters:
#       type: "thin"
#       availability: "nova"
#     reclaim_policy: "Delete"
#     volume_binding_mode: "WaitForFirstConsumer"
