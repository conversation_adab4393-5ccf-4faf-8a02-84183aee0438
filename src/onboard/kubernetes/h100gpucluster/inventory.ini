# ## Configure 'ip' variable to bind kubernetes services on a
# ## different ip than the default iface
# ## We should set etcd_member_name for etcd cluster. The node that is not a etcd member do not need to set the value, or can set the empty string value.
[all]
# node1 ansible_host=**********  # ip=******** etcd_member_name=etcd1
# node2 ansible_host=**********  # ip=******** etcd_member_name=etcd2
# node3 ansible_host=**********  # ip=******** etcd_member_name=etcd3
# node4 ansible_host=**********  # ip=******** etcd_member_name=etcd4
# node5 ansible_host=**********  # ip=******** etcd_member_name=etcd5
# node6 ansible_host=**********  # ip=******** etcd_member_name=etcd6

# ## configure a bastion host if your nodes are not directly reachable
# [bastion]
# bastion ansible_host=x.x.x.x ansible_user=some_user

[kube_control_plane]
# node1
# node2
# node3

[etcd]
# node1
# node2
# node3

[kube_node]
# node2
# node3
# node4
# node5
# node6
