#!/bin/bash
# Add this to /usr/local/bin/prx, then you call prx as
# `prx 'your question'`
# which gives you an AI answer in markdown in the terminal
# prerequisites:
# 1. curl, jq, glow (If you use macOS you can install by `brew install glow`)
# 2. set OPENAI_API_KEY and OPENAI_API_BASE environment variables

export OPENAI_API_KEY=${OPENAI_API_KEY:-"dummy"} # Set your OPENAI_API_KEY here
export OPENAI_API_BASE=${OPENAI_API_BASE:-"http://100.80.20.5:4000"} # Set your OPENAI_API_BASE here

# Check if environment variables are set
if [[ -z "$OPENAI_API_KEY" ]]; then
  echo "Error: OPENAI_API_KEY environment variable is not set."
  echo "Please set it with: export OPENAI_API_KEY=your_api_key"
  exit 1
fi

# Set default API base URL if not provided
if [[ -z "$OPENAI_API_BASE" ]]; then
  export OPENAI_API_BASE="https://api.openai.com"
fi

# Check and install dependencies
check_and_install_dependencies() {
  # Check for curl
  if ! command -v curl &> /dev/null; then
    echo "curl not found. Installing..."
    sudo apt update && sudo apt install -y curl
  fi

  # Check for jq
  if ! command -v jq &> /dev/null; then
    echo "jq not found. Installing..."
    sudo apt update && sudo apt install -y jq
  fi

  # Check for glow
  if ! command -v glow &> /dev/null; then
    echo "glow not found. Installing..."
    sudo mkdir -p /etc/apt/keyrings
    curl -fsSL https://repo.charm.sh/apt/gpg.key | sudo gpg --dearmor -o /etc/apt/keyrings/charm.gpg
    echo "deb [signed-by=/etc/apt/keyrings/charm.gpg] https://repo.charm.sh/apt/ * *" | sudo tee /etc/apt/sources.list.d/charm.list
    sudo apt update && sudo apt install -y glow
  fi
}

# Run dependency check
check_and_install_dependencies

function prx() {
  if [[ -z "$OPENAI_API_KEY" ]]; then
    echo "Error: OPENAI_API_KEY environment variable is not set."
    echo "Please set it with: export OPENAI_API_KEY=your_api_key"
    return 1
  fi
  
  if [[ -z "$1" ]]; then
    echo "Usage: prx '<your question>'"
    return 1
  fi

  # Define the API URL
  local API_URL="${OPENAI_API_BASE}/v1/chat/completions"
  local QUESTION="$1"
  # The following model is supported by Project x
  # deepseek-R1
  # DeepSeek-V3-0324
  # QwQ-32B
  # Qwen2.5-Coder-32B-Instruct
  local MODEL=${OPENAI_MODEL:-"deepseek-R1"} # Default model if not specified

  # Build JSON payload for OpenAI format
  local PAYLOAD
  PAYLOAD=$(jq -n \
    --arg model "$MODEL" \
    --arg content "$QUESTION" \
    '{
      model: $model,
      messages: [
        {
          role: "user",
          content: $content
        }
      ]
    }'
  )

  # Call the API
  local RESPONSE
  RESPONSE=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer ${OPENAI_API_KEY}" \
    -d "$PAYLOAD" \
    "${API_URL}"
  )

  # Extract the AI answer from OpenAI format
  local ANSWER
  ANSWER=$(printf '%s' "$RESPONSE" | jq -r '.choices[0].message.content')

  if [[ -z "$ANSWER" || "$ANSWER" == "null" ]]; then
    echo "No response or an error occurred."
    echo "$RESPONSE"
    return 1
  fi

  # Wrap in markdown fences
  local WRAPPED
  WRAPPED=$'```markdown\n'"$ANSWER"$'\n```'

  # Render with the best available highlighter
  printf '%s\n' "$WRAPPED" | glow -
}

prx "$@"
