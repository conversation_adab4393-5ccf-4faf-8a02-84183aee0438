# ProjectX CLI

A simple command-line interface for interacting with OpenAI-compatible AI models directly from your terminal.

## Overview

ProjectX CLI is a bash script that allows you to send queries to OpenAI-compatible API endpoints and receive beautifully formatted responses right in your terminal. It handles all the API communication and renders the markdown responses using the `glow` terminal markdown viewer.

![ProjectX CLI Demo](./assets/image.png)

## Features

- Simple, one-command interface to query OpenAI-compatible AI models
- Automatic dependency installation (curl, jq, glow)
- Beautiful markdown rendering in the terminal
- Easy to install and configure
- Environment variable configuration for flexible deployment
- Support for custom API endpoints and models

## Prerequisites

- A Linux/Unix-based system (Ubuntu recommended)
- Sudo privileges (for automatic dependency installation)
- Access to an OpenAI-compatible API endpoint

## Installation

1. Clone this repository or download the `prx` script

2. Make the script executable:
   ```bash
   chmod +x prx
   ```

3. Move the script to a directory in your PATH (optional):
   ```bash
   sudo mv prx /usr/local/bin/
   ```

4. Set up your environment variables:
   ```bash
   export OPENAI_API_KEY="your_api_key_here"
   export OPENAI_API_BASE="https://api.openai.com"  # or your custom API endpoint
   ```

   For permanent setup, add these lines to your `~/.bashrc` or `~/.zshrc` file.

## Environment Variables

The script uses the following environment variables:

- `OPENAI_API_KEY`: Your API key for authentication (required)
- `OPENAI_API_BASE`: The base URL for the API (defaults to "https://api.openai.com" if not set)
- `OPENAI_MODEL`: The model to use (defaults to "deepseek-R1" if not set). The follow models are avaliable:
     * deepseek-R1
     * DeepSeek-V3-0324
     * QwQ-32B
     * Qwen2.5-Coder-32B-Instruct

## Usage

Simply run the script followed by your question in quotes:

```bash
prx "What is the capital of France?"
```

For multi-line questions or complex prompts, you can use:

```bash
prx "Write a Python function that:
1. Takes a list of integers as input
2. Returns the sum of all even numbers in the list
3. Includes proper error handling"
```

## Customization

### Changing the AI Model

You can specify a different model by setting the `OPENAI_MODEL` environment variable:

```bash
export OPENAI_MODEL="deepseek-R1"
prx "Explain quantum computing in simple terms"
```

### Using with Custom API Endpoints

The script works with any OpenAI-compatible API endpoint. Simply set the `OPENAI_API_BASE` environment variable:

```bash
export OPENAI_API_BASE="http://your-custom-endpoint:8000"
prx "Tell me a joke"
```

## Dependencies

The script automatically checks for and installs the following dependencies on Ubuntu systems:

- `curl`: For making API requests
- `jq`: For parsing JSON responses
- `glow`: For rendering markdown in the terminal

## Troubleshooting

### API Key Issues

If you see an error about the API key not being set:
1. Make sure you've set the `OPENAI_API_KEY` environment variable
2. Verify that your API key is valid and has not expired
3. Check that you have not exceeded your API quota

### API Connection Issues

If you're having trouble connecting to the API:
1. Verify that your `OPENAI_API_BASE` is correctly set
2. Check your network connection
3. Ensure the API endpoint is operational

### Dependency Installation Issues

If you encounter problems with automatic dependency installation:
1. Make sure you have sudo privileges
2. Try installing the dependencies manually:
   ```bash
   sudo apt update
   sudo apt install curl jq
   sudo mkdir -p /etc/apt/keyrings
   curl -fsSL https://repo.charm.sh/apt/gpg.key | sudo gpg --dearmor -o /etc/apt/keyrings/charm.gpg
   echo "deb [signed-by=/etc/apt/keyrings/charm.gpg] https://repo.charm.sh/apt/ * *" | sudo tee /etc/apt/sources.list.d/charm.list
   sudo apt update && sudo apt install glow
   ```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- OpenAI and compatible API providers for the underlying AI capabilities
- [Charm's Glow](https://github.com/charmbracelet/glow) for the beautiful markdown rendering
