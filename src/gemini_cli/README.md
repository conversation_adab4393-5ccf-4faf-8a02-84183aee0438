# Gemini CLI

A simple command-line interface for interacting with Google's Gemini AI models directly from your terminal.

## Overview

Gemini CLI is a bash script that allows you to send queries to Google's Gemini AI models and receive beautifully formatted responses right in your terminal. It handles all the API communication and renders the markdown responses using the `glow` terminal markdown viewer.

![Gemini CLI Demo](./assets/image.png)

## Features

- Simple, one-command interface to query Gemini AI models
- Automatic dependency installation (curl, jq, glow)
- Beautiful markdown rendering in the terminal
- Easy to install and configure
- Supports the latest Gemini models

## Prerequisites

- A Linux/Unix-based system (Ubuntu recommended)
- Sudo privileges (for automatic dependency installation)
- A Google AI Studio API key

## Installation

1. Clone this repository or download the `gemini` script

2. Make the script executable:
   ```bash
   chmod +x gemini
   ```

3. Move the script to a directory in your PATH (optional):
   ```bash
   sudo mv gemini /usr/local/bin/
   ```

4. Edit the script to add your Gemini API key:
   ```bash
   vim /usr/local/bin/gemini
   ```
   Replace `YOUR_GEMINI_API_KEY` with your actual API key from Google AI Studio.

## Getting a Gemini API Key

1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Sign in with your Google account
3. Create a new API key
4. Copy the API key and paste it into the script as described in the installation section

## Usage

Simply run the script followed by your question in quotes:

```bash
gemini "What is the capital of France?"
```

For multi-line questions or complex prompts, you can use:

```bash
gemini "Write a Python function that:
1. Takes a list of integers as input
2. Returns the sum of all even numbers in the list
3. Includes proper error handling"
```

## Customization

### Changing the Gemini Model

The script currently uses the `gemini-2.5-flash-preview-04-17` model. You can change this by editing the `API_URL` variable in the script. Available models include:

- `gemini-2.5-flash-preview-04-17` (default)
- `gemini-2.5-pro-preview-03-25`
- `gemini-2.0-flash`

For the latest available models, check the [Gemini API documentation](https://ai.google.dev/gemini-api/docs/models).

## Dependencies

The script automatically checks for and installs the following dependencies on Ubuntu systems:

- `curl`: For making API requests
- `jq`: For parsing JSON responses
- `glow`: For rendering markdown in the terminal

## Troubleshooting

### API Key Issues

If you see an error about the API key not being set:
1. Make sure you've edited the script to include your actual API key
2. Verify that your API key is valid and has not expired
3. Check that you have not exceeded your API quota

### Dependency Installation Issues

If you encounter problems with automatic dependency installation:
1. Make sure you have sudo privileges
2. Try installing the dependencies manually:
   ```bash
   sudo apt update
   sudo apt install curl jq
   sudo mkdir -p /etc/apt/keyrings
   curl -fsSL https://repo.charm.sh/apt/gpg.key | sudo gpg --dearmor -o /etc/apt/keyrings/charm.gpg
   echo "deb [signed-by=/etc/apt/keyrings/charm.gpg] https://repo.charm.sh/apt/ * *" | sudo tee /etc/apt/sources.list.d/charm.list
   sudo apt update && sudo apt install glow
   ```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- [Google Gemini AI](https://ai.google.dev/gemini-api) for providing the API
- [Charm's Glow](https://github.com/charmbracelet/glow) for the beautiful markdown rendering
