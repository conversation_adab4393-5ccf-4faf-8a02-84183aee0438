#!/bin/bash
# Add this to /usr/local/bin/gemini, then you call gemini as
# `gemini 'your question'`
# which gives you an AI answer in markdown in the terminal
# prerequisites:
# 1. curl, jq, glow (If you use macOS you can install by `brew install glow`)
# 2. set your GEMINI_API_KEY below

export GEMINI_API_KEY=YOUR_GEMINI_API_KEY # Set your GEMINI_API_KEY here

# Check if GEMINI_API_KEY is properly set
if [[ "$GEMINI_API_KEY" == "YOUR_GEMINI_API_KEY" ]]; then
  echo "Error: GEMINI_API_KEY is not set. Please edit this script and set your API key."
  exit 1
fi

# Check and install dependencies
check_and_install_dependencies() {
  # Check for curl
  if ! command -v curl &> /dev/null; then
    echo "curl not found. Installing..."
    sudo apt update && sudo apt install -y curl
  fi

  # Check for jq
  if ! command -v jq &> /dev/null; then
    echo "jq not found. Installing..."
    sudo apt update && sudo apt install -y jq
  fi

  # Check for glow
  if ! command -v glow &> /dev/null; then
    echo "glow not found. Installing..."
    sudo mkdir -p /etc/apt/keyrings
    curl -fsSL https://repo.charm.sh/apt/gpg.key | sudo gpg --dearmor -o /etc/apt/keyrings/charm.gpg
    echo "deb [signed-by=/etc/apt/keyrings/charm.gpg] https://repo.charm.sh/apt/ * *" | sudo tee /etc/apt/sources.list.d/charm.list
    sudo apt update && sudo apt install -y glow
  fi

}

# Run dependency check
check_and_install_dependencies


function gemini() {
  if [[ -z "$GEMINI_API_KEY" || "$GEMINI_API_KEY" == "YOUR_GEMINI_API_KEY" ]]; then
    echo "Error: GEMINI_API_KEY is not properly set. Please edit this script and set your API key."
    return 1
  fi
  if [[ -z "$1" ]]; then
    echo "Usage: gemini '<your question>'"
    return 1
  fi

  # Define the API URL
  # you can found models that can be used in the API URL from https://https://ai.google.dev/gemini-api/docs/models
  # e.g. (gemini-2.5-flash-preview-04-17, gemini-2.5-pro-preview-03-25, gemini-2.0-flash)
  local API_URL="https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent"
  local QUESTION="$1"

  # Build JSON payload
  local PAYLOAD
  PAYLOAD=$(jq -n --arg q "$QUESTION" \
    '{ contents: [ { parts: [ { text: $q } ] } ] }'
  )

  # Call the API
  local RESPONSE
  RESPONSE=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -d "$PAYLOAD" \
    "${API_URL}?key=${GEMINI_API_KEY}"
  )

  # Extract the AI answer
  local ANSWER
  ANSWER=$(printf '%s' "$RESPONSE" | jq -r '.candidates[0].content.parts[0].text')

  if [[ -z "$ANSWER" ]]; then
    echo "No response or an error occurred."
    echo "$RESPONSE"
    return 1
  fi

  # Wrap in markdown fences
  local WRAPPED
  WRAPPED=$'```markdown\n'"$ANSWER"$'\n```'

  # Render with the best available highlighter
  printf '%s\n' "$WRAPPED" | glow -
}

gemini "$@"
