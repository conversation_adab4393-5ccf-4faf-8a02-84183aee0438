# Knative Function Examples for Embedding Management and Querying with Qdrant

This repository provides several Knative Function examples demonstrating how to manage embeddings and perform queries against a Qdrant vector database. These examples showcase different approaches, including traditional API calls and the use of LlamaIndex for enhanced functionality and integration with our AI platform.

## Project Structure

The project is organized into the following subdirectories, each containing a distinct Knative Function:
```
├── embed-s3/                   # Traditional API call approach for embedding storage
│   ├── ...                     # Function source code, Dockerfile, etc.
├── embed-s3-llamaindex/        # LlamaIndex integration for embedding storage and querying
│   ├── ...                     # Function source code, Dockerfile, etc.
└── openai-compatible-embed/  # OpenAI-compatible embedding library for LlamaIndex
├── ...                     # Library source code, setup instructions, etc.
```
### 1. `embed-s3/` (Traditional API Approach)

This directory contains a Knative Function that uses traditional API calls to store embeddings in a Qdrant vector database. This approach provides fine-grained control over the embedding storage process.

*   **Key Features:**
    *   Direct interaction with the Qdrant API.
    *   Suitable for users who require precise control over embedding storage.
    *   Serves as a baseline for comparison with the LlamaIndex approach.

### 2. `embed-s3-llamaindex/` (LlamaIndex Integration)

This directory demonstrates the integration of LlamaIndex with our AI platform for embedding storage and querying. LlamaIndex simplifies the process of managing and querying embeddings, providing a higher-level abstraction.

*   **Key Features:**
    *   Leverages LlamaIndex modules for streamlined embedding management.
    *   Enables efficient querying of embeddings stored in Qdrant.
    *   Provides a practical example of integrating LlamaIndex with our AI platform.
    *   Illustrates how to build RAG (Retrieval Augmented Generation) systems.

### 3. `openai-compatible-embed/` (OpenAI-Compatible Embedding Library)

This directory contains a custom LlamaIndex embedding library designed for compatibility with OpenAI's embedding models. As LlamaIndex currently lacks built-in support for direct OpenAI embedding model usage on our AI platform, this library bridges the gap.

*   **Key Features:**
    *   Allows users to seamlessly use OpenAI embedding models within LlamaIndex.
    *   Facilitates the development of LlamaIndex-native RAG systems on our AI platform.
    *   Simplifies the migration and integration of existing OpenAI-based workflows.
    *   Provides clear instructions and examples on how to use the library.

## Purpose

The primary purpose of this repository is to provide a practical sample demonstrating how to integrate LlamaIndex with our AI platform for building efficient and scalable RAG systems. It also showcases alternative methods for embedding management, allowing users to choose the approach that best suits their needs.

## Getting Started (General Guidance)

For each function (in `embed-s3` and `embed-s3-llamaindex`), you will typically find:

*   **Source Code:** The implementation of the Knative Function.
*   **Deployment Instructions:** Instructions on how to deploy the function to a Knative cluster.

For the `openai-compatible-embed` library:

*   **Library Code:** The implementation of the embedding library.
*   **Usage Examples:** Demonstrations of how to use the library within a LlamaIndex application.

Please refer to the individual directory `README` files for more specific instructions and details.
