# OpenAI Compatible Embed

## **Overview**  
This library provides a convenient way to interact with an OpenAI-like embedding service, making it simple to generate embeddings for text. The service is designed to be highly efficient, offering rapid inference through a user-defined model hosting server.

---

## **Usage**

Below is an example that closely follows the usage concepts demonstrated in `func.py`. In this example, we show how to configure and use the library to perform embedding, store data in Qdrant, and even fetch a PDF file from an S3-compatible source.

### Basic Embedding
```python
from openai_compatible_embed import OpenAIEmbedEmbedding

# Initialize the embedding class
embedder = OpenAIEmbedEmbedding(
    model_name="BAAI/bge-m3",
    base_url="http://***************:30125/v1", 
    auth_token="OpenAIEmbed"
)

# Generate a single text embedding
embedding = embedder._get_text_embedding("Sample text to embed")
print("Single Text Embedding:", embedding)

# Generate embeddings for multiple texts
texts = ["First text", "Second text", "Third text"]
embeddings = embedder._get_text_embeddings(texts)
print("Multiple Texts Embeddings:", embeddings)
```
### Batch Embedding
Below is a simple example of how to generate embeddings for multiple text chunks at once using the `OpenAIEmbedEmbedding` class:
```python
from openai_compatible_embed import OpenAIEmbedEmbedding

# Initialize with your model and server details
embed = OpenAIEmbedEmbedding(
    model_name="BAAI/bge-m3",
    base_url="http://***************:30125/v1",
    auth_token="EMBEDDING_AUTH_TOKEN",
)

# Suppose you have a list of text chunks
all_chunks = ["Text chunk 1", "Text chunk 2", "Text chunk 3"]

# Request embeddings in a single batch
embeddings = embed.get_text_embedding_batch(all_chunks)

print("Batch Embeddings:", embeddings)
```
