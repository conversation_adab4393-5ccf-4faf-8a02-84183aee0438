from parliament import Context
from flask import Request
import json
import traceback
import tempfile

import os
import boto3
import logging
from urllib.parse import urlparse
from botocore.exceptions import NoCredentialsError, PartialCredentialsError, ClientError

import qdrant_client
from llama_index.core import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, VectorStoreIndex, Settings
from llama_index.core.storage.storage_context import StorageContext
from llama_index.core.node_parser import SentenceSplitter
from llama_index.vector_stores.qdrant import QdrantVectorStore
from llama_index.llms.openai_like import OpenAILike
from llama_index.core.prompts import PromptTemplate


from openai_compatible_embed import OpenAIEmbedEmbedding


# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)


# Qdrant client configuration (using environment variables for deployment)
QDRANT_HOST = os.environ.get("QDRANT_HOST", "***************")
QDRANT_PORT = int(os.environ.get("QDRANT_PORT", 30333))
COLLECTION_NAME = os.environ.get("COLLECTION_NAME", "llamaindex")

# vLLM configuration
VLLM_HOST = os.environ.get("VLLM_HOST", "***************")
EMBED_PORT = int(os.environ.get("EMBED_PORT", 30125))
LLM_PORT_LARGE = int(os.environ.get("LLM_PORT_LARGE", 30121))
LLM_PORT_MID = int(os.environ.get("LLM_PORT_MID", 30122))

# Embedding configuration
EMBEDDING_MODEL = os.environ.get("EMBEDDING_MODEL", "BAAI/bge-m3")
EMBEDDING_AUTH_TOKEN = os.environ.get("EMBEDDING_AUTH_TOKEN", "OpenAIEmbed")

# LLM configuration
LLM_MODEL_LARGE = os.environ.get("LLM_MODEL_LARGE", "/models/Llama-3.3-70B-Instruct")
LLM_MODEL_MID = os.environ.get("LLM_MODEL_MID", "meta-llama/Llama-3.1-8B-Instruct")
LLM_TEMP = float(os.environ.get("LLM_TEMP", 0.5))
LLM_AUTH_TOKEN = os.environ.get("LLM_AUTH_TOKEN", "OpenAILike")

# Config settings for llama_index
Settings.embed_model = OpenAIEmbedEmbedding(
    model_name=EMBEDDING_MODEL,
    base_url=f"http://{VLLM_HOST}:{EMBED_PORT}/v1",
    auth_token=EMBEDDING_AUTH_TOKEN,
)
Settings.node_parser = SentenceSplitter(chunk_size=1024, chunk_overlap=100)
Settings.transformations = [SentenceSplitter(chunk_size=1024, chunk_overlap=100)]
Settings.llm = OpenAILike(
    model=LLM_MODEL_LARGE,
    api_base=f"http://{VLLM_HOST}:{LLM_PORT_LARGE}/v1",
    api_key=EMBEDDING_AUTH_TOKEN,
    temperature=LLM_TEMP,
    max_tokens=1024,
)

# write prompt template with functions
qa_prompt_tmpl_str = """\
You are a highly knowledgeable and comprehensive assistant, specializing in providing detailed and insightful answers to complex questions. Your goal is to provide the most complete and informative response possible, drawing upon both the provided context and your own extensive knowledge base.

Context Information:
---------------------
{context_str}
---------------------

Instructions:

1.  Carefully analyze the provided context to understand the relevant information.
2.  Answer the following query to the best of your ability, providing a comprehensive and detailed response.
3.  Feel free to supplement the context with your own knowledge where appropriate to provide a more complete answer. If the context is insufficient to fully answer the query, utilize your internal knowledge to fill in the gaps.
4.  Present your answer in a clear and well-structured Markdown format. Use headings, subheadings, lists, tables, code blocks, and other Markdown elements as needed to enhance readability and clarity.
5.  If the context contains information from multiple sources, please cite the relevant source(s) within your answer using appropriate citation methods (e.g., in-text citations, footnotes).

Query: {query_str}

Answer:
"""

qa_prompt_tmpl = PromptTemplate(qa_prompt_tmpl_str)


def process_folder_llamaindex(folder_path):
    """
    Extract text from PDF files in the specified folder, split the text into chunks,
    generate embeddings using OpenAIEmbed, and store the embeddings in Qdrant.

    Args:
        folder_path (str): Path to the folder containing PDF files.

    Returns:
        None

    Raises:
        Exception: If an error occurs while processing the folder.
    """

    try:
        client = qdrant_client.QdrantClient(host=QDRANT_HOST, port=QDRANT_PORT)

        # Create a vector store and index
        vector_store = QdrantVectorStore(client=client, collection_name=COLLECTION_NAME)
        storage_context = StorageContext.from_defaults(vector_store=vector_store)
        documents = SimpleDirectoryReader(folder_path).load_data()
        VectorStoreIndex.from_documents(
            documents, storage_context=storage_context, show_progress=True
        )
        # Query the collection info to verify the upload
        collection_info = client.get_collection(COLLECTION_NAME)
        points_count = collection_info.points_count
        logging.info(f"Collection '{COLLECTION_NAME}' contains {points_count} points")

        logging.info(f"Embeddings uploaded to collection '{COLLECTION_NAME}'.")

    except Exception as e:
        logging.error(f"Error uploading embeddings: {e}")


def query_index(query_text):
    """
    Query the Qdrant index with the specified text.

    Args:
        query_text: Text to use for the query.

    Returns:
        None

    Raises:
        Exception: If an error occurs while querying the index.
    """
    try:

        logging.info(f"Querying index with text: {query_text}")
        client = qdrant_client.QdrantClient(host=QDRANT_HOST, port=QDRANT_PORT)

        # Create a vector store and index
        vector_store = QdrantVectorStore(client=client, collection_name=COLLECTION_NAME)
        index = VectorStoreIndex.from_vector_store(vector_store=vector_store)
        query_engine = index.as_query_engine(
            similarity_top_k=5, verbose=True, streaming=True
        )

        # Update the prompt template
        query_engine.update_prompts(
            {"response_synthesizer:text_qa_template": qa_prompt_tmpl}
        )

        # Query the collection
        response = query_engine.query(query_text)
        response.print_response_stream()
        print("\n")
        logging.info(f"Query successful: {response}")
    except Exception as e:
        logging.error(f"Error querying index: {e}")


def download_s3_file(
    AWS_ACCESS_KEY_ID,
    AWS_SECRET_ACCESS_KEY,
    ENDPOINT_URL,
    BUCKET_NAME,
    object_key,
    local_path,
):
    """
    Download a file from an S3 bucket to a local path.

    Args:
        AWS_ACCESS_KEY_ID: AWS access key ID.
        AWS_SECRET_ACCESS_KEY: AWS secret access key.
        ENDPOINT_URL: S3 endpoint URL.
        BUCKET_NAME: S3 bucket name.
        object_key: S3 object key (file name).
        local_path: Local path to save the downloaded file.

    Returns:
        None

    Raises:
        Exception: If an error occurs while downloading the file.
    """

    try:
        parsed_url = urlparse(ENDPOINT_URL)
        if parsed_url.path and parsed_url.path.strip("/"):
            logging.warning(
                "Endpoint URL seems to already include the bucket name. Using path-style addressing might lead to errors."
            )

        s3 = boto3.client(
            "s3",
            aws_access_key_id=AWS_ACCESS_KEY_ID,
            aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
            endpoint_url=ENDPOINT_URL,
        )

        s3.download_file(BUCKET_NAME, object_key, local_path)
    except NoCredentialsError:
        print("Credentials not available")
    except PartialCredentialsError:
        print("Incomplete credentials provided")
    except ClientError as e:
        if e.response["Error"]["Code"] == "404":
            print(
                f"The object '{object_key}' does not exist in the bucket '{BUCKET_NAME}'"
            )
        else:
            print(f"Client error: {e}")
    except Exception as e:
        print(f"An error occurred: {e}")


def embed_pdf_from_request(request):
    """
    Extract PDF Path, S3 info from Flask request,
    perform embedding and store in Qdrant.

    Args:
        request: Flask request object.

    Returns:
        A string indicating the operation result and HTTP status code.
    """
    try:
        if not request.is_json:
            return "Request body must be in JSON format", 400

        request_json = request.get_json()

        AWS_ACCESS_KEY_ID = request_json["AWS_ACCESS_KEY_ID"]
        AWS_SECRET_ACCESS_KEY = request_json["AWS_SECRET_ACCESS_KEY"]
        ENDPOINT_URL = request_json["ENDPOINT_URL"]
        BUCKET_NAME = request_json["BUCKET_NAME"]
        DOWNLOAD_PATH = request_json["DOWNLOAD_PATH"]
        QUERY_TEXT = request_json["QUERY_TEXT"]

        if not all(
            [AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, ENDPOINT_URL, BUCKET_NAME]
        ):
            logging.error(
                "Please set AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, ENDPOINT_URL, and BUCKET_NAME environment variables."
            )
            exit(1)

        logging.info(
            f"Received PDF filename: {DOWNLOAD_PATH}, S3 bucket: {BUCKET_NAME}"
        )

        with tempfile.TemporaryDirectory() as tmpdirname:
            DOWNLOAD_URI = os.path.join(tmpdirname, DOWNLOAD_PATH)
            logging.info(
                f"Downloading file '{DOWNLOAD_PATH}' from bucket '{BUCKET_NAME}' to '{DOWNLOAD_URI}'..."
            )
            download_s3_file(
                AWS_ACCESS_KEY_ID,
                AWS_SECRET_ACCESS_KEY,
                ENDPOINT_URL,
                BUCKET_NAME,
                DOWNLOAD_PATH,
                DOWNLOAD_URI,
            )
            logging.info(
                f"File '{DOWNLOAD_PATH}' successfully downloaded to '{DOWNLOAD_URI}'"
            )
            logging.info(f"Temporary directory: {tmpdirname}")

            # Do something with the downloaded file here
            process_folder_llamaindex(tmpdirname)

            # Query the index
            query_index(QUERY_TEXT)

            return "PDF successfully embedded and stored in Qdrant\n", 200

    except Exception as e:
        error_message = f"An error occurred while processing the request: {e}\n{traceback.format_exc()}"  # Include traceback info
        logging.exception(error_message)
        return error_message, 500  # Return error message and 500 status code


def main(context: Context):
    """
    Function template
    The context parameter contains the Flask request object and any
    CloudEvent received with the request.
    """

    # Add your business logic here
    print("Received request")

    if "request" in context.keys():
        return embed_pdf_from_request(context.request)
    else:
        print("Empty request", flush=True)
        return "{}", 400
