# Knative Python Function: Embed-S3-LlamaIndex

This Knative function provides:
- Automatic PDF extraction and text splitting.
- Embedding generation using a configured model endpoint.
- Indexing and storage of embeddings in Qdrant.
- Simple query capability against the stored embeddings.

## How it Works
1. Receives POST requests with necessary S3 credentials and file paths.  
2. Downloads the specified PDF from S3.  
3. Processes and embeds the PDF text chunks.  
4. Stores embedded vectors in Qdrant.  
5. Optionally queries the stored vectors using a language model.

## Deployment
- Ensure all required environment variables are set (endpoint URLs, model configs, Qdrant settings).  
- Build and deploy this function on Knative.  
- Invoke using an HTTP POST request with JSON containing S3 and PDF details.  

## Usage
Send a JSON payload including credentials, bucket, and file info to the deployed function’s route. For example:
```bash
curl -X POST \
     -H 'Content-Type: application/json' \
     -d '{
           "AWS_ACCESS_KEY_ID": "K422U1T76BBOV4VYP7H9",
           "AWS_SECRET_ACCESS_KEY": "Wc7GzPRn25DtWn7ANtn7n8TOf1He5fFO2JFCYkhZ",
           "ENDPOINT_URL": "http://192.168.201.116:30480",
           "BUCKET_NAME": "ceph-bkt-x-aed6f4c4-8c33-471b-a691-6ec61a7a6cc5",
           "DOWNLOAD_PATH": "poweredge-xe9680-technical-guide.pdf",
           "QUERY_TEXT": "What kind of GPU does the PowerEdge XE9680 support?"
         }' \
     "http://<your-function-endpoint>"
```
Example output:
```
2024-12-23 04:52:26,525 - INFO - Received PDF filename: poweredge-xe9680-technical-guide.pdf, S3 bucket: ceph-bkt-x-aed6f4c4-8c33-471b-a691-6ec61a7a6cc5
Received request
2024-12-23 04:52:26,526 - INFO - Downloading file 'poweredge-xe9680-technical-guide.pdf' from bucket 'ceph-bkt-x-aed6f4c4-8c33-471b-a691-6ec61a7a6cc5' to '/tmp/tmp9sagsf1d/poweredge-xe9680-technical-guide.pdf'...
2024-12-23 04:52:26,678 - INFO - File 'poweredge-xe9680-technical-guide.pdf' successfully downloaded to '/tmp/tmp9sagsf1d/poweredge-xe9680-technical-guide.pdf'
2024-12-23 04:52:26,679 - INFO - Temporary directory: /tmp/tmp9sagsf1d
2024-12-23 04:52:26,700 - INFO - HTTP Request: GET http://192.168.201.116:30333/collections/llamaindex/exists "HTTP/1.1 200 OK"
Parsing nodes: 100%|██████████| 64/64 [00:00<00:00, 2362.99it/s]
Generating embeddings: 100%|██████████| 64/64 [00:00<00:00, 135.78it/s]
2024-12-23 04:52:29,979 - INFO - HTTP Request: GET http://192.168.201.116:30333/collections/llamaindex/exists "HTTP/1.1 200 OK"
2024-12-23 04:52:29,983 - INFO - HTTP Request: GET http://192.168.201.116:30333/collections/llamaindex "HTTP/1.1 200 OK"
2024-12-23 04:52:30,248 - INFO - HTTP Request: PUT http://192.168.201.116:30333/collections/llamaindex/points?wait=true "HTTP/1.1 200 OK"
2024-12-23 04:52:30,257 - INFO - HTTP Request: GET http://192.168.201.116:30333/collections/llamaindex "HTTP/1.1 200 OK"
2024-12-23 04:52:30,258 - INFO - Collection 'llamaindex' contains 896 points
2024-12-23 04:52:30,258 - INFO - Embeddings uploaded to collection 'llamaindex'.
2024-12-23 04:52:30,259 - INFO - Querying index with text: What kind of GPU does the PowerEdge XE9680 support?
2024-12-23 04:52:30,277 - INFO - HTTP Request: GET http://192.168.201.116:30333/collections/llamaindex/exists "HTTP/1.1 200 OK"
2024-12-23 04:52:30,322 - INFO - HTTP Request: POST http://192.168.201.116:30333/collections/llamaindex/points/search "HTTP/1.1 200 OK"
2024-12-23 04:52:30,359 - INFO - HTTP Request: POST http://192.168.201.116:30121/v1/completions "HTTP/1.1 200 OK"
### GPU Support for PowerEdge XE9680

The provided context does not explicitly mention the type of GPU supported by the PowerEdge XE9680. However, it does mention "Ten HPR GPU fans on the rear of the system" under the Cooling Options section. This implies that the system is designed to accommodate GPUs, but the specific type or model of GPU is not specified.

#### Possible GPU Options

Although the context does not provide direct information on the supported GPU types, we can make some educated guesses based on the system's specifications and the current market trends:

*   **NVIDIA GPUs**: Many modern datacenter and server systems, including those from Dell, support NVIDIA GPUs, such as the NVIDIA A100, A40, or A30. These GPUs are popular for their high performance, power efficiency, and support for various workloads, including AI, HPC, and graphics rendering.
*   **AMD GPUs**: AMD also offers a range of datacenter-grade GPUs, including the AMD Instinct MI8, MI6, and MI50. These GPUs are designed for high-performance computing, AI, and other demanding workloads.
*   **Intel GPUs**: With the introduction of Intel's Xe GPU architecture, Intel is also a player in the datacenter GPU market. Their GPUs, such as the Intel Xe HPG, might be supported by the PowerEdge XE9680, although this is speculative without further information.

#### Conclusion

While the context does not provide explicit information on the supported GPU types for the PowerEdge XE9680, it is likely that the system supports a range of datacenter-grade GPUs from manufacturers like NVIDIA, AMD, or Intel. For accurate and up-to-date information, it is recommended to consult the official Dell documentation or contact their support team.

#### Recommendations

To determine the specific GPU support for the PowerEdge XE9680, follow these steps:

1.  **Check the official Dell documentation**: Visit the Dell website and search for the PowerEdge XE9680 technical guide or datasheet. This should provide detailed information on the system's specifications, including GPU support.
2.  **Contact Dell support**: Reach out to Dell's customer support team for personalized assistance. They can provide information on the specific GPU models supported by the PowerEdge XE9680 and help with any other questions or concerns.
3.  **Consult with a system integrator or reseller**: If you are planning to purchase the PowerEdge XE9680, consider consulting with a system integrator or reseller who can provide guidance on the system's capabilities, including GPU support, and help with configuration and deployment.

```