import unittest
from flask import Flask, request, json
from unittest.mock import patch, MagicMock
import func

class TestEmbedPdfFromRequest(unittest.TestCase):

    @patch('func.download_s3_file')
    @patch('func.process_folder')
    def test_embed_pdf_success(self, mock_process_folder, mock_download_s3_file):
        """
        Test when the request contains all required parameters, the function should execute successfully and return success.
        """
        app = Flask(__name__)
        with app.test_request_context(
            '/',
            method='POST',
            data=json.dumps({
                "AWS_ACCESS_KEY_ID": "K422U1T76BBOV4VYP7H9",
                "AWS_SECRET_ACCESS_KEY": "Wc7GzPRn25DtWn7ANtn7n8TOf1He5fFO2JFCYkhZ",
                "ENDPOINT_URL": "http://192.168.201.116:30480",
                "BUCKET_NAME": "ceph-bkt-x-aed6f4c4-8c33-471b-a691-6ec61a7a6cc5",
                "DOWNLOAD_PATH": "poweredge-xe9680-technical-guide.pdf"
            }),
            content_type='application/json'
        ):
            response, status_code = func.embed_pdf_from_request(request)
            self.assertEqual(status_code, 200)
            self.assertEqual(response, "PDF successfully embedded and stored in Qdrant")
            mock_download_s3_file.assert_called_once()
            mock_process_folder.assert_called_once()

    def test_embed_pdf_invalid_json(self):
        """
        Test when the request body is not in JSON format, the function should return 500.
        """
        app = Flask(__name__)
        with app.test_request_context(
            '/',
            method='POST',
            data="Not a valid JSON string",
            content_type='application/json'
        ):
            response, status_code = func.embed_pdf_from_request(request)
            self.assertEqual(status_code, 500)
            self.assertEqual(response, "Request body must be in JSON format")

    @patch('func.logging.error')
    def test_embed_pdf_missing_fields(self, mock_logging_error):
        """
        Test when required fields are missing, the function should log an error and exit.
        """
        app = Flask(__name__)
        with app.test_request_context(
            '/',
            method='POST',
            data=json.dumps({
                # lack of required fields
                "AWS_ACCESS_KEY_ID": "K422U1T76BBOV4VYP7H9"
            }),
            content_type='application/json'
        ):
            with self.assertRaises(SystemExit):
                func.embed_pdf_from_request(request)
            mock_logging_error.assert_called_once()

if __name__ == "__main__":
    unittest.main()
