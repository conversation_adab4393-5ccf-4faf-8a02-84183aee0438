# Knative Python Function: Embed-S3-LlamaIndex

This Knative function provides:
- Automatic PDF extraction and text splitting.
- Embedding generation using a configured model endpoint.
- Indexing and storage of embeddings in Qdrant.
- Simple query capability against the stored embeddings.

## How it Works
1. Receives POST requests with necessary S3 credentials and file paths.  
2. Downloads the specified PDF from S3.  
3. Processes and embeds the PDF text chunks.  
4. Stores embedded vectors in Qdrant.  


## Deployment
- Ensure all required environment variables are set (endpoint URLs, model configs, Qdrant settings).  
- Build and deploy this function on Knative.  
- Invoke using an HTTP POST request with JSON containing S3 and PDF details.  

## Usage
Send a JSON payload including credentials, bucket, and file info to the deployed function’s route. For example:
```bash
curl -X POST \
     -H 'Content-Type: application/json' \
     -d '{
           "AWS_ACCESS_KEY_ID": "K422U1T76BBOV4VYP7H9",
           "AWS_SECRET_ACCESS_KEY": "Wc7GzPRn25DtWn7ANtn7n8TOf1He5fFO2JFCYkhZ",
           "ENDPOINT_URL": "http://***************:30480",
           "BUCKET_NAME": "ceph-bkt-x-aed6f4c4-8c33-471b-a691-6ec61a7a6cc5",
           "DOWNLOAD_PATH": "poweredge-xe9680-technical-guide.pdf"
         }' \
     "http://<your-function-endpoint>"
```
Example output:
```
2024-12-23 05:28:38,761 - INFO - Received PDF filename: poweredge-xe9680-technical-guide.pdf, S3 bucket: ceph-bkt-x-aed6f4c4-8c33-471b-a691-6ec61a7a6cc5
2024-12-23 05:28:38,762 - INFO - Downloading file 'poweredge-xe9680-technical-guide.pdf' from bucket 'ceph-bkt-x-aed6f4c4-8c33-471b-a691-6ec61a7a6cc5' to '/tmp/tmpljx2s_22/poweredge-xe9680-technical-guide.pdf'...
2024-12-23 05:28:39,180 - INFO - File 'poweredge-xe9680-technical-guide.pdf' successfully downloaded to '/tmp/tmpljx2s_22/poweredge-xe9680-technical-guide.pdf'
2024-12-23 05:28:43,312 - INFO - HTTP Request: GET http://***************:30333/collections/pdf_embeddings "HTTP/1.1 200 OK"
2024-12-23 05:28:43,324 - INFO - Collection 'pdf_embeddings' already exists, skipping creation.
2024-12-23 05:28:43,442 - INFO - HTTP Request: PUT http://***************:30333/collections/pdf_embeddings/points?wait=true "HTTP/1.1 200 OK"
2024-12-23 05:28:43,447 - INFO - Embeddings uploaded to collection 'pdf_embeddings'.
```
