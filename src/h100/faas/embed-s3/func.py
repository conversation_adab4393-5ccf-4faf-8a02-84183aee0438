from parliament import Context
from flask import Request
import json
import traceback
import tempfile

import os
import boto3
import logging
from urllib.parse import urlparse
from botocore.exceptions import NoCredentialsError, PartialCredentialsError, ClientError

from llama_index.core import SimpleDirectoryReader
import qdrant_client
from llama_index.core.node_parser import TokenTextSplitter
from qdrant_client.models import Distance, VectorParams
from qdrant_client.http.models import PointStruct, Batch

from openai_compatible_embed import OpenAIEmbedEmbedding


# Configure logging
LOGLVELE = os.environ.get("LOGLEVEL", "INFO")
if LOGLVELE == "DEBUG":
    level = logging.DEBUG
else:
    level = logging.INFO
logging.basicConfig(level=level, format="%(asctime)s - %(levelname)s - %(message)s")

# Qdrant client configuration (using environment variables for deployment)
QDRANT_HOST = os.environ.get("QDRANT_HOST", "***************")
QDRANT_PORT = int(os.environ.get("QDRANT_PORT", 30333))
COLLECTION_NAME = os.environ.get("COLLECTION_NAME", "pdf_embeddings")

# vLLM configuration
VLLM_HOST = os.environ.get("VLLM_HOST", "***************")
VLLM_PORT = int(os.environ.get("VLLM_PORT", 30125))

# Embedding configuration
EMBEDDING_MODEL = os.environ.get("EMBEDDING_MODEL", "BAAI/bge-m3")
EMBEDDING_AUTH_TOKEN = os.environ.get("EMBEDDING_AUTH_TOKEN", "OpenAIEmbed")


def process_folder(folder_path):
    """
    Extract text from PDF files in the specified folder,
    split the text into chunks, generate embeddings using OpenAIEmbed,
    and store the embeddings in Qdrant.

    Args:
        folder_path (str): Path to the folder containing PDF files.

    Returns:
        None

    Raises:
        Exception: If an error occurs while processing the folder.
    """
    client = qdrant_client.QdrantClient(host=QDRANT_HOST, port=QDRANT_PORT)

    # Load documents from the folder
    documents = SimpleDirectoryReader(folder_path).load_data()

    # Configure text splitter, chunk size and overlap
    text_splitter = TokenTextSplitter(chunk_size=500, chunk_overlap=100)

    # Split text into chunks
    all_chunks = []
    for doc in documents:
        chunks = text_splitter.split_text(doc.text)
        for chunk in chunks:
            all_chunks.append(chunk)

    # Initialize OpenAIEmbed to get embeddings
    embed = OpenAIEmbedEmbedding(
        model_name=EMBEDDING_MODEL,
        base_url=f"http://{VLLM_HOST}:{VLLM_PORT}/v1",
        auth_token="EMBEDDING_AUTH_TOKEN",
    )

    # Get embeddings for all chunks
    embeddings = embed.get_text_embedding_batch(all_chunks)

    # Create a collection in Qdrant and upload embeddings
    try:
        client.get_collection(COLLECTION_NAME)
        logging.info(
            f"Collection '{COLLECTION_NAME}' already exists, skipping creation."
        )

    except Exception:
        logging.info(f"Creating collection '{COLLECTION_NAME}'...")

        # Create a collection with cosine distance
        vector_size = 1024
        client.create_collection(
            collection_name=COLLECTION_NAME,
            vectors_config=VectorParams(size=vector_size, distance=Distance.COSINE),
        )
        logging.info(f"Collection '{COLLECTION_NAME}' created successfully.")

    # Upload embeddings to the collection
    try:
        points = []
        for i, embedding in enumerate(embeddings):
            points.append(
                PointStruct(id=i, vector=embedding, payload={"text": all_chunks[i]})
            )

        client.upsert(collection_name=COLLECTION_NAME, wait=True, points=points)
        logging.info(f"Embeddings uploaded to collection '{COLLECTION_NAME}'.")

    except Exception as e:
        logging.error(f"Error uploading embeddings: {e}")


def download_s3_file(
    AWS_ACCESS_KEY_ID,
    AWS_SECRET_ACCESS_KEY,
    ENDPOINT_URL,
    BUCKET_NAME,
    object_key,
    local_path,
):
    """
    Download a file from an S3 bucket using the provided credentials.

    Args:
        AWS_ACCESS_KEY_ID (str): AWS
        AWS_SECRET_ACCESS_KEY (str): AWS secret access key.
        ENDPOINT_URL (str): The endpoint URL of the S3 service.
        BUCKET_NAME (str): The name of the S3 bucket.
        object_key (str): The key of the object to download.
        local_path (str): The local path to save the downloaded file.

    Returns:
        None

    Raises:
        Exception: If an error occurs while downloading the file.
    """

    try:
        parsed_url = urlparse(ENDPOINT_URL)
        if parsed_url.path and parsed_url.path.strip("/"):
            logging.warning(
                "Endpoint URL seems to already include the bucket name. Using path-style addressing might lead to errors."
            )

        s3 = boto3.client(
            "s3",
            aws_access_key_id=AWS_ACCESS_KEY_ID,
            aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
            endpoint_url=ENDPOINT_URL,
        )
        s3.download_file(BUCKET_NAME, object_key, local_path)

    except NoCredentialsError:
        print("Credentials not available")
    except PartialCredentialsError:
        print("Incomplete credentials provided")
    except ClientError as e:
        if e.response["Error"]["Code"] == "404":
            print(
                f"The object '{object_key}' does not exist in the bucket '{BUCKET_NAME}'"
            )
        else:
            print(f"Client error: {e}")
    except Exception as e:
        print(f"An error occurred: {e}")


def embed_pdf_from_request(request):
    """
    Extract PDF Path, S3 info from Flask request,
    perform embedding and store in Qdrant.

    Args:
        request: Flask request object.

    Returns:
        A string indicating the operation result and HTTP status code.

    Raises:
        Exception: If an error occurs while processing the request.
    """
    try:
        if not request.is_json:
            return "Request body must be in JSON format", 400

        request_json = request.get_json()

        AWS_ACCESS_KEY_ID = request_json["AWS_ACCESS_KEY_ID"]
        AWS_SECRET_ACCESS_KEY = request_json["AWS_SECRET_ACCESS_KEY"]
        ENDPOINT_URL = request_json["ENDPOINT_URL"]
        BUCKET_NAME = request_json["BUCKET_NAME"]
        DOWNLOAD_PATH = request_json["DOWNLOAD_PATH"]

        if not all(
            [AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, ENDPOINT_URL, BUCKET_NAME]
        ):
            logging.error(
                "Please set AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, ENDPOINT_URL, and BUCKET_NAME environment variables."
            )
            exit(1)

        logging.info(
            f"Received PDF filename: {DOWNLOAD_PATH}, S3 bucket: {BUCKET_NAME}"
        )

        with tempfile.TemporaryDirectory() as tmpdirname:
            DOWNLOAD_URI = os.path.join(tmpdirname, DOWNLOAD_PATH)
            logging.info(
                f"Downloading file '{DOWNLOAD_PATH}' from bucket '{BUCKET_NAME}' to '{DOWNLOAD_URI}'..."
            )
            download_s3_file(
                AWS_ACCESS_KEY_ID,
                AWS_SECRET_ACCESS_KEY,
                ENDPOINT_URL,
                BUCKET_NAME,
                DOWNLOAD_PATH,
                DOWNLOAD_URI,
            )
            logging.info(
                f"File '{DOWNLOAD_PATH}' successfully downloaded to '{DOWNLOAD_URI}'"
            )
            # Do something with the downloaded file here
            process_folder(tmpdirname)
            return "PDF successfully embedded and stored in Qdrant\n", 200

    except Exception as e:
        error_message = f"An error occurred while processing the request: {e}\n{traceback.format_exc()}"  # Include traceback info
        logging.exception(error_message)
        return error_message, 500  # Return error message and 500 status code


def main(context: Context):
    """
    Function template
    The context parameter contains the Flask request object and any
    CloudEvent received with the request.
    """

    # Add your business logic here
    print("Received request")

    if "request" in context.keys():
        return embed_pdf_from_request(context.request)
    else:
        print("Empty request", flush=True)
        return "{}", 500
