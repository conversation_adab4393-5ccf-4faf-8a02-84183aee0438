# Install docker registry in Kubernetes

## Install
Generate htpasswd for docker registry, paste the value to corresponding field in *registry-values.yaml* file later.
```
apt update
apt install docker.io -y
docker run --entrypoint htpasswd registry:2.7.0 -Bbn admin Password123! > ./registry-htpasswd
```

Generate key and certificate for docker registry. (Need to provide SANs)
```
export FQDN='registry.projectx.eypoc.com'

openssl genrsa -out registry-tls.key 4096

openssl req -new -sha256 \
	-key registry-tls.key \
	-out registry-tls.csr \
	-subj "/C=US/ST=California/L=San Francisco/O=Dell/CN=$FQDN" \
	-reqexts SAN \
	-config <(printf "\n[SAN]\nsubjectAltName=DNS:$FQDN")

openssl x509 -req \
	-in registry-tls.csr \
	-signkey registry-tls.key \
	-out registry-tls.crt \
	-days 3650 \
	-extensions req_ext -extfile <(echo "[req]"; echo distinguished_name=req_distinguished_name; echo req_extensions=req_ext; echo "[req_distinguished_name]"; echo "[req_ext]"; echo subjectAltName=DNS:$FQDN)
```

Create secret for key and certificate
```
kubectl create ns registry
kubectl create secret tls registry-tls --cert=registry-tls.crt --key=registry-tls.key -n registry
```

Update fields in registry-values.yaml
```
htpasswd: |
    admin:$2y$05$T1.FnjZmePhd.xFCl334fOJPn4oQAKJ8aHxEsmMAK1N.7CYt9lZJK
```
```
hosts:
    - registry.projectx.eypoc.com
```
```
  tls:
    # Secrets must be manually created in the namespace.
    - secretName: registry-tls
      hosts:
        - registry.projectx.eypoc.com
```

Install docker registry in kubernetes
```
helm repo add twuni https://helm.twun.io
helm repo update
helm install registry twuni/docker-registry --namespace registry --create-namespace --values registry-values.yaml
```

## Usage
### Outside kubernetes cluster
```
export FQDN='registry.projectx.eypoc.com'
echo "*************** $FQDN" >> /etc/hosts
mkdir -p /etc/docker/certs.d/$FQDN/
cp registry-tls.crt /etc/docker/certs.d/$FQDN/ca.crt
systemctl restart docker.service
docker login -uadmin -pPassword123! https://$FQDN/
docker push $FQDN/busybox:latest
```

### Inside kubernetes cluster
Edit the CoreDNS and NodeLocalDNS ConfigMap, add "*************** registry.projectx.eypoc.com".  
Refer to [DNS configuration](https://eos2git.cec.lab.emc.com/wux13/x/blob/phase3/src/h100/INSTALLATION.md#coredns-configuration)

## Uninstall
```
helm uninstall registry -n registry
kubectl delete secret registry-tls -n registry
```