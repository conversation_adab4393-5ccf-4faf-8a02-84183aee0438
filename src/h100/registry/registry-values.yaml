persistence:
  enabled: true
  accessMode: ReadWriteOnce
  size: 10Gi
  storageClass: ""

service:
  type: ClusterIP
  port: 5000

secrets:
  # admin:Password123!
  htpasswd: |
    admin:$2y$05$T1.FnjZmePhd.xFCl334fOJPn4oQAKJ8aHxEsmMAK1N.7CYt9lZJK
ingress:
  enabled: true
  className: nginx
  path: /
  # Used to create an Ingress record.
  hosts:
    - registry.projectx.eypoc.com
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: "0"
    nginx.ingress.kubernetes.io/proxy-request-buffering: "off"
  tls:
    # Secrets must be manually created in the namespace.
    - secretName: registry-tls
      hosts:
        - registry.projectx.eypoc.com
