# Installation
* Step1: Install Operator
    ```
    helm repo add cnpg https://cloudnative-pg.github.io/charts
    helm upgrade --install cnpg \
        --namespace cnpg-system \
        --create-namespace \
        cnpg/cloudnative-pg
    ```

* Step2: Create Database Cluster
    ```
    helm repo add cnpg https://cloudnative-pg.github.io/charts
    helm upgrade --install database \
        --namespace database \
        --create-namespace \
        cnpg/cluster
    ```

* Step3: Create Pooler

    ```
    apiVersion: postgresql.cnpg.io/v1
    kind: Pooler
    metadata:
        name: pooler-rw
        namespace: database
    spec:
        cluster:
            name: database-cluster
        instances: 3
        type: rw
        pgbouncer:
            poolMode: session
            parameters:
                max_client_conn: "1000"
                default_pool_size: "10"
    ```
