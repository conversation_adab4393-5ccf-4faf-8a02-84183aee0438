Before following this document to start the stack installation, ensure that the Kubernetes cluster is ready.
Use the following commands to check the status of the cluster:
```
kubectl get node
kubectl get pod -A
``` 

# Install Helm
```
curl -fsSL -o get_helm.sh https://raw.githubusercontent.com/helm/helm/master/scripts/get-helm-3 \
&& chmod 700 get_helm.sh \
&& ./get_helm.sh
```

# Install NVIDIA Driver
```
helm repo add nvidia https://helm.ngc.nvidia.com/nvidia \
    && helm repo update
helm install --wait --generate-name \
    -n nvidia-gpu-operator --create-namespace \
    nvidia/gpu-operator \
    --version=v24.9.1
```
## TODO: Check installation complete

# Install Ceph
## Clear disks
On each worker node, run this command to clear disks. Change */dev/sd{a..f}* to your disks.
```
lsblk
for device in /dev/sd{a..f}; do sgdisk --zap-all $device; blkdiscard $device; partprobe $device; done
```
## Install Ceph
```

git clone --depth 1 --single-branch --branch release-1.15 https://github.com/rook/rook.git
cd rook/deploy/examples
kubectl create -f crds.yaml -f common.yaml -f operator.yaml
kubectl create -f cluster.yaml
kubectl apply -f filesystem.yaml
kbuectl apply -f csi/cephfs/storageclass.yaml
kubectl patch storageclass rook-cephfs -p '{"metadata": {"annotations":{"storageclass.kubernetes.io/is-default-class":"true"}}}'
```
## Create bucket
Create `object-store.yaml`
```yaml
apiVersion: ceph.rook.io/v1
kind: CephObjectStore
metadata:
  name: store-x
  namespace: rook-ceph
spec:
  metadataPool:
    failureDomain: osd
    replicated:
      size: 3
  dataPool:
    failureDomain: osd
    erasureCoded:
      dataChunks: 4
      codingChunks: 2
  preservePoolsOnDelete: true
  gateway:
    port: 80
    instances: 1
```
Create `storageclass-bucket.yaml`
```yaml
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: rook-ceph-bucket-x
provisioner: rook-ceph.ceph.rook.io/bucket # driver:namespace:cluster
reclaimPolicy: Delete
parameters:
  objectStoreName: store-x
  objectStoreNamespace: rook-ceph # namespace:cluster
```
Create `bucket-claim.yaml`
```yaml
apiVersion: objectbucket.io/v1alpha1
kind: ObjectBucketClaim
metadata:
  name: ceph-bucket-x
  namespace: rook-ceph
spec:
  generateBucketName: ceph-bkt-x
  storageClassName: rook-ceph-bucket-x
```
Create `object-store-x-nodeport.yaml`
```yaml
apiVersion: v1
kind: Service
metadata:
  name: rook-ceph-rgw-store-x-nodeport
  namespace: rook-ceph
spec:
  selector:
    app: rook-ceph-rgw
    ceph_daemon_id: store-x
    rgw: store-x
    rook_cluster: rook-ceph
    rook_object_store: store-x
  type: NodePort
  ports:
    - port: 80
      targetPort: 8080
      protocol: TCP
      nodePort: 30480

```
Apply the YAML files:
```
kubectl apply -f object-store.yaml
kubectl apply -f storageclass-bucket.yaml
kubectl apply -f bucket-claim.yaml
kubectl apply -f object-store-x-nodeport.yaml
```

# Install MetalLB
## Installation
```
helm repo add metallb https://metallb.github.io/metallb
helm repo update
kubectl create namespace metallb-system
helm install metallb metallb/metallb -n metallb-system
```
## Configuration
Configure IPAddressPool according to your network environment.
Make sure the ingress IP can reach cluster.
```bash
export INGRESS_IP_START=*************
export INGRESS_IP_END=*************
export EGRESS_INTERFACE=eno12399.1010

envsubst <<"EOF" | kubectl apply -f -
apiVersion: metallb.io/v1beta1
kind: IPAddressPool
metadata:
    name: ingress-public-ip
    namespace: metallb-system
spec:
    addresses:
    - ${INGRESS_IP_START}-${INGRESS_IP_END}
---
apiVersion: metallb.io/v1beta1
kind: L2Advertisement
metadata:
    name: ingress-public-ip
    namespace: metallb-system
spec:
    interfaces:
    - ${EGRESS_INTERFACE}
    ipAddressPools:
    - ingress-public-ip
EOF
```

# Install Ingress-Nginx
```
helm upgrade --install ingress-nginx ingress-nginx \
    --repo https://kubernetes.github.io/ingress-nginx \
    --namespace ingress-nginx --create-namespace
```

# DNS configuration
If there is a DNS server, add 2 A record to point these 2 domains:
- oauth2-proxy.projectx.eypoc.com
- sso.projectx.eypoc.com

to IP address `*************`

In this documentation, we use a jumphost as proxy, then we need manually configure the hosts in jumphost and CoreDNS configuration in k8s
## /etc/hosts on the jumphost
```
echo "************* sso.projectx.eypoc.com oauth2-proxy.projectx.eypoc.com" >> /etc/hosts
```
## CoreDNS configuration
Edit the CoreDNS ConfigMap:
```
kubectl edit cm -n kube-system coredns
```
The ConfigMap should be modified like this:
```yaml
apiVersion: v1
data:
  Corefile: |
    .:53 {
        errors {
        }
        health {
            lameduck 5s
        }
        ready
        kubernetes cluster.local in-addr.arpa ip6.arpa {
          pods insecure
          fallthrough in-addr.arpa ip6.arpa
        }
        hosts custom.hosts sso.projectx.eypoc.com {
          ************* sso.projectx.eypoc.com
          ************* oauth2-proxy.projectx.eypoc.com
          ************* master01.projectx.eypoc.com
          ************* knative.projectx.eypoc.com
          fallthrough
        }

        prometheus :9153
        forward . *********** {
          prefer_udp
          max_concurrent 1000
        }
        cache 30

        loop
        reload
        loadbalance
    }
kind: ConfigMap
metadata:
  name: coredns
  namespace: kube-system
```
Edit the NodeLocalDNS ConfigMap:
```
kubectl edit cm -n kube-system nodelocaldns
```
The ConfigMap should be modified like this:
```yaml
apiVersion: v1
data:
  Corefile: |
    cluster.local:53 {
        errors
        cache {
            success 9984 30
            denial 9984 5
        }
        reload
        loop
        bind *************
        forward . ********** {
            force_tcp
        }
        prometheus :9253
        health *************:9254
    }
    in-addr.arpa:53 {
        errors
        cache 30
        reload
        loop
        bind *************
        forward . ********** {
            force_tcp
        }
        prometheus :9253
    }
    ip6.arpa:53 {
        errors
        cache 30
        reload
        loop
        bind *************
        forward . ********** {
            force_tcp
        }
        prometheus :9253
    }
    .:53 {
        errors
        cache 30
        reload
        loop
        bind *************
        hosts custom.hosts sso.projectx.eypoc.com {
          ************* sso.projectx.eypoc.com
          ************* oauth2-proxy.projectx.eypoc.com
          ************* node1.projectx.eypoc.com
          ************* knative.projectx.eypoc.com
          fallthrough
        }
        forward . /etc/resolv.conf
        prometheus :9253
    }
kind: ConfigMap
metadata:
  name: nodelocaldns
  namespace: kube-system
```

Restart the CoreDNS related pod to activate above DNS configuration by deleteing them
```
kubectl get pod -A|grep -E 'coredns|localdns'
```
It will output like below:
```
kube-system           coredns-69d6675447-brrb6                                          1/1     Running     0             10m
kube-system           coredns-69d6675447-rbxtq                                          1/1     Running     0             10m
kube-system           nodelocaldns-2gztn                                                1/1     Running     0             10m
kube-system           nodelocaldns-2nsmn                                                1/1     Running     0             10m
kube-system           nodelocaldns-49zmz                                                1/1     Running     0             10m
kube-system           nodelocaldns-t7vqq                                                1/1     Running     0             10m
```
Delete all these pod to let them restart(modify pods name according to the output):
```
kubectl delete pod -n kube-system coredns-69d6675447-brrb6 coredns-69d6675447-rbxtq nodelocaldns-2gztn nodelocaldns-2nsmn nodelocaldns-49zmz nodelocaldns-t7vqq
```
Wait for these pods restart
```
kubectl get pod -A|grep -E 'coredns|localdns'
```


# Install Keycloak
Prepare `keycloak-values.yml`
```yaml
# Run in production mode behind NGINX proxy terminating TLS sessions
production: true
# ref: https://www.keycloak.org/server/reverseproxy
proxyHeaders: xforwarded

# Admin user
auth:
  adminUser: admin
# postgresSQL
#postgresql:
#  enabled: true
#  auth:
#    username: keycloak
#    database: keycloak
# Ingress config
ingress:
  enabled: true
  ingressClassName: "nginx"
  pathType: Prefix
  annotations:
    nginx.ingress.kubernetes.io/proxy-buffers-number: "4"
    nginx.ingress.kubernetes.io/proxy-buffer-size: "16k"
  hostname: sso.projectx.eypoc.com
  tls: true
  extraTls:
    - hosts:
        - sso.projectx.eypoc.com
```

```bash
helm repo add bitnami https://charts.bitnami.com/bitnami
helm repo update
kubectl create namespace keycloak
helm install keycloak bitnami/keycloak -f keycloak-values.yml --namespace keycloak
# Check status of Keycloak pods
kubectl get pods -n keycloak
# Check Keycloak ingress
kubectl get ingress keycloak -n keycloak -o yaml
```
The Keycloak ingress ingress should be:
```yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: keycloak
  namespace: keycloak
spec:
  ingressClassName: nginx
  rules:
  - host: sso.projectx.eypoc.com
    http:
      paths:
      - backend:
          service:
            name: keycloak
            port:
              name: http
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - sso.projectx.eypoc.com
```
Get keycloak `admin` user password
```
kubectl get secret keycloak -o jsonpath='{.data.admin-password}' -n keycloak | base64 -d && echo
```
Login Keycloak console sso.projectx.eypoc.com as admin and password obtained above to configure

## Configuration

* Step 1: Login as admin to Keycloak console

    Open URL: https://sso.projectx.eypoc.com, 
* Step 2: Create a new realm `projectx`
    ![realm](./keycloak/assets/client0.png)
    ![realm](./keycloak/assets/realm.png)

* Step 3: Configure Oauth2-Proxy Client
    
    ![client](./keycloak/assets/client.png)

    Click `Create`

    ![client2](./keycloak/assets/client2.png)
    
    Enable `Client authentication` and tick `Standard flow`
    
    ![client3](./keycloak/assets/client3.png)
    Provide the following ‘Logging settings’
    Valid redirect URIs: https://oauth2-proxy.projectx.eypoc.com/oauth2/callback and save the configuration.


    ![client4](./keycloak/assets/client4.png)

    Create oauth2-proxy client credentials. Under the Credentials tab you will now be able to locate oauth2-proxy client’s secret.


    ![client5](./keycloak/assets/client5.png)

    - Configure a dedicated audience mapper for the client.Navigate to Clients -> oauth2-proxy client -> Client scopes.
    
    - Access the dedicated mappers pane by clicking ‘oauth2-proxy-dedicated’, located under Assigned client scope. (It should have a description of “Dedicated scope and mappers for this client”)

    - Click on ‘Configure a new mapper’ and select ‘Audience’
    
    - Provide following data:
        * Name ‘aud-mapper-oauth2-proxy’
        * Included Client Audience select oauth2-proxy client’s id from the dropdown.
        * Add to ID token ‘On’
        * Add to access token ‘On’ OAuth2 proxy can be set up to pass both the access and ID JWT tokens to your upstream services.
    - Save the configuration.

    ![user](./keycloak/assets/user1.png)
    Click `Add User`

    ![user2](./keycloak/assets/user2.png)
    
    Set a username and Click `Create`

    ![user3](./keycloak/assets/user3.png)
    Click `Set Password`

    ![user4](./keycloak/assets/user4.png)
    Set a password and unselect `Temporary`, then click `Save`

# Install oauth2-proxy
Copy the `Client Secret` from keycloak.
![client](./oauth2-proxy/assets/client4.png)
Create `values.yml`, Replace `<client-secret>` in the secret with `Client Secret`

```yaml
config:
  # Add config annotations
  annotations: {}
  # OAuth client ID
  # Follow instructions to configure Keycloak client
  # https://oauth2-proxy.github.io/oauth2-proxy/configuration/providers/keycloak_oidc

  # Oauth2 client configuration. From Keycloak configuration
  clientID: "oauth2-proxy"
  clientSecret: "<client-secret>"

  # Cookie secret
  # Create a new secret with the following command
  # openssl rand -base64 32 | head -c 32 | base64
  cookieSecret: "bG5pRDBvL0VaWis3dksrZ05vYnJLclRFb2VNcVZJYkg="
  # The name of the cookie that oauth2-proxy will create
  # If left empty, it will default to the release name
  cookieName: "oauth2-proxy"

  # Config file
  configFile: |-
    # Provider config
    provider="keycloak-oidc"
    provider_display_name="Keycloak"
    redirect_url="https://oauth2-proxy.projectx.eypoc.com/oauth2/callback"
    oidc_issuer_url="https://sso.projectx.eypoc.com/realms/projectx"
    code_challenge_method="S256"
    ssl_insecure_skip_verify=true
    # Upstream config
    http_address="0.0.0.0:4180"
    upstreams="file:///dev/null"
    email_domains=["*"]
    cookie_domains=["projectx.eypoc.com"]
    cookie_secure=false
    scope="openid"
    whitelist_domains=[".projectx.eypoc.com"]
    insecure_oidc_allow_unverified_email="true"
    # Header config
    pass_user_headers=true
    set_xauthrequest=true
    set_authorization_header=true
    pass_access_token=true
    pass_authorization_header=true
    #prefer_email_to_user=true
    reverse_proxy=true

sessionStorage:
  # Can be one of the supported session storage cookie|redis
  type: redis
# Enabling redis backend installation
redis:
  enabled: true
  # standalone redis. No cluster
  architecture: standalone

ingress:
  enabled: true
  className: "nginx"
  pathType: Prefix
  path: /oauth2
  annotations:
    # Enable cert-manager to create automatically the SSL certificate and store in Secret
    # Possible Cluster-Issuer values:
    #   * 'letsencrypt-issuer' (valid TLS certificate using IONOS API)
    #   * 'ca-issuer' (CA-signed certificate, not valid)
    nginx.ingress.kubernetes.io/proxy-buffer-size: "16k"
  hosts:
    - oauth2-proxy.projectx.eypoc.com
  tls:
    - hosts:
        - oauth2-proxy.projectx.eypoc.com
```

```bash
helm repo add oauth2-proxy https://oauth2-proxy.github.io/manifests
helm repo update
kubectl create namespace oauth2-proxy
helm install oauth2-proxy oauth2-proxy/oauth2-proxy -f values.yml --namespace oauth2-proxy
# Check status oauth2-proxy PODs
kubectl --namespace=oauth2-proxy get pods -l "app=oauth2-proxy"
```

# Install PostgreSQL
```
helm install postgresql oci://registry-1.docker.io/bitnamicharts/postgresql --namespace postgresql --create-namespace --set global.postgresql.auth.postgresPassword=mystic
```

# Install Qdrant
```
helm repo add qdrant https://qdrant.to/helm
helm install qdrant qdrant/qdrant --namespace qdrant --create-namespace
```

# Install RabbitMQ
```
helm install rabbitmq oci://registry-1.docker.io/bitnamicharts/rabbitmq --namespace rabbitmq --create-namespace --set auth.password=mystic
```


# Install vLLM (Models Serving)
## Create Hugging Face Secret
Create `hg-secret.yaml`, replacing with your own Hugging Face API token(need base64 encoded: `echo -n hf_xxx|base64`):
```yaml
apiVersion: v1
data:
  hf_api_token: ****************************************************
kind: Secret
metadata:
  name: hf-secret
  namespace: default
type: Opaque
```
Apply the secret:
```bash
kubectl apply -f hg-secret.yaml
```
## Deploy Llama-3.1-8B
Create `llm-pvc.yaml`
```yaml
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: llama-3-1
  namespace: default
spec:
  accessModes:
  - ReadWriteMany
  resources:
    requests:
      storage: 50Gi
  volumeMode: Filesystem

```
Create `llm-deployment.yaml`
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: llama3-1
  name: llama3-1
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: llama3-1
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: llama3-1
    spec:
      containers:
      - args:
        - vllm serve meta-llama/Llama-3.1-8B-Instruct --trust-remote-code --enable-chunked-prefill
          --max_num_batched_tokens 1024
        command:
        - /bin/sh
        - -c
        env:
        - name: HUGGING_FACE_HUB_TOKEN
          valueFrom:
            secretKeyRef:
              key: hf_api_token
              name: hf-secret
        image: vllm/vllm-openai:latest
        imagePullPolicy: Always
        name: llama
        ports:
        - containerPort: 8000
          protocol: TCP
        resources:
          limits:
            cpu: "16"
            nvidia.com/gpu: "1"
          requests:
            cpu: "6"
            memory: 6G
            nvidia.com/gpu: "1"
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /root/.cache/huggingface
          name: cache-volume
        - mountPath: /dev/shm
          name: shm
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
      volumes:
      - name: cache-volume
        persistentVolumeClaim:
          claimName: llama-3-1
      - emptyDir:
          medium: Memory
        name: shm
```
Create `llm-svc.yaml`
```yaml
apiVersion: v1
kind: Service
metadata:
  labels:
    app: llama3-1
  name: llama3-1
  namespace: default
spec:
  ports:
  - name: vllm
    nodePort: 30122
    port: 8000
    protocol: TCP
    targetPort: 8000
  selector:
    app: llama3-1
  type: NodePort

```
Apply the YAML files:
```bash
kubectl apply -f llm-pvc.yaml
kubectl apply -f llm-deployment.yaml
kubectl apply -f llm-svc.yaml
```
## deploy bge-m3
Create `embedding-pvc.yaml`
```yaml
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: bge-m3
  namespace: default
spec:
  accessModes:
  - ReadWriteMany
  resources:
    requests:
      storage: 5Gi
  volumeMode: Filesystem
```
Create `embedding-deployment.yaml`
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: bge-m3
  namespace: default
  labels:
    app: bge-m3
spec:
  replicas: 1
  selector:
    matchLabels:
      app: bge-m3
  template:
    metadata:
      labels:
        app: bge-m3
    spec:
      volumes:
      - name: cache-volume
        persistentVolumeClaim:
          claimName: bge-m3
      # vLLM needs to access the host's shared memory for tensor parallel inference.
      - name: shm
        emptyDir:
          medium: Memory
      containers:
      - name: llama
        image: vllm/vllm-openai:latest
        command: ["/bin/sh", "-c"]
        args: [
          "vllm serve BAAI/bge-m3 --trust-remote-code --task embedding"
        ]
        env:
        - name: HUGGING_FACE_HUB_TOKEN
          valueFrom:
            secretKeyRef:
              name: hf-secret
              key: hf_api_token
        ports:
        - containerPort: 8000
        resources:
          limits:
            cpu: "16"
            nvidia.com/gpu: "1"
          requests:
            cpu: "6"
            memory: 6G
            nvidia.com/gpu: "1"
        volumeMounts:
        - mountPath: /root/.cache/huggingface
          name: cache-volume
        - name: shm
          mountPath: /dev/shm
```
Create `embedding-svc.yaml`
```yaml
apiVersion: v1
kind: Service
metadata:
  name: bge-m3
  namespace: default
spec:
  ports:
  - nodePort: 30125
    port: 8000
    protocol: TCP
    targetPort: 8000
  selector:
    app: bge-m3
  type: NodePort
```
Apply the YAML files:
```bash
kubectl apply -f embedding-pvc.yaml
kubectl apply -f embedding-deployment.yaml
kubectl apply -f embedding-svc.yaml
```

# TODO:Install monitor
## Promethous
## Grafana
## Dashboard configuration

# TODO:Config docker registry

# Next Step
Follow GEETING-STARTED.md to use this stack to deploy an RAG application