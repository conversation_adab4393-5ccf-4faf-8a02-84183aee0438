{"__inputs": [{"name": "DS_PROMETHEUS", "label": "Prometheus", "description": "", "type": "datasource", "pluginId": "prometheus", "pluginName": "Prometheus"}], "__elements": {}, "__requires": [{"type": "panel", "id": "gauge", "name": "Gauge", "version": ""}, {"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "11.3.1"}, {"type": "panel", "id": "heatmap", "name": "Heatmap", "version": ""}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "1.0.0"}, {"type": "panel", "id": "stat", "name": "Stat", "version": ""}, {"type": "panel", "id": "table", "name": "Table", "version": ""}, {"type": "panel", "id": "timeseries", "name": "Time series", "version": ""}], "annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "showIn": 0, "tags": [], "type": "dashboard"}]}, "description": "Overview of your Ceph cluster.", "editable": false, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 2, "panels": [], "title": "CLUSTER STATE", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"decimals": 0, "links": [], "mappings": [{"id": 0, "options": {"0": {"text": "HEALTHY"}, "1": {"text": "WARNING"}, "2": {"text": "ERROR"}}, "type": "value"}, {"id": 1, "options": {"result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#9ac48a", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 1}, {"color": "rgba(245, 54, 54, 0.9)", "value": 2}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 0, "y": 1}, "id": 3, "interval": "1m", "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "ceph_health_status{}", "format": "time_series", "instant": true, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A", "step": 300}], "title": "Ceph health status", "transparent": true, "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"decimals": 1, "links": [], "mappings": [{"id": 0, "options": {"result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "Bps"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 3, "y": 1}, "id": 4, "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum(irate(ceph_osd_op_w_in_bytes{}[5m]))", "format": "time_series", "instant": true, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Write Throughput", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"decimals": 1, "links": [], "mappings": [{"id": 0, "options": {"result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#d44a3a", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 0}, {"color": "#9ac48a", "value": 0}]}, "unit": "Bps"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 6, "y": 1}, "id": 5, "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum(irate(ceph_osd_op_r_out_bytes{}[5m]))", "format": "time_series", "instant": true, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Read Throughput", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"decimals": 2, "links": [], "mappings": [{"id": 0, "options": {"result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 0.025}, {"color": "rgba(245, 54, 54, 0.9)", "value": 1}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 9, "y": 1}, "id": 6, "interval": "1m", "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "ceph_cluster_total_bytes{}", "format": "time_series", "instant": true, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A", "step": 300}], "title": "Cluster Capacity", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"links": [], "mappings": [{"id": 0, "options": {"result": {"text": "N/A"}}, "type": "special"}], "max": 1, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(245, 54, 54, 0.9)", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 0.1}, {"color": "rgba(50, 172, 45, 0.97)", "value": 0.3}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 6, "w": 3, "x": 12, "y": 1}, "id": 7, "interval": "1m", "maxDataPoints": 100, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "sizing": "auto"}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "(ceph_cluster_total_bytes{}-ceph_cluster_total_used_bytes{})/ceph_cluster_total_bytes{}", "format": "time_series", "instant": true, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A", "step": 300}], "title": "Available Capacity", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"decimals": 2, "links": [], "mappings": [{"id": 0, "options": {"result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 15, "y": 1}, "id": 8, "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum(ceph_pool_objects{})", "format": "time_series", "instant": true, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Number of Objects", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"decimals": 1, "links": [], "mappings": [{"id": 0, "options": {"result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 18, "y": 1}, "id": 9, "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["delta"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum(ceph_osd_op_w_in_bytes{})", "format": "time_series", "instant": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Bytes Written", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"decimals": 1, "links": [], "mappings": [{"id": 0, "options": {"result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 21, "y": 1}, "id": 10, "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["delta"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum(ceph_osd_op_r_out_bytes{})", "format": "time_series", "instant": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "<PERSON><PERSON>", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"decimals": 0, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "#9ac48a", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 1}, {"color": "#e24d42", "value": 1}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 0, "y": 4}, "id": 11, "maxDataPoints": 100, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "count(ALERTS{alertstate=\"firing\",alertname=~\"^Ceph.+\"}) OR vector(0)", "format": "time_series", "instant": true, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "<PERSON><PERSON>s starting with <PERSON><PERSON>", "transparent": true, "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"decimals": 0, "links": [], "mappings": [{"id": 0, "options": {"result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "ops"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 3, "y": 4}, "id": 12, "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum(irate(ceph_osd_op_w{}[5m]))", "format": "time_series", "instant": true, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Write IOPS", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"decimals": 0, "links": [], "mappings": [{"id": 0, "options": {"result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#d44a3a", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 0}, {"color": "#9ac48a", "value": 0}]}, "unit": "ops"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 6, "y": 4}, "id": 13, "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum(irate(ceph_osd_op_r{}[5m]))", "format": "time_series", "instant": true, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Read IOPS", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"decimals": 2, "links": [], "mappings": [{"id": 0, "options": {"result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 0.025}, {"color": "rgba(245, 54, 54, 0.9)", "value": 0.1}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 9, "y": 4}, "id": 14, "interval": "1m", "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "ceph_cluster_total_used_bytes{}", "format": "time_series", "instant": true, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Used Capacity", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"decimals": 2, "links": [], "mappings": [{"id": 0, "options": {"result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#ea6460", "value": null}, {"color": "#052b51", "value": 0}, {"color": "#508642", "value": 0}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 15, "y": 4}, "id": 15, "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["diff"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum(ceph_pool_objects)", "format": "time_series", "instant": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Difference", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"decimals": 0, "links": [], "mappings": [{"id": 0, "options": {"result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 128}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 18, "y": 4}, "id": 16, "maxDataPoints": 100, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum(ceph_mon_num_sessions{})", "format": "time_series", "instant": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Mon Session Num", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"decimals": 0, "links": [], "mappings": [{"id": 0, "options": {"result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(245, 54, 54, 0.9)", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 2}, {"color": "green", "value": 3}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 21, "y": 4}, "id": 17, "interval": "1m", "maxDataPoints": 100, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "count(ceph_mon_quorum_status{}) or vector(0)", "format": "time_series", "instant": true, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A", "step": 300}], "title": "Monitors In Quorum", "type": "stat"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 7}, "id": 18, "panels": [], "title": "OSD STATE", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"decimals": 0, "links": [], "mappings": [{"id": 0, "options": {"result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#9ac48a", "value": null}, {"color": "rgba(237, 40, 40, 0.89)", "value": 1}, {"color": "rgba(245, 54, 54, 0.9)", "value": 1}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 2, "x": 0, "y": 8}, "id": 19, "maxDataPoints": 100, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "count(ceph_osd_up{}) - count(ceph_osd_in{})", "format": "time_series", "instant": true, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A", "step": 300}], "title": "OSDs OUT", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"decimals": 0, "links": [], "mappings": [{"id": 0, "options": {"result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)", "value": null}, {"color": "#eab839", "value": 1}, {"color": "#ea6460", "value": 1}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 2, "x": 2, "y": 8}, "id": 20, "maxDataPoints": 100, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "count(ceph_osd_up{} == 0.0) OR vector(0)", "format": "time_series", "instant": true, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A", "step": 300}], "title": "OSDs DOWN", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"decimals": 0, "links": [], "mappings": [{"id": 0, "options": {"result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 2, "x": 4, "y": 8}, "id": 21, "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum(ceph_osd_up{})", "format": "time_series", "instant": true, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A", "step": 300}], "title": "OSDs UP", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"decimals": 0, "links": [], "mappings": [{"id": 0, "options": {"result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 2, "x": 6, "y": 8}, "id": 22, "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum(ceph_osd_in{})", "format": "time_series", "instant": true, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A", "step": 300}], "title": "OSDs IN", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"decimals": 1, "links": [], "mappings": [{"id": 0, "options": {"result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 250}, {"color": "rgba(245, 54, 54, 0.9)", "value": 300}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 2, "x": 8, "y": 8}, "id": 23, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "avg(ceph_osd_numpg{})", "format": "time_series", "instant": true, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A", "step": 300}], "title": "Avg PGs", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"decimals": 2, "links": [], "mappings": [{"id": 0, "options": {"result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 10}, {"color": "rgba(245, 54, 54, 0.9)", "value": 50}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 10, "y": 8}, "id": 24, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "avg(ceph_osd_apply_latency_ms{})", "format": "time_series", "instant": true, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A", "step": 300}], "title": "Avg Apply Latency", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"decimals": 2, "links": [], "mappings": [{"id": 0, "options": {"result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 10}, {"color": "rgba(245, 54, 54, 0.9)", "value": 50}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 13, "y": 8}, "id": 25, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "avg(ceph_osd_commit_latency_ms{})", "format": "time_series", "instant": true, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A", "step": 300}], "title": "Avg Commit Latency", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"decimals": 4, "links": [], "mappings": [{"id": 0, "options": {"result": {"color": "#299c46", "text": "0"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#299c46", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 1}, {"color": "#d44a3a", "value": 2}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 16, "y": 8}, "id": 26, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "avg(rate(ceph_osd_op_w_latency_sum{}[5m]) / rate(ceph_osd_op_w_latency_count{}[5m]) >= 0)", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Avg Op Write Latency", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"decimals": 6, "links": [], "mappings": [{"id": 0, "options": {"result": {"color": "#299c46", "text": "0"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#299c46", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 1}, {"color": "#d44a3a", "value": 2}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 19, "y": 8}, "id": 27, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "avg(rate(ceph_osd_op_r_latency_sum{}[5m])/rate(ceph_osd_op_r_latency_count{}[5m]) >= 0)", "format": "time_series", "instant": true, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Avg Op Read Latency", "type": "stat"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 11}, "id": 28, "panels": [], "title": "CLUSTER STATS", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 40, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 0, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 2, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Available"}, "properties": [{"id": "color", "value": {"fixedColor": "#EAB839", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Total Capacity"}, "properties": [{"id": "color", "value": {"fixedColor": "#447EBC", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Used"}, "properties": [{"id": "color", "value": {"fixedColor": "#BF1B00", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Used"}, "properties": [{"id": "color", "value": {"fixedColor": "#BF1B00", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "total_avail"}, "properties": [{"id": "color", "value": {"fixedColor": "#6ED0E0", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "total_space"}, "properties": [{"id": "color", "value": {"fixedColor": "#7EB26D", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "total_used"}, "properties": [{"id": "color", "value": {"fixedColor": "#890F02", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Total Capacity"}, "properties": [{"id": "custom.fillOpacity", "value": 0}, {"id": "custom.lineWidth", "value": 3}, {"id": "custom.stacking", "value": {"group": false, "mode": "normal"}}]}]}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 12}, "id": 29, "interval": "$interval", "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "ceph_cluster_total_bytes{}-ceph_cluster_total_used_bytes{}", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Available", "refId": "A", "step": 300}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "ceph_cluster_total_used_bytes{}", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Used", "refId": "B", "step": 300}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "ceph_cluster_total_bytes{}", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Total Capacity", "refId": "C", "step": 300}], "title": "Capacity", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Total Capacity"}, "properties": [{"id": "color", "value": {"fixedColor": "#7EB26D", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Used"}, "properties": [{"id": "color", "value": {"fixedColor": "#BF1B00", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "total_avail"}, "properties": [{"id": "color", "value": {"fixedColor": "#6ED0E0", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "total_space"}, "properties": [{"id": "color", "value": {"fixedColor": "#7EB26D", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "total_used"}, "properties": [{"id": "color", "value": {"fixedColor": "#890F02", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 12}, "id": 30, "interval": "$interval", "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum(irate(ceph_osd_op_w{}[5m]))", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Write", "refId": "A", "step": 300}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum(irate(ceph_osd_op_r{}[5m]))", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Read", "refId": "B", "step": 300}], "title": "IOPS", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 12}, "id": 31, "interval": "$interval", "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum(irate(ceph_osd_op_w_in_bytes{}[5m]))", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Write", "refId": "A", "step": 300}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum(irate(ceph_osd_op_r_out_bytes{}[5m]))", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Read", "refId": "B", "step": 300}], "title": "Cluster Throughput", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 20}, "id": 32, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "(ceph_pool_bytes_used{}) *on (pool_id) group_left(name)(ceph_pool_metadata{})", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{name}}", "refId": "A", "step": 300}], "title": "Pool Used Bytes", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 20}, "id": 33, "options": {"legend": {"calcs": [], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "(ceph_pool_avail_raw{}) *on (pool_id) group_left(name)(ceph_pool_metadata{})", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{name}} Avail", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "(ceph_pool_stored_raw{}) *on (pool_id) group_left(name)(ceph_pool_metadata{})", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{name}} Stored", "range": true, "refId": "B"}], "title": "Pool RAW Bytes", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 20}, "id": 34, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "(ceph_pool_objects{}) *on (pool_id) group_left(name)(ceph_pool_metadata{})", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{name}}", "refId": "A"}], "title": "Objects Per Pool", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 28}, "id": 35, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.1.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "(ceph_pool_quota_bytes{}) *on (pool_id) group_left(name)(ceph_pool_metadata{})", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{name}}", "refId": "A"}], "title": "Pool Quota Bytes", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 28}, "id": 36, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.1.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "(ceph_pool_quota_objects{}) *on (pool_id) group_left(name)(ceph_pool_metadata{})", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{name}}", "refId": "A"}], "title": "Pool Objects Quota", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 28}, "id": 37, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.1.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "count(ceph_bluestore_kv_commit_lat_count{})", "format": "time_series", "intervalFactor": 1, "legendFormat": "BlueStore", "range": true, "refId": "A"}], "title": "OSD Type Count", "type": "timeseries"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 35}, "id": 38, "panels": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "decimals": 2, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Time"}, "properties": [{"id": "displayName", "value": "Time"}, {"id": "unit", "value": "time: YYYY-MM-DD HH:mm:ss"}, {"id": "custom.align"}]}]}, "gridPos": {"h": 6, "w": 8, "x": 0, "y": 28}, "id": 39, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "9.1.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "ALERTS{alertstate=\"firing\",alertname=~\"^Ceph.+\"}", "format": "table", "instant": true, "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "<PERSON><PERSON>s starting with <PERSON><PERSON>", "transformations": [{"id": "merge", "options": {"reducer": []}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "filterable": false, "inspect": false}, "decimals": 2, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Time"}, "properties": [{"id": "displayName", "value": "Time"}, {"id": "unit", "value": "time: YYYY-MM-DD HH:mm:ss"}, {"id": "custom.align"}]}]}, "gridPos": {"h": 6, "w": 8, "x": 8, "y": 28}, "id": 40, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "9.1.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "topk(5,sort_desc(ceph_osd_apply_latency_ms{} + ceph_osd_commit_latency_ms{}))", "format": "table", "instant": true, "intervalFactor": 1, "legendFormat": "_auto", "refId": "A"}], "title": "Top Sluggish OSDs", "transformations": [{"id": "merge", "options": {"reducer": []}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "decimals": 2, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Time"}, "properties": [{"id": "displayName", "value": "Time"}, {"id": "unit", "value": "time: YYYY-MM-DD HH:mm:ss"}, {"id": "custom.align"}]}]}, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 28}, "id": 41, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "9.1.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "ceph_osd_up{} == 0", "format": "table", "instant": true, "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Down OSDs", "transformations": [{"id": "merge", "options": {"reducer": []}}], "type": "table"}], "title": "<PERSON><PERSON><PERSON>", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 36}, "id": 42, "panels": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"scaleDistributionLog": 2, "type": "log"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 29}, "id": 43, "options": {"legend": {"calcs": ["mean", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.1.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "node_memory_Active_anon_bytes{}", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum(node_memory_Active_anon_bytes{})", "format": "time_series", "intervalFactor": 1, "legendFormat": "Cluster Memory Usage", "refId": "B"}], "title": "Node Memory Usage", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"scaleDistributionLog": 2, "type": "log"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 29}, "id": 44, "options": {"legend": {"calcs": ["lastNotNull", "max"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.1.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "avg by(instance)(irate(node_cpu_seconds_total{job='node',mode!=\"idle\"}[$interval])) * 100", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A"}], "title": "Node CPU Usage", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 35}, "id": 45, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.1.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum by (instance)(irate(node_disk_read_bytes_total{}[$interval]))", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A"}], "title": "Node Out", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 35}, "id": 46, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.1.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum by (instance)(irate(node_disk_written_bytes_total{}[$interval]))", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A"}], "title": "Node In", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 44}, "id": 47, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.1.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "(node_filesystem_free_bytes{ mountpoint=\"/\", device != \"rootfs\"})*100 / (node_filesystem_size_bytes{ mountpoint=\"/\", device != \"rootfs\"})", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}", "range": true, "refId": "A"}], "title": "Free Space in root filesystem", "type": "timeseries"}], "title": "Node Statistics (NodeExporter)", "type": "row"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 37}, "id": 48, "panels": [], "title": "OBJECTS", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/^Total.*$/"}, "properties": [{"id": "custom.stacking", "value": {"group": false, "mode": "normal"}}]}]}, "gridPos": {"h": 12, "w": 6, "x": 0, "y": 38}, "id": 49, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "asc"}}, "pluginVersion": "9.1.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum(ceph_pool_objects)", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Total", "range": true, "refId": "A", "step": 300}], "title": "OSD Type Count", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/^Total.*$/"}, "properties": [{"id": "custom.stacking", "value": {"group": false, "mode": "normal"}}]}]}, "gridPos": {"h": 12, "w": 8, "x": 6, "y": 38}, "id": 50, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "asc"}}, "pluginVersion": "9.1.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum(ceph_pg_active{})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Active", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum(ceph_pg_clean{})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Clean", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum(ceph_pg_peering{})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Peering", "range": true, "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum(ceph_pg_degraded{})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Degraded", "range": true, "refId": "D", "step": 300}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum(ceph_pg_stale{})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Stale", "range": true, "refId": "E", "step": 300}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum(ceph_unclean_pgs{})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "<PERSON><PERSON>", "range": true, "refId": "F", "step": 300}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum(ceph_pg_undersized{})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Undersized", "range": true, "refId": "G", "step": 300}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum(ceph_pg_incomplete{})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Incomplete", "range": true, "refId": "H"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum(ceph_pg_forced_backfill{})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Forced Backfill", "range": true, "refId": "I"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum(ceph_pg_forced_recovery{})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Forced Recovery", "range": true, "refId": "J"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum(ceph_pg_creating{})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Creating", "range": true, "refId": "K"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum(ceph_pg_wait_backfill{})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Wait Backfill", "range": true, "refId": "L"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum(ceph_pg_deep{})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Deep", "range": true, "refId": "M"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum(ceph_pg_scrubbing{})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Scrubbing", "range": true, "refId": "N"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum(ceph_pg_recovering{})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Recovering", "range": true, "refId": "O"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum(ceph_pg_repair{})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Repair", "range": true, "refId": "P"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum(ceph_pg_down{})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Down", "range": true, "refId": "Q"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum(ceph_pg_peered{})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Peered", "range": true, "refId": "R"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum(ceph_pg_backfill{})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Backfill", "range": true, "refId": "S"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum(ceph_pg_remapped{})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Remapped", "range": true, "refId": "T"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum(ceph_pg_backfill_toofull{})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Backfill Toofull", "range": true, "refId": "U"}], "title": "PGs State", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/^Total.*$/"}, "properties": [{"id": "custom.stacking", "value": {"group": false, "mode": "normal"}}]}]}, "gridPos": {"h": 6, "w": 10, "x": 14, "y": 38}, "id": 51, "options": {"legend": {"calcs": ["mean", "lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "asc"}}, "pluginVersion": "9.1.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum(ceph_pg_degraded{})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Degraded", "range": true, "refId": "A", "step": 300}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum(ceph_pg_stale{})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Stale", "range": true, "refId": "B", "step": 300}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum(ceph_pg_undersized{})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Undersized", "range": true, "refId": "C", "step": 300}], "title": "Stuck PGs", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 6, "w": 10, "x": 14, "y": 44}, "id": 52, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.1.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum(irate(ceph_osd_recovery_ops{}[$interval]))", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "OPS", "refId": "A", "step": 300}], "title": "Recovery Operations", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 50}, "id": 53, "panels": [], "title": "LATENCY", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}, "scaleDistribution": {"type": "linear"}}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 51}, "id": 54, "options": {"calculate": true, "calculation": {"yBuckets": {"mode": "count", "scale": {"log": 2, "type": "log"}, "value": "1"}}, "cellGap": 2, "cellValues": {}, "color": {"exponent": 0.5, "fill": "#b4ff00", "mode": "opacity", "scale": "exponential", "scheme": "Oranges", "steps": 128}, "exemplars": {"color": "rgba(255,0,255,0.7)"}, "filterValues": {"le": 1e-09}, "legend": {"show": true}, "rowsFrame": {"layout": "auto"}, "showValue": "never", "tooltip": {"show": true, "yHistogram": false}, "yAxis": {"axisPlacement": "left", "min": "0", "reverse": false, "unit": "ms"}}, "pluginVersion": "9.1.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "ceph_osd_apply_latency_ms{}", "format": "time_series", "instant": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "OSD Apply Latency Distribution", "type": "heatmap"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}, "scaleDistribution": {"type": "linear"}}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 51}, "id": 55, "options": {"calculate": true, "calculation": {"yBuckets": {"mode": "count", "scale": {"log": 2, "type": "log"}}}, "cellGap": 2, "cellValues": {}, "color": {"exponent": 0.5, "fill": "#65c5db", "mode": "opacity", "scale": "exponential", "scheme": "Oranges", "steps": 128}, "exemplars": {"color": "rgba(255,0,255,0.7)"}, "filterValues": {"le": 1e-09}, "legend": {"show": true}, "rowsFrame": {"layout": "auto"}, "showValue": "never", "tooltip": {"show": true, "yHistogram": false}, "yAxis": {"axisPlacement": "left", "min": "0", "reverse": false, "unit": "ms"}}, "pluginVersion": "9.1.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "ceph_osd_commit_latency_ms{}", "format": "time_series", "instant": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "OSD Commit Latency Distribution", "type": "heatmap"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}, "scaleDistribution": {"type": "linear"}}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 59}, "id": 56, "options": {"calculate": true, "calculation": {"yBuckets": {"mode": "count", "scale": {"log": 2, "type": "log"}}}, "cellGap": 2, "cellValues": {}, "color": {"exponent": 0.5, "fill": "#806eb7", "mode": "opacity", "scale": "exponential", "scheme": "Oranges", "steps": 128}, "exemplars": {"color": "rgba(255,0,255,0.7)"}, "filterValues": {"le": 1e-09}, "legend": {"show": true}, "rowsFrame": {"layout": "auto"}, "showValue": "never", "tooltip": {"show": true, "yHistogram": false}, "yAxis": {"axisPlacement": "left", "decimals": 2, "min": "0", "reverse": false, "unit": "ms"}}, "pluginVersion": "9.1.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "rate(ceph_osd_op_r_latency_sum{}[5m]) / rate(ceph_osd_op_r_latency_count{}[5m]) >= 0", "format": "time_series", "instant": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "OSD Read Op Latency Distribution", "type": "heatmap"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}, "scaleDistribution": {"type": "linear"}}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 59}, "id": 57, "options": {"calculate": true, "calculation": {"yBuckets": {"mode": "count", "scale": {"log": 2, "type": "log"}}}, "cellGap": 2, "cellValues": {}, "color": {"exponent": 0.5, "fill": "#f9934e", "mode": "opacity", "scale": "exponential", "scheme": "Oranges", "steps": 128}, "exemplars": {"color": "rgba(255,0,255,0.7)"}, "filterValues": {"le": 1e-09}, "legend": {"show": true}, "rowsFrame": {"layout": "auto"}, "showValue": "never", "tooltip": {"show": true, "yHistogram": false}, "yAxis": {"axisPlacement": "left", "decimals": 2, "min": "0", "reverse": false, "unit": "ms"}}, "pluginVersion": "9.1.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "rate(ceph_osd_op_w_latency_sum{}[5m]) / rate(ceph_osd_op_w_latency_count{}[5m]) >= 0", "format": "time_series", "instant": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "OSD Write Op Latency Distribution", "type": "heatmap"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 67}, "id": 58, "options": {"legend": {"calcs": [], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.1.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "avg(rate(ceph_osd_op_r_latency_sum{}[5m]) / rate(ceph_osd_op_r_latency_count{}[5m]) >= 0)", "format": "time_series", "intervalFactor": 1, "legendFormat": "Read", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "avg(rate(ceph_osd_op_w_latency_sum{}[5m]) / rate(ceph_osd_op_w_latency_count{}[5m]) >= 0)", "format": "time_series", "intervalFactor": 1, "legendFormat": "Write", "refId": "B"}], "title": "Recovery Operations", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 67}, "id": 59, "options": {"legend": {"calcs": ["lastNotNull", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.1.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "avg(ceph_osd_apply_latency_ms{})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "apply", "metric": "ceph_osd_perf_apply_latency_seconds", "refId": "A", "step": 4}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "avg(ceph_osd_commit_latency_ms{})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "commit", "metric": "ceph_osd_perf_commit_latency_seconds", "refId": "B", "step": 4}], "title": "AVG OSD Apply + Commit Latency", "type": "timeseries"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 74}, "id": 60, "panels": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 9, "w": 6, "x": 0, "y": 72}, "id": 61, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.1.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "count by (ceph_version)(ceph_osd_metadata{})", "format": "time_series", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Ceph OSD Versions", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 9, "w": 6, "x": 6, "y": 72}, "id": 62, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.1.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "count by (ceph_version)(ceph_mon_metadata{})", "format": "time_series", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "<PERSON><PERSON>s", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 9, "w": 6, "x": 12, "y": 72}, "id": 63, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.1.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "count by (ceph_version)(ceph_mds_metadata{})", "format": "time_series", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Ceph MDS Versions", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 9, "w": 6, "x": 18, "y": 72}, "id": 64, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.1.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "count by (ceph_version)(ceph_rgw_metadata{})", "format": "time_series", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Ceph RGW Versions", "type": "timeseries"}], "title": "Ceph Versions", "type": "row"}], "refresh": "1m", "schemaVersion": 40, "tags": ["ceph-mixin"], "templating": {"list": [{"current": {}, "label": "Data Source", "name": "DS_PROMETHEUS", "options": [], "query": "prometheus", "refresh": 1, "regex": "", "type": "datasource"}, {"auto": true, "auto_count": 10, "auto_min": "1m", "current": {"text": "$__auto", "value": "$__auto"}, "label": "Interval", "name": "interval", "options": [{"selected": false, "text": "5s", "value": "5s"}, {"selected": false, "text": "10s", "value": "10s"}, {"selected": false, "text": "30s", "value": "30s"}, {"selected": false, "text": "1m", "value": "1m"}, {"selected": false, "text": "10m", "value": "10m"}, {"selected": false, "text": "30m", "value": "30m"}, {"selected": false, "text": "1h", "value": "1h"}, {"selected": false, "text": "6h", "value": "6h"}, {"selected": false, "text": "12h", "value": "12h"}, {"selected": false, "text": "1d", "value": "1d"}, {"selected": false, "text": "7d", "value": "7d"}, {"selected": false, "text": "14d", "value": "14d"}, {"selected": false, "text": "30d", "value": "30d"}], "query": "5s,10s,30s,1m,10m,30m,1h,6h,12h,1d,7d,14d,30d", "refresh": 2, "type": "interval"}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Ceph <PERSON>", "uid": "tbO9LAiZK", "version": 1, "weekStart": ""}