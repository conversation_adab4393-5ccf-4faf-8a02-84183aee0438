# Installation
* Step1: Add the helm repo:
    ```
    helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
    ```
* Step2: search for the available prometheus charts:
    ```
    helm search repo kube-prometheus
    ```
* Step3: Inspect the chart so we can modify the settings:
    ```
    helm inspect values prometheus-community/kube-prometheus-stack > kube-prometheus-stack.values
    ```
* Step4: Edit Values

    The values fiel is under `manifests/helm`
* Stop5: Deploy the Prometheus and Grafana pods using the `kube-prometheus-stack` via Helm:
    ```
    helm install prometheus-community/kube-prometheus-stack \
   --create-namespace --namespace prometheus \
   --generate-name \
   --values kube-prometheus-stack.values
    ```
* Step6: Expose `grafana` service
    
    ```
    # kubectl edit -n prometheus $(kubectl get svc -n prometheus -o name -l app.kubernetes.io/name=grafana)
    ```
    config the following keys:
```
apiVersion: v1
kind: Service
metadata:
  annotations:
    meta.helm.sh/release-name: kube-prometheus-stack-1734510678
    meta.helm.sh/release-namespace: prometheus
  creationTimestamp: "2024-12-18T08:31:30Z"
  labels:
    app.kubernetes.io/instance: kube-prometheus-stack-1734510678
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: grafana
    app.kubernetes.io/version: 11.4.0
    helm.sh/chart: grafana-8.8.2
  name: kube-prometheus-stack-1734510678-grafana
  namespace: prometheus
  resourceVersion: "1214065"
  uid: 6a8bd0c2-14d7-4c78-8537-31c67199d322
spec:
  clusterIP: *************
  clusterIPs:
  - *************
  externalTrafficPolicy: Cluster
  internalTrafficPolicy: Cluster
  ipFamilies:
  - IPv4
  ipFamilyPolicy: SingleStack
  ports:
  - name: http-web
    # Add nodePort: 32322
    nodePort: 32322
    port: 80
    protocol: TCP
    targetPort: 3000
  selector:
    app.kubernetes.io/instance: kube-prometheus-stack-1734510678
    app.kubernetes.io/name: grafana
  sessionAffinity: None
  # Config type to NodePort
  type: NodePort
status:
  loadBalancer: {}
```

Step7: Login Grafana console


Open http://*************:32322 in Browser, the username is `admin`, password is `prom-operator`.

![dashboard](./assets/dashboard.png)

Click `Dashboards`

![dashboard](./assets/dashboard2.png)
Click `Import`


![dashboard](./assets/dashboard3.png)
Click `Upload dashboard JSON file`

Select the Dashboard json under `manifests` directory
