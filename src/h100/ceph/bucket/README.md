# Create a bucket
Create object store store-x
```
kubectl apply -f object-store.yaml
```

Create storage class rook-ceph-bucket-x
```
kubectl apply -f storageclass-bucket.yaml
```

Create bucket claim ceph-bucket-x
```
kubectl apply -f bucket-claim.yaml
```

*Create node port 30480 for store-x, for debug purpose
```
kubectl apply -f object-store-x-nodeport.yaml
```

# How to access bucket
Export info
```
#config-map, secret, OBC will part of default if no specific name space mentioned
export AWS_HOST=$(kubectl -n rook-ceph get cm ceph-bucket-x -o jsonpath='{.data.BUCKET_HOST}')
export PORT=$(kubectl -n rook-ceph get cm ceph-bucket-x -o jsonpath='{.data.BUCKET_PORT}')
export BUCKET_NAME=$(kubectl -n rook-ceph get cm ceph-bucket-x -o jsonpath='{.data.BUCKET_NAME}')
export AWS_ACCESS_KEY_ID=$(kubectl -n rook-ceph get secret ceph-bucket-x -o jsonpath='{.data.AWS_ACCESS_KEY_ID}' | base64 --decode)
export AWS_SECRET_ACCESS_KEY=$(kubectl -n rook-ceph get secret ceph-bucket-x -o jsonpath='{.data.AWS_SECRET_ACCESS_KEY}' | base64 --decode)
echo AWS_HOST=$AWS_HOST
echo PORT=$PORT
echo BUCKET_NAME=$BUCKET_NAME
echo AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID
echo AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY
```

Get info like:
```
AWS_HOST=rook-ceph-rgw-store-x.rook-ceph.svc
PORT=80
BUCKET_NAME=ceph-bkt-x-aed6f4c4-8c33-471b-a691-6ec61a7a6cc5
AWS_ACCESS_KEY_ID=K422U1T76BBOV4VYP7H9
AWS_SECRET_ACCESS_KEY=Wc7GzPRn25DtWn7ANtn7n8TOf1He5fFO2JFCYkhZ
```

Install a S3 command line client on Ubuntu 22.04
```
sudo apt install s3cmd -y
```

Configure s3cmd using exported values
```
s3cmd --configure
```
 - Access Key: AWS_ACCESS_KEY_ID exported
 - Secret Key: AWS_SECRET_ACCESS_KEY exported
 - S3 Endpoint: The host base that s2cmd will connect to, here use http://<node-ip>:<node-port>
 - DNS-style bucket+hostname:port template for accessing a bucket: leave it as space so s3cmd will force to use path style(http://<s3_endpoint>/<bucket-name>/<object-key>) instead of dns style(http://<bucket-name>.hostname:port/<object-key>) to query bucket.
 - Use HTTPS protocol: Currently use http.

s3cmd configration like:
```
New settings:
  Access Key: **K422U1T76BBOV4VYP7H9**
  Secret Key: **Wc7GzPRn25DtWn7ANtn7n8TOf1He5fFO2JFCYkhZ**
  Default Region: US
  S3 Endpoint: **http://***************:30480**
  DNS-style bucket+hostname:port template for accessing a bucket:  
  Encryption password:
  Path to GPG program: /usr/bin/gpg
  Use HTTPS protocol: **False**
  HTTP Proxy server name:
  HTTP Proxy server port: 0
```

List bucket name
```
s3cmd ls
```

List objects in a bucket(bucket name from *s3cmd ls*)
```
s3cmd ls s3://ceph-bkt-x-aed6f4c4-8c33-471b-a691-6ec61a7a6cc5
```

Put object to a bucket
```
s3cmd put file.txt s3://ceph-bkt-x-aed6f4c4-8c33-471b-a691-6ec61a7a6cc5
```

Get object from a bucket
```
s3cmd get s3://ceph-bkt-x-aed6f4c4-8c33-471b-a691-6ec61a7a6cc5/file.txt
```

[TBU]To use DNS style, need more configuration on envrionment.