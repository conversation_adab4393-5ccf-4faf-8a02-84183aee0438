# Install Rook Ceph and <PERSON><PERSON><PERSON>
```
ROOK_VERSION="release-1.15"
ROOK_URL="https://raw.githubusercontent.com/rook/rook/${ROOK_VERSION}/deploy/examples"

kubectl create -f ${ROOK_URL}/crds.yaml -f
kubectl create -f ${ROOK_URL}/common.yaml
kubectl create -f ./cephoperator.yaml
kubectl create -f ./cephcluster.yaml
kubectl create -f ${ROOK_URL}/filesystem.yaml
kubectl create -f ${ROOK_URL}/csi/cephfs/storageclass.yaml
kubectl patch storageclass rook-cephfs -p '{"metadata": {"annotations":{"storageclass.kubernetes.io/is-default-class":"true"}}}'
```