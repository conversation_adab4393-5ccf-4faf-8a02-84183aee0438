# Installation
* Step1: Deploy the ingress controller with the following command:
    ```
    helm upgrade --install ingress-nginx ingress-nginx \
    --repo https://kubernetes.github.io/ingress-nginx \
    --namespace ingress-nginx --create-namespace
    ```
    It will install the controller in the ingress-nginx namespace, creating that namespace if it doesn't already exist.

    > This command is idempotent:
    > * if the ingress controller is not installed, it will install it,
    > * if the ingress controller is already installed, it will upgrade it.

    If you want a full list of values that you can set, while installing with <PERSON><PERSON>, then run:
    ```
    helm show values ingress-nginx --repo https://kubernetes.github.io/ingress-nginx
    ```