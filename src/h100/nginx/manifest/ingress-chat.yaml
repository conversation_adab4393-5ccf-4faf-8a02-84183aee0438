apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: chat-ingress
  namespace: eypoc
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/auth-signin: https://oauth2-proxy.projectx.eypoc.com/oauth2/start?rd=https://$host$request_uri
    nginx.ingress.kubernetes.io/auth-url: http://oauth2-proxy.oauth2-proxy.svc.cluster.local/oauth2/auth
    nginx.ingress.kubernetes.io/proxy-body-size: "0"
    nginx.ingress.kubernetes.io/proxy-request-buffering: "off"
    nginx.ingress.kubernetes.io/auth-response-headers: "Authorization,X-Auth-Request-User,X-Auth-Request-Email,X-Auth-Request-Groups, X-Auth-Request-Preferred-Username"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - chat.projectx.eypoc.com
  rules:
  - host: chat.projectx.eypoc.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: eypoc
            port:
              number: 5000
