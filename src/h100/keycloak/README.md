# Prerequisite
* `Metallb` installed
* `Ingress-nginx` installed


# Installation
* Step 1: Add Bitnami Helm repository:
    ```
    helm repo add bitnami https://charts.bitnami.com/bitnami
    ```
* Step 2: Fetch the latest charts from the repository:
    ```
    helm repo update
    ```
* Step 3: Create namespace
    ```
    kubectl create namespace keycloak
    ```
* Step 4: Create file `keycloak-values.yml`
    ```
    # Run in production mode behind NGINX proxy terminating TLS sessions
    production: true
    # ref: https://www.keycloak.org/server/reverseproxy
    proxyHeaders: xforwarded

    # Admin user
    auth:
    adminUser: admin
    # postgresSQL
    #postgresql:
    #  enabled: true
    #  auth:
    #    username: keycloak
    #    database: keycloak
    # Ingress config
    ingress:
    enabled: true
    ingressClassName: "nginx"
    pathType: Prefix
    annotations:
        nginx.ingress.kubernetes.io/proxy-buffers-number: "4"
        nginx.ingress.kubernetes.io/proxy-buffer-size: "16k"
    hostname: sso.projectx.eric.com
    tls: true
    ```
* Step 5: Install `Keycloak` in keycloak namespace
    ```
    helm install keycloak bitnami/keycloak -f keycloak-values.yml --namespace keycloak
    ```
* Step 6: Check status of Keycloak pods
    ```
    kubectl get pods -n keycloak
    ```
* Step 7: Config Keyloak ingress
    add `tls` section under spec using the command below:
    ```
    kubectl edit ingress -n keycloak keycloak
    ```
    after modification, the ingress should be:
```
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: keycloak
  namespace: keycloak
spec:
  ingressClassName: nginx
  rules:
  - host: sso.projectx.eypoc.com
    http:
      paths:
      - backend:
          service:
            name: keycloak
            port:
              name: http
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - sso.projectx.eypoc.com
```
* Step 7: Get keycloak `admin` user password
    ```
    kubectl get secret keycloak -o jsonpath='{.data.admin-password}' -n keycloak | base64 -d && echo
    ```
* Step 8: connect to keycloak admin console `hostname: sso.projectx.eric.com`

    Log in using ‘admin’ user and password obtained in step 7.

## Configuration

* Step 1: Login as admin to Keycloak console

    Open URL: https://sso.projectx.eypoc.com, 
* Step 2: Create a new realm `projectx`


     ![realm](./assets/client0.png)
    ![realm](./assets/realm.png)

* Step 3: Configure Oauth2-Proxy Client
    
    ![client](./assets/client.png)

    Click `Create`

    ![client2](./assets/client2.png)
    
    Enable `Client authentication` and tick `Standard flow`
    
    ![client3](./assets/client3.png)
    Provide the following ‘Logging settings’
    Valid redirect URIs: https://oauth2-proxy.projectx.eypoc.com/oauth2/callback and save the configuration.


    ![client4](./assets/client4.png)

    Create oauth2-proxy client credentials. Under the Credentials tab you will now be able to locate oauth2-proxy client’s secret.


    ![client5](./assets/client5.png)

    - Configure a dedicated audience mapper for the client.Navigate to Clients -> oauth2-proxy client -> Client scopes.
    
    - Access the dedicated mappers pane by clicking ‘oauth2-proxy-dedicated’, located under Assigned client scope. (It should have a description of “Dedicated scope and mappers for this client”)

    - Click on ‘Configure a new mapper’ and select ‘Audience’
    
    - Provide following data:
        * Name ‘aud-mapper-oauth2-proxy’
        * Included Client Audience select oauth2-proxy client’s id from the dropdown.
        * Add to ID token ‘On’
        * Add to access token ‘On’ OAuth2 proxy can be set up to pass both the access and ID JWT tokens to your upstream services.
    - Save the configuration.

    ![user](./assets/user1.png)
    Click `Add User`

    ![user2](./assets/user2.png)
    
    Set a username and Click `Create`

    ![user3](./assets/user3.png)
    Click `Set Password`

    ![user4](./assets/user4.png)
    Set a password and unselect `Temporary`, then click `Save`
