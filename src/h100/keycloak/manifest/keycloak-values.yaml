# Run in production mode behind NGINX proxy terminating TLS sessions
production: true
# ref: https://www.keycloak.org/server/reverseproxy
proxyHeaders: xforwarded

# Admin user
auth:
  adminUser: admin
# postgresSQL
#postgresql:
#  enabled: true
#  auth:
#    username: keycloak
#    database: keycloak
# Ingress config
ingress:
  enabled: true
  ingressClassName: "nginx"
  pathType: Prefix
  annotations:
    nginx.ingress.kubernetes.io/proxy-buffers-number: "4"
    nginx.ingress.kubernetes.io/proxy-buffer-size: "16k"
  hostname: sso.projectx.eric.com
  tls: true
