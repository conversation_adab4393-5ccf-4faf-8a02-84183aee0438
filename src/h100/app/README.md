# Prerequisites
- Served LLM model (OpenAI Compatible API)
- Served embedding model (OpenAI Compatible API)
- PostgreSQL
- Qdrant


# Local Run
```
python -m venv .venv
source venv/bin/activate
pip install -r requirements.txt
python main.py
```
Now you can visit http://localhost:5000

# Deploy on k8s
```
docker build -t eypoc .
docker tag eypoc ************:9443/dell/eypoc:0.0.39
docker push ************:9443/dell/eypoc:0.0.39
kubectl apply -f eypoc.yaml
```
To adjust the environment configuration and image tag, update both the command-line and the eypoc.yaml file accordingly.

Once done:

- You can access the service via the NodePort at 30888 (you may edit this port in the eypoc.yaml file if necessary).
- Alternatively, if you set up an ingress, you can access the service according to your ingress configuration.
