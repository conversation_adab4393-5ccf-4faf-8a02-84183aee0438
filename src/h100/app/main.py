import requests
import fitz  # PyMuPDF
import psycopg2
from psycopg2 import pool, OperationalError
from flask import Flask, request, jsonify, send_from_directory, redirect
from uuid import uuid4
from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams
from llama_index.llms.openai_like import OpenAILike
import logging
import time

from config import *

app = Flask(__name__, static_url_path='', static_folder='static')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Qdrant client
qdrant_client = QdrantClient(host=QDRANT_HOST, port=QDRANT_PORT)

# Initialize PostgreSQL connection pool
try:
    db_pool = psycopg2.pool.SimpleConnectionPool(
        1, 
        10, 
        dbname=POSTGRES_DBNAME,
        user=POSTGRES_USER,
        password=POSTGRES_PASSWORD,
        host=POSTGRES_HOST,
        port=POSTGRES_PORT
    )
except Exception as error:
    logger.error(f"Error: Could not establish connection pool: {error}")
    db_pool = None

def embed_text(text):
    try:
        response = requests.post(EMBEDDING_API_URL, json={"input": text, "model": EMBEDDING_MODEL, "encoding_format": "float"})
        response.raise_for_status()
        return response.json()["data"][0]["embedding"]
    except requests.RequestException as e:
        logger.error(f"Failed to embed text: {e}")
        raise

def execute_db_query(db_pool, query, params=None, fetch_result=False, retry_attempts=3, retry_delay=1):
    """
    Helper function to execute a query with retry.
    """
    attempt = 0

    while attempt < retry_attempts:
        conn = None
        cur = None

        try:
            conn = db_pool.getconn()
            cur = conn.cursor()
            cur.execute(query, params)
            conn.commit()
            if fetch_result:
                return cur.fetchall() if "fetchall" in fetch_result else cur.fetchone()
            return None
        except OperationalError as e:
            logger.error(f"Database operation failed: {e}")
            attempt += 1
            time.sleep(retry_delay)  # Delay before retrying
        finally:
            if cur:
                cur.close()
            if conn:
                db_pool.putconn(conn)

    raise OperationalError("Failed to execute the database query after multiple attempts")

# Route: Upload PDF file and insert content into specified knowledge base
@app.route('/upload', methods=['POST'])
def upload_pdf():
    file = request.files['file']
    knowledge_base = request.form.get('knowledge_base')

    if not knowledge_base:
        return jsonify({"status": "error", "message": "No knowledge base provided"})

    if file and file.filename.endswith('.pdf'):
        doc = fitz.open(stream=file.read(), filetype='pdf')
        texts = []

        # Extract PDF text
        for page in doc:
            texts.append(page.get_text("text"))

        embeddings = [embed_text(text) for text in texts]

        # Create or update Qdrant collection
        try:
            qdrant_client.create_collection(
                collection_name=knowledge_base,
                vectors_config=VectorParams(size=1024, distance=Distance.DOT),
            )
        except Exception as e:
            logger.warning(f"Collection might already exist: {e}")

        # Insert vectors into specified Qdrant collection
        point_ids = []
        for text, embedding in zip(texts, embeddings):
            point_id = str(uuid4())
            point_ids.append(point_id)
            qdrant_client.upsert(
                collection_name=knowledge_base,
                points=[
                    {"id": point_id, "vector": embedding, "payload": {"text": text}}
                ]
            )

        # Record document info in PostgreSQL
        query = "INSERT INTO documents (knowledge_base, doc_id, filename) VALUES (%s, %s, %s) RETURNING id"
        params = (knowledge_base, ','.join(point_ids), file.filename)

        try:
            execute_db_query(db_pool, query, params)
        except OperationalError as e:
            return jsonify({"status": "error", "message": f"Database error: {e}"})

        return jsonify({"status": "success"})
    else:
        return jsonify({"status": "error", "message": "Invalid file type or no file provided"})

# Route: Retrieve response based on query from specified knowledge base
@app.route('/query', methods=['POST'])
def query_pdf():
    data = request.json
    query = data.get('query')
    knowledge_base = data.get('knowledge_base')
    temperature = data.get('temperature', 0.1)
    top_k = data.get('top_k', 5)
    cutoff_score = data.get('cutoff_score', 0.5)
    selected_llm = data.get('llm_backend')

    logger.info(f'Query: {query} | Knowledge Base: {knowledge_base} | LLM: {selected_llm} | Temperature: {temperature} | Top K: {top_k} | Cutoff Score: {cutoff_score}')

    if not selected_llm or selected_llm not in LLM_CONFIGS:
        return jsonify({"status": "error", "message": "Invalid LLM backend selected"})

    llm_api_url = LLM_CONFIGS[selected_llm]['api']
    llm_model = LLM_CONFIGS[selected_llm]['model']

    logger.info(f'llm_api_url: {llm_api_url} | llm_model: {llm_model}')

    if not query:
        return jsonify({"status": "error", "message": "No query provided"})
    if not knowledge_base:
        return jsonify({"status": "error", "message": "No knowledge base provided"})

    query_vector = embed_text(query)

    # Search for the most relevant vectors in the specified Qdrant collection
    search_results = qdrant_client.search(
        collection_name=knowledge_base,
        query_vector=query_vector,
        limit=top_k,
        with_vectors=False,
        score_threshold=cutoff_score,
    )

    # Extract relevant texts from the search results
    retrieved_texts = [result.payload["text"] for result in search_results]

    logger.info(f'retrieved {len(retrieved_texts)} texts with score {[result.score for result in search_results]}')

    if not retrieved_texts:
        return jsonify({"status": "error", "message": "No relevant documents found"})

    # Configure the context for generating the answer
    context = " ".join(retrieved_texts)

    # Initialize OpenAI-compatible model
    llm = OpenAILike(
        model=llm_model,
        api_base=llm_api_url,
        api_key="fake key",
        temperature=temperature,
    )

    prompt = f'''
<|begin_of_text|><|start_header_id|>system<|end_header_id|>

You are a helpful assistant.
Context information is below.
----------------------
{context}
----------------------
Given the context information and not prior knowledge, answer the query.<|eot_id|><|start_header_id|>user<|end_header_id|>

{query}<|eot_id|><|start_header_id|>assistant<|end_header_id|>
'''
    
    # logger.info(f'LLM: {selected_llm}\nAPI: {llm_api_url}\nmodel: {llm_model}\nprompt: {prompt}\n')
    response = llm.complete(prompt, max_tokens=1024)

    if response:
        return jsonify({"response": str(response)})

    return jsonify({"status": "error", "message": "Error generating response with LLM"})

# Route: Delete document from knowledge base
@app.route('/delete', methods=['POST'])
def delete_document():
    knowledge_base = request.json.get('knowledge_base')
    filename = request.json.get('filename')

    if not knowledge_base:
        return jsonify({"status": "error", "message": "No knowledge base provided"})
    if not filename:
        return jsonify({"status": "error", "message": "No filename provided"})

    query = "SELECT doc_id FROM documents WHERE knowledge_base = %s AND filename = %s"
    params = (knowledge_base, filename)

    try:
        result = execute_db_query(db_pool, query, params, fetch_result="fetchone")
    except OperationalError as e:
        return jsonify({"status": "error", "message": f"Database error: {e}"})

    if not result:
        return jsonify({"status": "error", "message": "Document not found"})

    # Delete vectors from Qdrant
    point_ids = result[0].split(',')
    qdrant_client.delete(
        collection_name=knowledge_base,
        points_selector=point_ids  # Directly passing the list of point IDs
    )

    # Delete record from PostgreSQL
    delete_query = "DELETE FROM documents WHERE knowledge_base = %s AND filename = %s"
    delete_params = (knowledge_base, filename)

    try:
        execute_db_query(db_pool, delete_query, delete_params)
    except OperationalError as e:
        return jsonify({"status": "error", "message": f"Database error: {e}"})

    return jsonify({"status": "success"})

# Route: Get all knowledge bases and their documents
@app.route('/all_knowledge_bases', methods=['GET'])
def get_all_knowledge_bases():
    query = "SELECT knowledge_base, filename FROM documents"

    try:
        knowledge_base_docs = execute_db_query(db_pool, query, fetch_result="fetchall")
        kb_dict = {}
        for row in knowledge_base_docs:
            kb, filename = row
            if kb not in kb_dict:
                kb_dict[kb] = []
            kb_dict[kb].append({"filename": filename})
            
        return jsonify(kb_dict)
    except OperationalError as e:
        return jsonify({"status": "error", "message": f"Database error: {e}"})

@app.route('/llm_backends', methods=['GET'])
def get_llm_backends():
    llms = [{"id": k, "model_name": v["model"]} for k, v in LLM_CONFIGS.items()]
    return jsonify({"llm_backends": llms})

@app.route('/')
def index():
    return send_from_directory(app.static_folder, "index.html")

@app.route('/api/userinfo')
def userinfo():
    cookies = request.cookies
    response = requests.get('http://oauth2-proxy.oauth2-proxy.svc.cluster.local/oauth2/userinfo', cookies=cookies)
    
    if response.status_code == 200:
        userinfo = response.json()
        return jsonify(userinfo)
    else:
        return jsonify({"error": "Failed to fetch user info"}), response.status_code

@app.route('/api/logout')
def logout():
    cookies = request.cookies
    response = requests.get('http://oauth2-proxy.oauth2-proxy.svc.cluster.local/oauth2/sign_out', cookies=cookies)

    if response.status_code == 200:
        return redirect('/')
    else:
        return jsonify({"error": "Failed to log out"}), response.status_code

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0')