body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    height: 100vh;
    overflow: hidden;
}

#sidebar {
    height: 100vh;
    position: fixed;
    left: 0;
    top: 0;
    width: 250px;
    transition: all 0.3s;
    display: flex;
    flex-direction: column;
}

#sidebar.collapsed {
    width: 60px;
}

#sidebar.collapsed .sidebar-header .h4 {
    display: none;
}

#sidebar.collapsed .nav,
#sidebar.collapsed .mt-auto {
    display: none;
}

#sidebar.collapsed #sidebar-toggle {
    display: block;
    margin: auto;
}

#sidebar .sidebar-header {
    padding: 10px;
    background: #343a40;
}

#sidebar .nav-link {
    color: #fff;
}

#sidebar .nav-link.active {
    background: #007bff;
}

#sidebar .sidebar-footer {
    margin-top: auto;
}

#sidebar-toggle {
    background: none;
    border: none;
    color: #fff;
    cursor: pointer;
}

.tab-content {
    margin-left: 250px;
    transition: all 0.3s;
    width: calc(100% - 250px);
}

#sidebar.collapsed + .tab-content {
    margin-left: 60px;
    width: calc(100% - 60px);
}

#response {
    margin-top: 20px;
    white-space: pre-wrap;
    background-color: #f9f9f9;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #ccc;
}

.kb-item:hover {
    background-color: #f0f0f0;
    cursor: pointer;
}

.kb-item.active {
    background-color: #007bff;
    color: white;
}

#documents-header {
    margin-top: 20px;
}

#doc-list {
    max-height: 400px;
    overflow-y: auto;
}