let knowledgeBasesWithDocs = {};  // to store knowledge bases with their documents

$(document).ready(function() {
    fetchUserInfo();
    fetchAllKnowledgeBasesAndDocuments();
    fetchLLMBackends();
    $('.nav-link').first().click();  // Open the first tab by default

    // Sidebar toggle functionality
    $('#sidebar-toggle').click(function() {
        toggleSidebar();
    });

    // Restore sidebar state from localStorage
    if (localStorage.getItem('sidebar-collapsed') === 'true') {
        $('#sidebar').addClass('collapsed');
        $('.tab-content').addClass('collapsed');
    }
});

function toggleSidebar() {
    const sidebar = $('#sidebar');
    const content = $('.tab-content');
    sidebar.toggleClass('collapsed');
    content.toggleClass('collapsed');
    localStorage.setItem('sidebar-collapsed', sidebar.hasClass('collapsed'));
}

function fetchAllKnowledgeBasesAndDocuments() {
    $.get('/all_knowledge_bases', function(data) {
        knowledgeBasesWithDocs = data;
        updateKnowledgeBaseList();
        updateSelectKbOptions();
    }).fail(function(error) {
        console.error('Error fetching all knowledge bases and documents:', error);
    });
}

function updateKnowledgeBaseList() {
    const kbList = $('#kb-list');
    kbList.empty();  // Clear existing items
    for (const kb in knowledgeBasesWithDocs) {
        kbList.append(`
            <li class="list-group-item kb-item" onclick="fetchDocuments('${kb}')">
                ${kb}
            </li>
        `);
    }
}

function updateSelectKbOptions() {
    const selectKb = $('#select-kb');
    selectKb.empty();  // Clear existing options
    for (const kb in knowledgeBasesWithDocs) {
        selectKb.append(new Option(kb, kb));
    }
}

function fetchLLMBackends() {
    $.get('/llm_backends', function(data) {
        const selectLlm = $('#select-llm');
        selectLlm.empty();  // Clear existing options
        data.llm_backends.forEach(llm => {
            selectLlm.append(new Option(`${llm.id} : ${llm.model_name}`, llm.id));
        });
    }).fail(function(error) {
        console.error('Error fetching llm backends:', error);
    });
}

function uploadPDF() {
    const kbName = $('#kb-name').val();
    const fileInput = $('#pdf-file')[0];
    const file = fileInput.files[0];

    if (!kbName || !file) {
        alert('Please provide a knowledge base name and select a PDF file.');
        return;
    }

    const formData = new FormData();
    formData.append('knowledge_base', kbName);
    formData.append('file', file);

    $.ajax({
        url: '/upload',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(data) {
            alert(data.status === 'success' ? 'Upload successful!' : 'Upload failed.');
            fetchAllKnowledgeBasesAndDocuments();  // Update knowledge base and document list
        },
        error: function(error) {
            console.error('Error:', error);
        }
    });
}

function sendQuery() {
    const kbName = $('#select-kb').val();
    const llmBackend = $('#select-llm').val();
    const query = $('#user-query').val();

    const temperature = parseFloat($('#temperature').val());
    const topK = parseInt($('#top-k').val());
    const cutoffScore = parseFloat($('#cutoff-score').val());

    if (!kbName || !query) {
        alert('Please select a knowledge base and enter your query.');
        return;
    }

    // Clear previous result and display loading message
    const responseDiv = $('#response');
    responseDiv.html('<p>Loading...</p>');

    // Disable the button
    const sendButton = $('#send-button');
    sendButton.prop('disabled', true);

    $.ajax({
        url: '/query',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            query: query,
            knowledge_base: kbName,
            llm_backend: llmBackend,
            temperature: temperature,
            top_k: topK,
            cutoff_score: cutoffScore
        }),
        success: function(data) {
            if (data.response) {
                responseDiv.html(data.response);
            } else {
                responseDiv.html(JSON.stringify(data, null, 2));
            }
        },
        error: function(error) {
            console.error('Error:', error);
        },
        complete: function() {
            // Re-enable the button
            sendButton.prop('disabled', false);
        }
    });
}

function fetchUserInfo() {
    $.get('/api/userinfo', function(data) {
        if (data.preferredUsername) {
            $('#user-info').text(`Logged in as: ${data.preferredUsername}`);
        } else {
            $('#user-info').text('Failed to fetch user info.');
        }
    }).fail(function(error) {
        console.error('Error fetching user info:', error);
    });
}

function logout() {
    $.get('/api/logout', function(response) {
        if (response.redirected) {
            window.location.href = response.url;
        } else {
            console.error('Logout failed.');
        }
    }).fail(function(error) {
        console.error('Error during logout:', error);
    });
}

function updateTemperatureValue(value) {
    $('#temperature-value').text(value);
}

function fetchDocuments(kbName) {
    $('#kb-name').val(kbName);
    $('#documents-header').text(`Documents in "${kbName}"`);
    const docList = $('#doc-list');
    docList.empty();  // Clear existing items
    const documents = knowledgeBasesWithDocs[kbName];
    documents.forEach(doc => {
        docList.append(`
            <li class="list-group-item d-flex justify-content-between align-items-center">
                ${doc.filename}
                <button class="btn btn-danger btn-sm" onclick="deleteDocument('${kbName}', '${doc.filename}')">Delete</button>
            </li>
        `);
    });
    // Highlight the selected knowledge base
    $('.kb-item').removeClass('active');
    $(`.kb-item:contains('${kbName}')`).addClass('active');
}

function validateKbName() {
    const kbName = $('#kb-name').val();
    if (!knowledgeBasesWithDocs[kbName]) {
        $('#documents-header').text('Documents');
        $('#doc-list').empty();
        $('.kb-item').removeClass('active');
    } else {
        fetchDocuments(kbName);
    }
}

function deleteDocument(kbName, filename) {
    $.ajax({
        url: '/delete',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({ knowledge_base: kbName, filename: filename }),
        success: function(data) {
            alert(data.status === 'success' ? 'Delete successful!' : 'Delete failed.');
            fetchAllKnowledgeBasesAndDocuments();  // Update knowledge base and document list
        },
        error: function(error) {
            console.error('Error:', error);
        }
    });
}