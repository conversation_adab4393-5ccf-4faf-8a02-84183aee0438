<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat with Knowledge Base</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body>
    <div class="d-flex">
        <nav id="sidebar" class="bg-dark text-white">
            <div class="sidebar-header d-flex justify-content-between align-items-center py-3">
                <span class="h4">EYPOC RAG</span>
                <button id="sidebar-toggle" class="btn btn-outline-light btn-sm"><i class="fas fa-bars"></i></button>
            </div>
            <div id="sidebar-menu">
                <ul class="nav flex-column nav-pills">
                    <li class="nav-item">
                        <a class="nav-link active" href="#chat" data-toggle="tab">Chat</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#manage-kb" data-toggle="tab">Knowledge Base</a>
                    </li>
                </ul>
            </div>
            <div class="sidebar-footer mt-auto p-3">
                <span id="user-info" class="d-block mb-2"></span>
                <button id="logout-button" class="btn btn-outline-light btn-block" onclick="logout()">Logout</button>
            </div>
        </nav>
        <div class="tab-content flex-grow-1 p-4">
            <div class="tab-pane fade show active" id="chat">
                <div class="row">
                    <div class="col-9">
                        <div class="form-group">
                            <label for="user-query">Your Query:</label>
                            <textarea id="user-query" class="form-control" rows="4"></textarea>
                        </div>
                        <button id="send-button" class="btn btn-primary" onclick="sendQuery()">Send</button>
                        <div id="response" class="mt-3">Response will be displayed here...</div>
                    </div>
                    <div class="col-3">
                        <div class="form-group">
                            <label for="select-kb">Select Knowledge Base:</label>
                            <select id="select-kb" class="form-control"></select>
                        </div>
                        <div class="form-group">
                            <label for="select-llm">Select LLM Backend:</label>
                            <select id="select-llm" class="form-control"></select>
                        </div>
                        <div class="form-group">
                            <label for="temperature">LLM Temperature: <span id="temperature-value">0.1</span></label>
                            <input type="range" id="temperature" class="form-control-range" step="0.1" min="0" max="1" value="0.1" oninput="updateTemperatureValue(this.value)">
                        </div>
                        <div class="form-group">
                            <label for="top-k">RAG Top-K:</label>
                            <input type="number" id="top-k" class="form-control" min="1" value="5">
                        </div>
                        <div class="form-group">
                            <label for="cutoff-score">RAG Cutoff Score:</label>
                            <input type="number" id="cutoff-score" class="form-control" step="0.1" min="0" value="0.5">
                        </div>
                    </div>
                </div>
            </div>
            <div class="tab-pane fade" id="manage-kb">
                <div class="row">
                    <div class="col-4">
                        <h4>Knowledge Bases</h4>
                        <ul id="kb-list" class="list-group">
                            <!-- Knowledge bases will be dynamically added here -->
                        </ul>
                    </div>
                    <div class="col-8">
                        <h4 id="documents-header">Documents</h4>
                        <ul id="doc-list" class="list-group mb-3">
                            <!-- Documents will be dynamically added here -->
                        </ul>
                        <div class="form-group">
                            <label for="kb-name">Knowledge Base Name:</label>
                            <input type="text" id="kb-name" class="form-control" onchange="validateKbName()">
                        </div>
                        <div class="form-group">
                            <label for="pdf-file">Upload PDF:</label>
                            <input type="file" id="pdf-file" class="form-control-file" accept=".pdf">
                        </div>
                        <button class="btn btn-primary" onclick="uploadPDF()">Upload</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.4/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script src="js/scripts.js"></script>
</body>
</html>