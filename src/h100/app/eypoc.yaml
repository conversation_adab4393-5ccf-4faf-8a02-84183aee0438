apiVersion: v1
kind: Namespace
metadata:
  name: eypoc

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: eypoc
  namespace: eypoc
spec:
  replicas: 1
  selector:
    matchLabels:
      app: eypoc
  template:
    metadata:
      labels:
        app: eypoc
    spec:
      containers:
      - name: eypoc
        image: ************:9443/dell/eypoc:0.0.39
        ports:
        - containerPort: 5000
        env:
        - name: QDRANT_HOST
          value: 'qdrant.qdrant.svc.cluster.local'
        - name: QDRANT_PORT
          value: '6333'
        - name: POSTGRES_DBNAME
          value: 'knowledge_base'
        - name: POSTGRES_USER
          value: 'postgres'
        - name: POSTGRES_PASSWORD
          value: '1ON9QotBEw'
        - name: POSTGRES_HOST
          value: 'postgresql.postgresql.svc.cluster.local'
        - name: POSTGRES_PORT
          value: '5432'
        - name: LLM_CONFIGS
          value: '{"llm1":{"api":"http://llama-3-3-70b-instruct-service.default.svc.cluster.local:8000/v1","model":"/models/Llama-3.3-70B-Instruct"},"llm2":{"api":"http://llama3-1.default.svc.cluster.local:8000/v1","model":"meta-llama/Llama-3.1-8B-Instruct"},"llm3":{"api":"http://llama-3-groq.default.svc.cluster.local:8000/v1","model":"Groq/Llama-3-Groq-8B-Tool-Use"}}'
        - name: EMBEDDING_API_URL
          value: 'http://bge-m3.default.svc.cluster.local:8000/v1/embeddings'
        - name: EMBEDDING_MODEL
          value: 'BAAI/bge-m3'
      imagePullSecrets:
      - name: nexus-registry-secret

---
apiVersion: v1
kind: Service
metadata:
  name: eypoc
  namespace: eypoc
spec:
  selector:
    app: eypoc
  ports:
    - protocol: TCP
      port: 80
      targetPort: 5000
      nodePort: 30888
  type: NodePort