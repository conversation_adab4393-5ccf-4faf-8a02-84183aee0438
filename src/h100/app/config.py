import os
import json

# Qdrant configuration
QDRANT_HOST = os.getenv('QDRANT_HOST', '***************')
QDRANT_PORT = os.getenv('QDRANT_PORT', '30333')

# PostgreSQL configuration
POSTGRES_DBNAME = os.getenv('POSTGRES_DBNAME', 'knowledge_base')
POSTGRES_USER = os.getenv('POSTGRES_USER', 'postgres')
POSTGRES_PASSWORD = os.getenv('POSTGRES_PASSWORD', '1ON9QotBEw')
POSTGRES_HOST = os.getenv('POSTGRES_HOST', '***************')
POSTGRES_PORT = os.getenv('POSTGRES_PORT', '30432')

# LLM configuration
LLM_CONFIGS = json.loads(os.getenv('LLM_CONFIGS', '{"llm1":{"api":"http://***************:30121/v1","model":"/models/Llama-3.3-70B-Instruct"}}'))

# Embedding API configuration
EMBEDDING_API_URL = os.getenv('EMBEDDING_API_URL', 'http://***************:30125/v1/embeddings')
EMBEDDING_MODEL = os.getenv('EMBEDDING_MODEL', 'BAAI/bge-m3')
