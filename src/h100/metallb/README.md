# Metallb

## Why?
Kubernetes does not offer an implementation of network load balancers (Services of type LoadBalancer) for bare-metal clusters. The implementations of network load balancers that Kubernetes does ship with are all glue code that calls out to various IaaS platforms (GCP, AWS, Azure…). If you’re not running on a supported IaaS platform (GCP, AWS, Azure…), LoadBalancers will remain in the “pending” state indefinitely when created.

Bare-metal cluster operators are left with two lesser tools to bring user traffic into their clusters, “NodePort” and “externalIPs” services. Both of these options have significant downsides for production use, which makes bare-metal clusters second-class citizens in the Kubernetes ecosystem.

MetalLB aims to redress this imbalance by offering a network load balancer implementation that integrates with standard network equipment, so that external services on bare-metal clusters also “just work” as much as possible.

## Installation
* Step1： Add official Helm Repo
    ```
    helm repo add metallb https://metallb.github.io/metallb
    ```
* Step 2: Fetch the latest charts from the repository:
    ```
    helm repo update
    ```
* Step 3: Create namespace
    ```
    kubectl create namespace metallb-system
    ```
* Step 4: Install `Metallb` in `metallb-system` namespace
    ```
    helm install metallb metallb/metallb -n metallb-system
    ```
## Configuration
* Step1： Create an `IPAddressPool ` with IP address
    ```
    export INGRESS_IP_START=*************
    export INGRESS_IP_END=*************
    export EGRESS_INTERFACE=eno12399.1010

    envsubst <<"EOF" | kubectl apply -f -
    apiVersion: metallb.io/v1beta1
    kind: IPAddressPool
    metadata:
      name: ingress-public-ip
      namespace: metallb-system
    spec:
      addresses:
        - ${INGRESS_IP_START}-${INGRESS_IP_END}
    ---
    apiVersion: metallb.io/v1beta1
    kind: L2Advertisement
    metadata:
      name: ingress-public-ip
      namespace: metallb-system
    spec:
      interfaces:
      - ${EGRESS_INTERFACE}
      ipAddressPools:
      - ingress-public-ip
    EOF
    ```
* Step2: Check the external ip address of the ingress controller
    ```
    # kubectl get svc -n ingress-nginx
    NAME                                 TYPE           CLUSTER-IP      EXTERNAL-IP       PORT(S)                      AGE
    ingress-nginx-controller             LoadBalancer   *************   ***************   80:31334/TCP,443:31980/TCP   3d18h
    ingress-nginx-controller-admission   ClusterIP      *************   <none>            443/TCP                      3d18h
    ```