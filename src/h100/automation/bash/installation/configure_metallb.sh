#!/bin/bash

# Function to configure MetalLB
configure_metallb() {
    echo "Configuring MetalLB..."
    
    # Read configuration values
    echo "Reading configuration values..."
    INGRESS_IP_START=$(read_yaml_value '.metallb.ingress-ip-start')
    if [ $? -ne 0 ] || [ -z "$INGRESS_IP_START" ]; then
        echo "Failed to read ingress-ip-start from config"
        return 1
    fi
    
    INGRESS_IP_END=$(read_yaml_value '.metallb.ingress-ip-end')
    if [ $? -ne 0 ] || [ -z "$INGRESS_IP_END" ]; then
        echo "Failed to read ingress-ip-end from config"
        return 1
    fi
    
    EGRESS_INTERFACE=$(read_yaml_value '.metallb.egress-interface')
    if [ $? -ne 0 ] || [ -z "$EGRESS_INTERFACE" ]; then
        echo "Failed to read egress-interface from config"
        return 1
    fi
    
    echo "Using configuration:"
    echo "  Ingress IP range: $INGRESS_IP_START - $INGRESS_IP_END"
    echo "  Egress interface: $EGRESS_INTERFACE"
    
    # Validate interface on all nodes
    echo "Checking interface $EGRESS_INTERFACE on all nodes..."
    cat <<'EOF' | kubectl apply -f -
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: interface-check
  namespace: metallb-system
spec:
  selector:
    matchLabels:
      app: interface-check
  template:
    metadata:
      labels:
        app: interface-check
    spec:
      hostNetwork: true
      containers:
      - name: interface-check
        image: busybox
        command:
        - /bin/sh
        - -c
        - |
          if ! ip link show $EGRESS_INTERFACE > /dev/null 2>&1; then
            echo "Interface $EGRESS_INTERFACE not found on node $(hostname)"
            exit 1
          fi
          if ! ip link show $EGRESS_INTERFACE | grep -q "UP"; then
            echo "Interface $EGRESS_INTERFACE is not UP on node $(hostname)"
            exit 1
          fi
          echo "Interface check passed on node $(hostname)"
      terminationGracePeriodSeconds: 0
EOF
    
    # Wait for interface check to complete
    sleep 5
    local failed_pods=$(kubectl get pods -n metallb-system -l app=interface-check --field-selector status.phase=Failed -o name)
    if [ ! -z "$failed_pods" ]; then
        echo "ERROR: Interface check failed on some nodes"
        kubectl logs -n metallb-system -l app=interface-check
        kubectl delete ds interface-check -n metallb-system
        return 1
    fi
    
    kubectl delete ds interface-check -n metallb-system
    
    # Check IP availability
    echo "Checking IP range availability on all nodes..."
    cat <<EOF | kubectl apply -f -
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: ip-check
  namespace: metallb-system
spec:
  selector:
    matchLabels:
      app: ip-check
  template:
    metadata:
      labels:
        app: ip-check
    spec:
      hostNetwork: true
      containers:
      - name: ip-check
        image: busybox
        command:
        - /bin/sh
        - -c
        - |
          for ip in \$(seq -f "$INGRESS_IP_START" 1 $INGRESS_IP_END); do
            if arping -c 1 -w 1 -I $EGRESS_INTERFACE \$ip > /dev/null 2>&1; then
              echo "WARNING: IP \$ip is in use on node \$(hostname)"
              exit 1
            fi
          done
          echo "All IPs are available on node \$(hostname)"
      terminationGracePeriodSeconds: 0
EOF
    
    # Wait for IP check to complete
    sleep 5
    failed_pods=$(kubectl get pods -n metallb-system -l app=ip-check --field-selector status.phase=Failed -o name)
    if [ ! -z "$failed_pods" ]; then
        echo "ERROR: IP conflict detected on some nodes"
        kubectl logs -n metallb-system -l app=ip-check
        kubectl delete ds ip-check -n metallb-system
        return 1
    fi
    
    kubectl delete ds ip-check -n metallb-system
    
    # Apply MetalLB configuration
    echo "Applying MetalLB configuration..."
    cat <<EOF | kubectl apply -f -
apiVersion: metallb.io/v1beta1
kind: IPAddressPool
metadata:
    name: ingress-public-ip
    namespace: metallb-system
spec:
    addresses:
    - ${INGRESS_IP_START}-${INGRESS_IP_END}
---
apiVersion: metallb.io/v1beta1
kind: L2Advertisement
metadata:
    name: ingress-public-ip
    namespace: metallb-system
spec:
    interfaces:
    - ${EGRESS_INTERFACE}
    ipAddressPools:
    - ingress-public-ip
EOF
    
    if [ $? -ne 0 ]; then
        echo "ERROR: Failed to apply MetalLB configuration"
        return 1
    fi
    
    echo "MetalLB configuration completed successfully"
    return 0
}
