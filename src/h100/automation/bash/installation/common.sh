#!/bin/bash

# Function to check the command result and capture errors
check_command() {
    if [ $? -ne 0 ]; then
        echo "ERROR: $1 failed."
        exit 1
    else
        echo "$1 succeeded."
    fi
}


# Function to check kubernetes cluster status
check_k8s_cluster() {
    # Check if kubectl is installed
    if ! command -v kubectl &> /dev/null; then
        echo "kubectl not found, installing..."
        curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
        chmod +x kubectl
        sudo mv kubectl /usr/local/bin/
        check_command "kubectl installation"
    else
        echo "kubectl is installed, version: $(kubectl version --client --short)"
    fi

    # Use custom kubeconfig if specified in the config file
    KUBECONFIG=$(read_yaml_value '.kubeconfig')
    if [ $? -eq 0 ] && [ ! -z "$KUBECONFIG" ] && [ "$KUBECONFIG" != "null" ]; then
        if [ ! -f "${KUBECONFIG}" ]; then
            echo "Error: specified kubeconfig file does not exist: ${KUBECONFIG}"
            exit 1
        fi
        echo "Using kubeconfig from config file: ${KUBECONFIG}"
        export KUBECONFIG="${KUBECONFIG}"
        # Ensure helm uses the same kubeconfig
        export HELM_KUBECONFIG="${KUBECONFIG}"
    fi

    echo "Checking Kubernetes cluster status..."
    kubectl get node
    check_command "Kubernetes node status check"
    kubectl get pod -A
    check_command "Kubernetes pod status check"
}

# Function to install Helm
install_helm() {
    # First check if helm is already installed
    if command -v helm &> /dev/null; then
        echo "Helm is already installed, version: $(helm version --short)"
        # Verify helm is working
        helm list &> /dev/null
        check_command "Helm availability check"
        return 0
    fi

    echo "Installing Helm..."
    curl -fsSL -o get_helm.sh https://raw.githubusercontent.com/helm/helm/master/scripts/get-helm-3
    check_command "Download Helm script"
    chmod 700 get_helm.sh
    ./get_helm.sh
    check_command "Helm installation"

    # Verify helm is working after installation
    helm list &> /dev/null
    check_command "Post-installation helm availability check"
}


# Function to show usage
show_usage() {
    echo "Usage: $0 [-a|--auto] [-h|--help]"
    echo "Options:"
    echo "  -a, --auto          Enable automatic mode for component installation"
    echo "  -h, --help          Show this help message"
}

# Function to prompt user for component installation
prompt_component() {
    local component=$1
    local description=$2
    local default=${3:-n}
    
    local prompt_char="y/N"
    if [ "$default" = "y" ]; then
        prompt_char="Y/n"
    fi
    
    read -p "Install $description? [$prompt_char] " choice
    choice=${choice:-$default}
    
    case "$choice" in
        [Yy]*)
            return 0
            ;;
        *)
            return 1
            ;;
    esac
}

# Function to ensure yq is installed and read yaml config
read_yaml_value() {
    local key=$1
    local input_file=${2:-"inputs-installation.yaml"}
    
    # Install yq if needed
    if ! command -v yq &> /dev/null; then
        echo "Installing yq (required for configuration)..." >&2
        if ! sudo wget -q "https://github.com/mikefarah/yq/releases/download/v4.40.5/yq_linux_amd64" -O /usr/local/bin/yq; then
            echo "Failed to download yq" >&2
            return 1
        fi
        if ! sudo chmod +x /usr/local/bin/yq; then
            echo "Failed to make yq executable" >&2
            return 1
        fi
        echo "yq installed successfully" >&2
    fi
    
    # Read and return the value, redirecting stderr to avoid version info output
    yq e "$key" "$input_file" 2>/dev/null
}

# Function to ensure required tools are installed
ensure_dependencies() {
    local deps=("$@")
    local missing=()
    
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            missing+=("$dep")
        fi
    done

    if [[ ${#missing[@]} -eq 0 ]]; then
        return 0
    fi

    echo "Installing missing dependencies: ${missing[*]}"
    if [[ -f /etc/debian_version ]]; then
        sudo apt-get update && sudo apt-get install -y "${missing[@]}"
        check_command "Install dependencies: ${missing[*]}"
    elif [[ -f /etc/redhat-release ]]; then
        sudo yum install -y "${missing[@]}"
        check_command "Install dependencies: ${missing[*]}"
    else
        echo "ERROR: Unsupported OS for package installation"
        return 1
    fi
}
