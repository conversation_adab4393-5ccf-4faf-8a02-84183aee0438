#!/bin/bash

# Source common functions
source ./common.sh

set -e

# Function to cleanup on error
cleanup() {
    echo "Error occurred during installation. Cleaning up..."
    # Add cleanup steps here if needed
    exit 1
}

# Set trap for cleanup
trap cleanup ERR

# Entry point to the script
main() {
    # Parse command line arguments
    INTERACTIVE_MODE=true  # Default to interactive mode
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -a|--auto)  # Short and long option for automatic mode
                INTERACTIVE_MODE=false
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                echo "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done

    echo "This script will install the complete stack. Please make sure you have:"
    echo "1. A working Kubernetes cluster"
    echo "2. Sufficient resources for all components"
    echo "3. Required network access"
    echo
    read -p "Continue with installation? (y/N) " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Installation cancelled."
        exit 1
    fi

    echo "Starting installation..."
    
    # These components are required and will always be installed
    echo "Installing required base components..."
    check_k8s_cluster
    install_helm
    
    # Define components in correct order
    components=(
        "install_nvidia_driver:NVIDIA Driver"
        "clear_disks:Disk Cleanup"
        "install_ceph:Ceph Storage System"
        "create_ceph_bucket:Ceph Object Store Bucket"
        "install_metallb:MetalLB Load Balancer"
        "configure_metallb:MetalLB Configuration"
        "install_ingress_nginx:Ingress-Nginx"
        "install_keycloak:Keycloak Authentication"
        "configure_keycloak:Keycloak Configuration"
        "install_oauth2_proxy:OAuth2 Proxy"
        # "configure_dns:DNS Configuration"
        "install_postgresql:PostgreSQL Database"
        "install_qdrant:Qdrant Vector Database"
        "install_rabbitmq:RabbitMQ Message Queue"
        "create_huggingface_secret:Hugging Face Secret"
        "deploy_llama_3_1_8b:Llama-3.1-8B Model"
        "deploy_bge_m3:BGE-M3 Embedding Model"
        # "install_prometheus:Prometheus Monitoring"
        # "install_grafana:Grafana Visualization"
    )
    
    if [ "$INTERACTIVE_MODE" = true ]; then
        echo -e "\nSelect components to install:"
        for component in "${components[@]}"; do
            IFS=':' read -r func desc <<< "$component"
            # Source the corresponding module before executing the function
            source ./${func}.sh
            if prompt_component "$func" "$desc"; then
                echo "Executing $desc installation..."
                $func
            else
                echo "Skipping $desc installation"
            fi
        done
    else
        echo "Running in automatic mode - installing all components"
        for component in "${components[@]}"; do
            IFS=':' read -r func desc <<< "$component"
            # Source the corresponding module before executing the function
            source ./${func}.sh
            echo "Installing $desc..."
            $func
        done
    fi
    
    echo "Installation complete. Follow the GETTING-STARTED.md to deploy an RAG application."
}

main "$@"