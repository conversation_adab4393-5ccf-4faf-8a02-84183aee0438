#!/bin/bash

# Function to safely clear all non-system disks on Kubernetes nodes
clear_disks() {
    # Show warning and get confirmation
    echo "WARNING: This operation will PERMANENTLY ERASE ALL DATA on non-system disks!"
    echo "This includes:"
    echo "  - All partitions and partition tables"
    echo "  - All LVM configurations"
    echo "  - All data on these disks"
    read -p "Are you sure you want to continue? (y/N) " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Operation cancelled by user."
        return 1
    fi

    echo "Starting disk cleanup process..."
    
    # Create DaemonSet for disk cleanup
    cat <<EOF | kubectl apply -f -
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: disk-cleanup
  namespace: default
spec:
  selector:
    matchLabels:
      app: disk-cleanup
  template:
    metadata:
      labels:
        app: disk-cleanup
    spec:
      hostPID: true
      hostNetwork: true
      containers:
      - name: disk-cleanup
        image: ubuntu:latest
        securityContext:
          privileged: true
        command: ["/bin/bash", "-c"]
        args:
        - |
          # Install required tools (without systemd)
          DEBIAN_FRONTEND=noninteractive apt-get update -qq
          DEBIAN_FRONTEND=noninteractive apt-get install -y -qq --no-install-recommends gdisk util-linux lvm2 parted
          
          echo "[1/4] Analyzing current disk layout..."
          lsblk
          
          # Identify system disk to protect it
          echo "[2/4] Identifying system disk..."
          ROOT_DEVICE=\$(chroot /host findmnt -n -o SOURCE / 2>/dev/null || echo "/dev/\$(lsblk -no pkname \$(findmnt -n -o SOURCE /host))")
          echo "Root device: \$ROOT_DEVICE"
          
          # Handle both LVM and non-LVM configurations
          if [[ \$ROOT_DEVICE == *"/dev/mapper"* ]]; then
              echo "Detected LVM configuration"
              PART_NAME=\$(chroot /host dmsetup deps -o blkdevname \$ROOT_DEVICE | sed -E "s/.*\((.*)\)/\1/")
              SYSTEM_DISKS=\$(chroot /host lsblk -no pkname -p "/dev/\$PART_NAME" | grep "/dev/" | tail -n1)
          else
              SYSTEM_DISKS=\$(chroot /host lsblk -no pkname -p \$ROOT_DEVICE | grep "/dev/")
          fi
          
          if [ -z "\$SYSTEM_DISKS" ]; then
              echo "ERROR: Failed to identify system disk - aborting for safety"
              exit 1
          fi
          echo "Protected system disk: \$SYSTEM_DISKS"
          
          # Get list of non-system disks to process
          echo "[3/4] Identifying disks to process..."
          DISKS_TO_PROCESS=\$(lsblk -nd -o NAME,TYPE,SIZE | grep "disk" | \
              while read name type size; do
                  if [ "/dev/\$name" != "\$SYSTEM_DISKS" ] && [[ ! "\$name" =~ ^nbd ]]; then
                      echo "\$name"
                  fi
              done)
          
          DISK_COUNT=\$(echo "\$DISKS_TO_PROCESS" | wc -l)
          
          if [ \$DISK_COUNT -eq 0 ]; then
              echo "No non-system disks found to process"
              exit 0
          fi
          
          echo "Found \$DISK_COUNT disk(s) to process:"
          echo "\$DISKS_TO_PROCESS" | while read disk; do
              echo "  - /dev/\$disk \$(lsblk -dn -o SIZE /dev/\$disk)"
          done
          
          echo "[4/4] Processing non-system disks..."
          CURRENT_DISK=0
          
          for device in \$DISKS_TO_PROCESS; do 
              CURRENT_DISK=\$((CURRENT_DISK + 1))
              FULL_PATH="/dev/\$device"
              
              echo "Processing disk \$CURRENT_DISK/\$DISK_COUNT: \$FULL_PATH"
              
              if [ ! -e "\$FULL_PATH" ]; then
                  echo "Warning: Device \$FULL_PATH not found, skipping"
                  continue
              fi
              
              echo "Clearing disk: \$FULL_PATH"
              
              # Step 1: Clear partition table and signatures
              echo "  - Wiping partition table..."
              sgdisk --zap-all "\$FULL_PATH"
              
              # Step 2: Discard blocks if supported
              echo "  - Discarding blocks (if supported)..."
              blkdiscard "\$FULL_PATH" 2>/dev/null || true
              
              # Step 3: Update kernel partition table
              echo "  - Updating kernel partition table..."
              partprobe "\$FULL_PATH"
              
              if [ \$? -ne 0 ]; then 
                  echo "ERROR: Failed to clear \$FULL_PATH"
                  exit 1
              fi
              echo "Successfully cleared \$FULL_PATH"
          done
          
          echo "Disk cleanup completed successfully on node \$(hostname)"
          # Sleep briefly to ensure logs are captured
          sleep 5
          exit 0
        volumeMounts:
        - name: host
          mountPath: /host
        - name: dev
          mountPath: /dev
        - name: run
          mountPath: /run
      volumes:
      - name: host
        hostPath:
          path: /
      - name: dev
        hostPath:
          path: /dev
      - name: run
        hostPath:
          path: /run
      tolerations:
      - operator: Exists
EOF

    echo "Waiting for disk cleanup DaemonSet to complete..."
    
    # Wait for pods to be created
    sleep 10
    
    # Monitor the progress
    while true; do
        TOTAL_NODES=$(kubectl get nodes --no-headers | wc -l)
        COMPLETED_PODS=$(kubectl get pods -l app=disk-cleanup --no-headers | grep -c "Completed" || true)
        FAILED_PODS=$(kubectl get pods -l app=disk-cleanup --no-headers | grep -c "Error\|Failed" || true)
        RUNNING_PODS=$(kubectl get pods -l app=disk-cleanup --no-headers | grep -c "Running" || true)
        
        echo "Progress: $COMPLETED_PODS/$TOTAL_NODES nodes completed, $RUNNING_PODS running, $FAILED_PODS failed"
        
        if [ $FAILED_PODS -gt 0 ]; then
            echo "ERROR: Some pods failed during disk cleanup"
            echo "----------------------------------------"
            for pod in $(kubectl get pods -l app=disk-cleanup -o name); do
                node=$(kubectl get $pod -o jsonpath='{.spec.nodeName}')
                status=$(kubectl get $pod -o jsonpath='{.status.phase}')
                echo "Node: $node (Status: $status)"
                echo "----------------------------------------"
                kubectl logs $pod
                echo "----------------------------------------"
            done
            kubectl delete ds disk-cleanup
            return 1
        fi
        
        if [ $COMPLETED_PODS -eq $TOTAL_NODES ]; then
            echo "All nodes completed disk cleanup successfully"
            break
        fi
        
        sleep 5
    done
    
    # Show logs from all pods
    echo "Disk cleanup logs:"
    echo "----------------------------------------"
    for pod in $(kubectl get pods -l app=disk-cleanup -o name); do
        node=$(kubectl get $pod -o jsonpath='{.spec.nodeName}')
        echo "Logs from node: $node"
        echo "----------------------------------------"
        kubectl logs $pod
        echo "----------------------------------------"
    done
    
    # Cleanup
    kubectl delete ds disk-cleanup
    
    echo "Disk cleanup completed successfully on all nodes"
} 