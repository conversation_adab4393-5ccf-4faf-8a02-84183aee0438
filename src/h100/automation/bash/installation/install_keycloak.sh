#!/bin/bash

# Function to install Keycloak
install_keycloak() {
    echo "Installing Keycloak..."
    
    # Read configuration values
    local KEYCLOAK_HOST=$(read_yaml_value '.keycloak.host')
    local KEYCLOAK_ADMIN_USER=$(read_yaml_value '.keycloak.adminUser')
    
    if [[ -z "$KEYCLOAK_HOST" ]]; then
        echo "ERROR: Keycloak host not configured in inputs-installation.yaml"
        return 1
    fi
    
    if [[ -z "$KEYCLOAK_ADMIN_USER" ]]; then
        echo "ERROR: Keycloak admin user not configured in inputs-installation.yaml"
        return 1
    fi
    
    # Check if namespace exists and clean up if needed
    if kubectl get namespace keycloak >/dev/null 2>&1; then
        echo "Found existing keycloak namespace, cleaning up..."
        kubectl delete ingress -n keycloak --all
        kubectl delete service -n keycloak --all
        helm uninstall keycloak -n keycloak
        kubectl delete namespace keycloak
    fi

    # Create namespace
    kubectl create namespace keycloak

    # Prepare values file
    cat > keycloak-values.yml <<EOF
# Run in production mode behind NGINX proxy
production: true
proxyHeaders: xforwarded

# Admin user
auth:
  adminUser: ${KEYCLOAK_ADMIN_USER}

# Ingress config
ingress:
  enabled: true
  ingressClassName: "nginx"
  pathType: Prefix
  annotations:
    nginx.ingress.kubernetes.io/proxy-buffers-number: "4"
    nginx.ingress.kubernetes.io/proxy-buffer-size: "16k"
  hostname: ${KEYCLOAK_HOST}
  tls: true
  extraTls:
    - hosts:
        - ${KEYCLOAK_HOST}
EOF

    # Add Bitnami repo if not exists
    if ! helm repo list | grep -q "bitnami"; then
        helm repo add bitnami https://charts.bitnami.com/bitnami
        helm repo update
    fi

    # Install Keycloak
    if ! helm install keycloak bitnami/keycloak \
        -f keycloak-values.yml \
        --namespace keycloak \
        --wait --timeout 10m; then
        echo "ERROR: Keycloak installation failed"
        return 1
    fi

    # Verify installation
    verify_keycloak_installation "${KEYCLOAK_HOST}"
}

# Function to verify Keycloak installation
verify_keycloak_installation() {
    local KEYCLOAK_HOST="$1"
    echo "Verifying Keycloak installation..."

    # Verify TLS configuration
    if ! kubectl get ingress -n keycloak keycloak -o jsonpath='{.spec.tls[0].hosts[0]}' | grep -q "${KEYCLOAK_HOST}"; then
        echo "ERROR: TLS configuration verification failed"
        echo "Current ingress configuration:"
        kubectl get ingress -n keycloak keycloak -o yaml
        return 1
    fi

    # Get admin password
    local ADMIN_PASSWORD=$(kubectl get secret keycloak -o jsonpath='{.data.admin-password}' -n keycloak | base64 -d)
    if [[ -z "$ADMIN_PASSWORD" ]]; then
        echo "ERROR: Could not retrieve admin password"
        return 1
    fi

    # Get Keycloak Ingress IP
    local INGRESS_IP=$(kubectl get ingress -n keycloak keycloak -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
    if [[ -z "$INGRESS_IP" ]]; then
        echo "ERROR: Could not get Keycloak Ingress IP"
        return 1
    fi

    # Wait for Keycloak to be accessible
    echo "Checking Keycloak availability..."
    for i in {1..30}; do
        if curl -sk -o /dev/null -w "%{http_code}" "https://${INGRESS_IP}/realms/master/.well-known/openid-configuration" \
            --resolve "${KEYCLOAK_HOST}:443:${INGRESS_IP}" \
            -H "Host: ${KEYCLOAK_HOST}" | grep -q "200"; then
            echo "Keycloak is accessible and responding"
            break
        fi
        if [ $i -eq 30 ]; then
            echo "ERROR: Keycloak is not responding"
            return 1
        fi
        echo "Waiting for Keycloak to be accessible... ($i/30)"
        sleep 10
    done

    echo "Keycloak installation verified successfully"
    echo "Admin password: $ADMIN_PASSWORD"
    echo "Ingress IP: $INGRESS_IP"
    return 0
}
