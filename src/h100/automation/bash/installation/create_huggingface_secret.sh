#!/bin/bash

# Function to create Hugging Face Secret
create_huggingface_secret() {
    echo "Creating Hugging Face Secret..."
    
    # Read Hugging Face API token from configuration file
    local HF_API_TOKEN=$(read_yaml_value '.hfApiToken')
    
    # Check if the token is empty
    if [ -z "$HF_API_TOKEN" ]; then
        echo "ERROR: Hugging Face API token is not set in the configuration file."
        return 1
    fi
    
    # Delete existing secret if it exists
    kubectl delete secret hf-secret --namespace=default --ignore-not-found
    
    # Create the secret directly
    kubectl create secret generic hf-secret \
        --from-literal=hfApiToken="$HF_API_TOKEN" \
        --namespace=default
    
    check_command "Create Hugging Face Secret"
}
