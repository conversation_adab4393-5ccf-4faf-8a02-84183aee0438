#!/bin/bash

# Function to deploy bge-m3
deploy_bge_m3() {
    echo "Deploying bge-m3..."

    # Cleanup existing deployment
    kubectl delete deployment bge-m3 --namespace=default --ignore-not-found
    kubectl delete service bge-m3 --namespace=default --ignore-not-found
    kubectl delete pvc bge-m3 --namespace=default --ignore-not-found

    # Create PersistentVolumeClaim
    kubectl apply -f - <<EOF
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: bge-m3
  namespace: default
spec:
  accessModes:
  - ReadWriteMany
  resources:
    requests:
      storage: 5Gi
  volumeMode: Filesystem
EOF

    # Create Deployment
    kubectl apply -f - <<EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: bge-m3
  namespace: default
  labels:
    app: bge-m3
spec:
  replicas: 1
  selector:
    matchLabels:
      app: bge-m3
  template:
    metadata:
      labels:
        app: bge-m3
    spec:
      volumes:
      - name: cache-volume
        persistentVolumeClaim:
          claimName: bge-m3
      - name: shm
        emptyDir:
          medium: Memory
      containers:
      - name: llama
        image: vllm/vllm-openai:latest
        command: ["/bin/sh", "-c"]
        args: ["vllm serve BAAI/bge-m3 --trust-remote-code --task embedding"]
        env:
        - name: HUGGING_FACE_HUB_TOKEN
          valueFrom:
            secretKeyRef:
              name: hf-secret
              key: hfApiToken
        ports:
        - containerPort: 8000
        resources:
          limits:
            cpu: "16"
            nvidia.com/gpu: "1"
          requests:
            cpu: "6"
            memory: "6Gi"
            nvidia.com/gpu: "1"
        volumeMounts:
        - mountPath: /root/.cache/huggingface
          name: cache-volume
        - mountPath: /dev/shm
          name: shm
EOF

    # Create Service for bge-m3
    kubectl apply -f - <<EOF
apiVersion: v1
kind: Service
metadata:
  name: bge-m3
  namespace: default
spec:
  ports:
  - nodePort: 30125
    port: 8000
    protocol: TCP
    targetPort: 8000
  selector:
    app: bge-m3
  type: NodePort
EOF

    # Verify deployment
    echo "Verifying bge-m3 deployment..."
    if ! kubectl wait --for=condition=available --timeout=300s deployment/bge-m3 -n default; then
        echo "ERROR: bge-m3 deployment not ready"
        return 1
    fi

    # Check if the model is serving successfully by checking logs
    echo "Waiting for the bge-m3 model to start serving..."
    for i in {1..30}; do
        if kubectl logs deployment/bge-m3 -n default | grep -q "Application startup complete."; then
            echo "bge-m3 model is serving successfully."
            break
        fi
        if [ $i -eq 30 ]; then
            echo "ERROR: bge-m3 model did not start serving in time."
            return 1
        fi
        echo "Waiting for bge-m3 model to start... ($i/30)"
        sleep 10
    done

    # Port forward to access the model service
    echo "Setting up port forwarding to access the bge-m3 model service..."
    kubectl port-forward deployment/bge-m3 -n default 0:8000 > port_forward_bge.log 2>&1 &
    PORT_FORWARD_PID=$!

    # Wait for a moment to ensure port forwarding is established
    sleep 5

    # Get the randomly assigned port from the output log
    RANDOM_PORT=$(grep -oP 'Forwarding from 127.0.0.1:\K[0-9]+' port_forward_bge.log | tail -n 1)

    # Test API call to the bge-m3 model
    echo "Testing API call to the bge-m3 model..."
    TEST_DATA='{"model": "BAAI/bge-m3", "input": "Hello, how are you?"}'  # Example input for the embedding model
    if ! curl -s -X POST -H "Content-Type: application/json" -d "$TEST_DATA" "http://localhost:$RANDOM_PORT/v1/embeddings" | grep -q "data"; then
        echo "ERROR: API call to the bge-m3 model failed"
        kill $PORT_FORWARD_PID
        return 1
    fi

    echo "API call to the BAAI/bge-m3 model succeeded."

    # Cleanup port forwarding
    kill $PORT_FORWARD_PID
}
