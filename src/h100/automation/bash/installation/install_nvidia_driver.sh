#!/bin/bash

# Function to install NVIDIA Driver
install_nvidia_driver() {
    echo "Starting NVIDIA Driver installation process..."
    
    # Ensure required dependencies
    if ! ensure_dependencies jq; then
        echo "ERROR: Failed to install required dependencies"
        return 1
    fi
    
    # Function to verify GPU setup
    verify_gpu_setup() {
        echo "Verifying GPU Operator status..."
        # Wait longer for the validator pods to be created
        sleep 30
        
        # Check if validator pods exist before waiting
        if ! kubectl get pod -l app=nvidia-operator-validator -n nvidia-gpu-operator &> /dev/null; then
            echo "ERROR: GPU Operator validator pods not found"
            return 1
        fi

        if ! kubectl wait --for=condition=ready pod -l app=nvidia-operator-validator -n nvidia-gpu-operator --timeout=300s; then
            echo "ERROR: GPU Operator validator is not ready"
            return 1
        fi

        echo "Verifying node GPU resources..."
        if ! kubectl get nodes -o json | jq -r '.items[].status.allocatable."nvidia.com/gpu"' | grep -q "[0-9]"; then
            echo "ERROR: No available GPU resources detected"
            return 1
        fi

        echo "Running nvidia-smi test..."
        if ! kubectl run nvidia-smi --rm -i --tty --restart=Never \
            --image=nvidia/cuda:11.6.2-base-ubuntu20.04 \
            --command -- nvidia-smi; then
            echo "ERROR: nvidia-smi test failed"
            return 1
        fi

        echo "GPU verification completed: All tests passed"
        return 0
    }

    # Function to cleanup GPU operator
    cleanup_gpu_operator() {
        echo "Cleaning up GPU operator installation..."
        
        # Delete helm release if exists
        if helm list -n nvidia-gpu-operator | grep -q "gpu-operator"; then
            echo "Removing existing Helm release..."
            helm uninstall -n nvidia-gpu-operator $(helm list -n nvidia-gpu-operator -q)
            sleep 10
        fi
        
        # Delete namespace if exists
        if kubectl get namespace nvidia-gpu-operator &> /dev/null; then
            echo "Removing nvidia-gpu-operator namespace..."
            kubectl delete namespace nvidia-gpu-operator --timeout=60s
            # Wait for namespace deletion
            for i in {1..30}; do
                if ! kubectl get namespace nvidia-gpu-operator &> /dev/null; then
                    break
                fi
                echo "Waiting for namespace deletion... ($i/30)"
                sleep 2
            done
        fi
        
        # Clean up any remaining CRDs
        echo "Cleaning up NVIDIA CRDs..."
        kubectl get crd | grep -i nvidia | awk '{print $1}' | xargs -r kubectl delete crd
        
        # Clean up cluster-wide resources
        echo "Cleaning up cluster-wide resources..."
        kubectl delete clusterrole gpu-operator --ignore-not-found
        kubectl delete clusterrolebinding gpu-operator --ignore-not-found
        
        # Clean up any remaining resources with the gpu-operator label
        echo "Cleaning up resources with gpu-operator label..."
        kubectl delete clusterrole -l app.kubernetes.io/instance=gpu-operator --ignore-not-found
        kubectl delete clusterrolebinding -l app.kubernetes.io/instance=gpu-operator --ignore-not-found
        kubectl delete serviceaccount -l app.kubernetes.io/instance=gpu-operator -n nvidia-gpu-operator --ignore-not-found
        
        sleep 10
        echo "Cleanup completed"
    }

    # Check if GPU operator is already installed
    if kubectl get pods -n nvidia-gpu-operator 2>/dev/null | grep -q "gpu-operator"; then
        echo "Detected existing NVIDIA GPU Operator installation, verifying status..."
        
        if verify_gpu_setup; then
            echo "Existing GPU Operator is functioning correctly, skipping installation"
            return 0
        else
            echo "Existing GPU Operator validation failed, preparing for reinstallation..."
            cleanup_gpu_operator
        fi
    fi

    # Add and update Helm repository
    echo "Adding NVIDIA Helm repository..."
    helm repo add nvidia https://helm.ngc.nvidia.com/nvidia
    check_command "Add NVIDIA Helm repository"
    
    echo "Updating Helm repositories..."
    helm repo update
    check_command "Update Helm repositories"

    # Install GPU operator
    echo "Installing NVIDIA GPU Operator..."
    cleanup_gpu_operator  # Ensure clean state before installation
    
    helm install --wait --generate-name nvidia/gpu-operator \
        --namespace nvidia-gpu-operator \
        --create-namespace \
        --version=v24.9.1 \
        --set driver.enabled=true \
        --timeout 15m
    check_command "Install NVIDIA GPU Operator"

    # Post-installation verification with retry
    echo "Performing post-installation verification..."
    for i in {1..3}; do
        echo "Verification attempt $i/3..."
        sleep 30  # Wait between attempts
        if verify_gpu_setup; then
            echo "NVIDIA Driver and GPU Operator installation and verification completed successfully"
            return 0
        fi
        echo "Verification attempt $i failed, retrying..."
    done

    echo "ERROR: Post-installation GPU verification failed after 3 attempts"
    return 1
} 