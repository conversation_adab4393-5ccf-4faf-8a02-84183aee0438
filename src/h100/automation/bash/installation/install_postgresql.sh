#!/bin/bash

# Function to install PostgreSQL
install_postgresql() {
    echo "Installing PostgreSQL..."
    
    # Read password from configuration file
    local postgres_password=$(read_yaml_value '.postgresql.password')
    
    # Cleanup existing installation
    helm uninstall postgresql -n postgresql 2>/dev/null || true
    kubectl delete namespace postgresql --wait=false 2>/dev/null || true
    kubectl wait --for=delete namespace/postgresql --timeout=60s 2>/dev/null || true
    
    # Install PostgreSQL
    helm install postgresql oci://registry-1.docker.io/bitnamicharts/postgresql \
        --namespace postgresql \
        --create-namespace \
        --set global.postgresql.auth.postgresPassword="$postgres_password"
    check_command "Install PostgreSQL"
    
    # Verify installation
    echo "Verifying PostgreSQL installation..."
    if ! kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=postgresql -n postgresql --timeout=300s; then
        echo "ERROR: PostgreSQL pods not ready"
        return 1
    fi
    
    # Test connection
    local POD_NAME=$(kubectl get pods -n postgresql -l app.kubernetes.io/name=postgresql -o jsonpath='{.items[0].metadata.name}')
    if ! kubectl exec -n postgresql $POD_NAME -- pg_isready -U postgres; then
        echo "ERROR: Cannot connect to PostgreSQL"
        return 1
    fi
}
