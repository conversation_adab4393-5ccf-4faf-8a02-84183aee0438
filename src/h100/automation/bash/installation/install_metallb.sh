
#!/bin/bash

# Function to install MetalLB
install_metallb() {
    echo "Installing MetalLB..."

    # Cleanup function for MetalLB resources
    cleanup_metallb() {
        echo "Cleaning up existing MetalLB installation..."
        
        # Remove finalizers from resources
        for resource in $(kubectl get -n metallb-system $(kubectl api-resources --namespaced=true --output=name | grep metallb.io) -o name 2>/dev/null); do
            kubectl patch -n metallb-system $resource -p '{"metadata":{"finalizers":[]}}' --type=merge 2>/dev/null || true
        done
        
        # Delete MetalLB namespace if exists
        if kubectl get namespace metallb-system &>/dev/null; then
            echo "Removing metallb-system namespace..."
            kubectl delete namespace metallb-system --timeout=60s
            # Wait for namespace deletion
            for i in {1..30}; do
                if ! kubectl get namespace metallb-system &>/dev/null; then
                    break
                fi
                echo "Waiting for namespace deletion... ($i/30)"
                sleep 2
            done
        fi
        
        # Remove CRDs
        echo "Removing MetalLB CRDs..."
        kubectl get crd -o name | grep metallb.io | xargs -r kubectl delete --timeout=60s 2>/dev/null || true
        
        # Remove cluster-wide resources
        echo "Removing cluster-wide resources..."
        kubectl delete clusterrole -l app.kubernetes.io/name=metallb --ignore-not-found
        kubectl delete clusterrolebinding -l app.kubernetes.io/name=metallb --ignore-not-found
        
        # Remove Helm release if exists
        if helm list -n metallb-system 2>/dev/null | grep -q "metallb"; then
            echo "Removing Helm release..."
            helm uninstall metallb -n metallb-system 2>/dev/null || true
        fi
        
        echo "Cleanup completed"
        sleep 5
    }

    # Run cleanup first
    cleanup_metallb

    # Add MetalLB Helm repository
    echo "Adding MetalLB Helm repository..."
    helm repo add metallb https://metallb.github.io/metallb
    check_command "Add MetalLB repository"
    
    echo "Updating Helm repositories..."
    helm repo update
    check_command "Update Helm repositories"

    # Create namespace
    echo "Creating metallb-system namespace..."
    kubectl create namespace metallb-system
    check_command "Create MetalLB namespace"

    # Install MetalLB using Helm
    echo "Installing MetalLB via Helm..."
    helm install metallb metallb/metallb \
        --namespace metallb-system \
        --wait \
        --timeout 300s
    check_command "Install MetalLB"

    # Wait for MetalLB pods with increased timeout
    echo "Waiting for MetalLB pods to be ready..."
    for i in {1..10}; do
        if kubectl wait --namespace metallb-system \
            --for=condition=ready pod \
            --selector=app.kubernetes.io/name=metallb \
            --timeout=60s; then
            break
        fi
        if [ $i -eq 10 ]; then
            echo "ERROR: Timeout waiting for MetalLB pods"
            return 1
        fi
        echo "Retry $i/10: Waiting for pods..."
        sleep 30
    done
    check_command "Wait for MetalLB pods"

    # Verify installation
    echo "Verifying MetalLB installation..."
    
    # Check pods status
    if ! kubectl get pods -n metallb-system | grep -q "Running"; then
        echo "ERROR: MetalLB pods are not running properly"
        kubectl get pods -n metallb-system
        return 1
    fi

    # Check CRDs
    required_crds=("addresspools.metallb.io" "bfdprofiles.metallb.io" "bgppeers.metallb.io" "bgpadvertisements.metallb.io" "ipaddresspools.metallb.io" "l2advertisements.metallb.io")
    for crd in "${required_crds[@]}"; do
        if ! kubectl get crd | grep -q "$crd"; then
            echo "ERROR: Required CRD $crd is missing"
            return 1
        fi
    done

    # Check controller and speaker deployments
    if ! kubectl get deployment -n metallb-system metallb-controller &>/dev/null; then
        echo "ERROR: MetalLB controller deployment not found"
        return 1
    fi
    
    if ! kubectl get daemonset -n metallb-system metallb-speaker &>/dev/null; then
        echo "ERROR: MetalLB speaker daemonset not found"
        return 1
    fi

    echo "MetalLB installation and verification completed successfully"
    return 0
}
