#!/bin/bash

# Function to deploy Llama-3.1-8B
deploy_llama_3_1_8b() {
    echo "Deploying Llama-3.1-8B..."

    # Cleanup existing deployment
    kubectl delete deployment llama3-1 --namespace=default --ignore-not-found
    kubectl delete service llama3-1 --namespace=default --ignore-not-found
    kubectl delete pvc llama-3-1 --namespace=default --ignore-not-found

    # Create PersistentVolumeClaim
    kubectl apply -f - <<EOF
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: llama-3-1
  namespace: default
spec:
  accessModes:
  - ReadWriteMany
  resources:
    requests:
      storage: 50Gi
  volumeMode: Filesystem
EOF

    # Create Deployment
    kubectl apply -f - <<EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: llama3-1
  name: llama3-1
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: llama3-1
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: llama3-1
    spec:
      containers:
      - args:
        - vllm serve meta-llama/Llama-3.1-8B-Instruct --trust-remote-code --enable-chunked-prefill --max_num_batched_tokens 1024
        command:
        - /bin/sh
        - -c
        env:
        - name: HUGGING_FACE_HUB_TOKEN
          valueFrom:
            secretKeyRef:
              key: hfApiToken
              name: hf-secret
        image: vllm/vllm-openai:latest
        imagePullPolicy: Always
        name: llama
        ports:
        - containerPort: 8000
          protocol: TCP
        resources:
          limits:
            cpu: "16"
            nvidia.com/gpu: "1"
          requests:
            cpu: "6"
            memory: "6Gi"
            nvidia.com/gpu: "1"
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /root/.cache/huggingface
          name: cache-volume
        - mountPath: /dev/shm
          name: shm
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
      volumes:
      - name: cache-volume
        persistentVolumeClaim:
          claimName: llama-3-1
      - emptyDir:
          medium: Memory
        name: shm
EOF

    # Create Service
    kubectl apply -f - <<EOF
apiVersion: v1
kind: Service
metadata:
  labels:
    app: llama3-1
  name: llama3-1
  namespace: default
spec:
  ports:
  - name: vllm
    nodePort: 30122
    port: 8000
    protocol: TCP
    targetPort: 8000
  selector:
    app: llama3-1
  type: NodePort
EOF

    # Verify deployment
    echo "Verifying Llama-3.1-8B deployment..."
    if ! kubectl wait --for=condition=available --timeout=300s deployment/llama3-1 -n default; then
        echo "ERROR: Llama-3.1-8B deployment not ready"
        return 1
    fi

    # Check if the model is serving successfully by checking logs
    echo "Waiting for the model to start serving..."
    for i in {1..30}; do
        if kubectl logs deployment/llama3-1 -n default | grep -q "Application startup complete."; then
            echo "Llama-3.1-8B model is serving successfully."
            break
        fi
        if [ $i -eq 30 ]; then
            echo "ERROR: Model did not start serving in time."
            return 1
        fi
        echo "Waiting for model to start... ($i/30)"
        sleep 10
    done

    # Port forward to access the model service
    echo "Setting up port forwarding to access the model service..."
    kubectl port-forward deployment/llama3-1 -n default 0:8000 > port_forward.log 2>&1 &
    PORT_FORWARD_PID=$!

    # Wait for a moment to ensure port forwarding is established
    sleep 5

    # Get the randomly assigned port from the output log
    RANDOM_PORT=$(grep -oP 'Forwarding from 127.0.0.1:\K[0-9]+' port_forward.log | tail -n 1)

    # Test API call to the model
    echo "Testing API call to the model..."
    TEST_DATA='{"model": "meta-llama/Llama-3.1-8B-Instruct", "messages": [{"role": "user", "content": "Hello, how are you?"}], "max_tokens": 5}'  # Example input for the model
    if ! curl -s -X POST -H "Content-Type: application/json" -d "$TEST_DATA" "http://localhost:$RANDOM_PORT/v1/chat/completions" | grep -q "choices"; then
        echo "ERROR: API call to the model failed"
        kill $PORT_FORWARD_PID
        return 1
    fi

    echo "API call to the meta-llama/Llama-3.1-8B-Instruct model succeeded."

    # Cleanup port forwarding
    kill $PORT_FORWARD_PID
}
