#!/bin/bash

# Function to install RabbitMQ
install_rabbitmq() {
    echo "Installing RabbitMQ..."
    
    # Read password from configuration file
    local rabbitmq_password=$(read_yaml_value '.rabbitmq.password')
    
    # Cleanup existing installation
    helm uninstall rabbitmq -n rabbitmq 2>/dev/null || true
    kubectl delete namespace rabbitmq --wait=false 2>/dev/null || true
    kubectl wait --for=delete namespace/rabbitmq --timeout=60s 2>/dev/null || true
    
    # Install RabbitMQ
    helm install rabbitmq oci://registry-1.docker.io/bitnamicharts/rabbitmq \
        --namespace rabbitmq \
        --create-namespace \
        --set auth.password="$rabbitmq_password" \
        --set resources.limits.memory=512Mi \
        --set resources.requests.memory=256Mi
    check_command "Install RabbitMQ"
    
    # Verify installation
    echo "Verifying RabbitMQ installation..."
    if ! kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=rabbitmq -n rabbitmq --timeout=300s; then
        echo "ERROR: RabbitMQ pods not ready"
        return 1
    fi
    
    # Test connection
    local POD_NAME=$(kubectl get pods -n rabbitmq -l app.kubernetes.io/name=rabbitmq -o jsonpath='{.items[0].metadata.name}')
    if ! kubectl exec -n rabbitmq $POD_NAME -- rabbitmq-diagnostics check_running; then
        echo "ERROR: Cannot connect to RabbitMQ"
        return 1
    fi
}
