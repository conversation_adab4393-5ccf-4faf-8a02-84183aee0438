#!/bin/bash

configure_keycloak() {
    echo "Configuring Keycloak..."
    
    # Ensure required dependencies
    if ! ensure_dependencies jq; then
        echo "ERROR: Failed to install required dependencies"
        return 1
    fi
    
    # Get configuration values
    local KEYCLOAK_HOST=$(read_yaml_value '.keycloak.host')
    local KEYCLOAK_ADMIN_USER=$(read_yaml_value '.keycloak.adminUser')
    local KEYCLOAK_REALM=$(read_yaml_value '.keycloak.realm')
    local CLIENT_ID=$(read_yaml_value '.keycloak.client.id')
    local REDIRECT_URI=$(read_yaml_value '.keycloak.client.redirectUris[0]')
    local MAPPER_NAME=$(read_yaml_value '.keycloak.client.audienceMapper.name')
    local MAPPER_AUDIENCE=$(read_yaml_value '.keycloak.client.audienceMapper.audience')
    local TEST_USER=$(read_yaml_value '.keycloak.testUser.username')
    local TEST_PASS=$(read_yaml_value '.keycloak.testUser.password')

    # Validate required values
    [[ -z "$KEYCLOAK_HOST" ]] && echo "ERROR: Keycloak host not configured" && return 1
    [[ -z "$KEYCLOAK_ADMIN_USER" ]] && echo "ERROR: Keycloak admin user not configured" && return 1
    [[ -z "$KEYCLOAK_REALM" ]] && echo "ERROR: Keycloak realm not configured" && return 1
    [[ -z "$CLIENT_ID" ]] && echo "ERROR: Client ID not configured" && return 1
    [[ -z "$REDIRECT_URI" ]] && echo "ERROR: Redirect URI not configured" && return 1
    [[ -z "$MAPPER_NAME" ]] && echo "ERROR: Audience mapper name not configured" && return 1
    [[ -z "$MAPPER_AUDIENCE" ]] && echo "ERROR: Audience mapper audience not configured" && return 1
    [[ -z "$TEST_USER" ]] && echo "ERROR: Test username not configured" && return 1
    [[ -z "$TEST_PASS" ]] && echo "ERROR: Test password not configured" && return 1

    # Get admin password
    local ADMIN_PASSWORD=$(kubectl get secret keycloak -o jsonpath='{.data.admin-password}' -n keycloak | base64 -d)
    if [[ -z "$ADMIN_PASSWORD" ]]; then
        echo "ERROR: Could not retrieve admin password"
        return 1
    fi

    # Get Ingress IP
    local INGRESS_IP=$(kubectl get ingress -n keycloak keycloak -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
    if [[ -z "$INGRESS_IP" ]]; then
        echo "ERROR: Could not get Keycloak Ingress IP"
        return 1
    fi
    
    # Get admin token
    local ADMIN_TOKEN=$(curl -sk \
        -d "client_id=admin-cli" \
        -d "username=${KEYCLOAK_ADMIN_USER}" \
        -d "password=${ADMIN_PASSWORD}" \
        -d "grant_type=password" \
        --resolve "${KEYCLOAK_HOST}:443:${INGRESS_IP}" \
        -H "Host: ${KEYCLOAK_HOST}" \
        "https://${KEYCLOAK_HOST}/realms/master/protocol/openid-connect/token" | jq -r '.access_token')

    if [[ -z "$ADMIN_TOKEN" || "$ADMIN_TOKEN" == "null" ]]; then
        echo "ERROR: Failed to get admin token"
        return 1
    fi

    # Check and delete existing realm
    echo "Checking for existing realm..."
    if curl -sk \
        -H "Authorization: Bearer ${ADMIN_TOKEN}" \
        --resolve "${KEYCLOAK_HOST}:443:${INGRESS_IP}" \
        -H "Host: ${KEYCLOAK_HOST}" \
        "https://${KEYCLOAK_HOST}/admin/realms/${KEYCLOAK_REALM}" | grep -q "realm"; then
        echo "Found existing realm, deleting..."
        if ! curl -sk \
            -X DELETE \
            -H "Authorization: Bearer ${ADMIN_TOKEN}" \
            --resolve "${KEYCLOAK_HOST}:443:${INGRESS_IP}" \
            -H "Host: ${KEYCLOAK_HOST}" \
            "https://${KEYCLOAK_HOST}/admin/realms/${KEYCLOAK_REALM}"; then
            echo "ERROR: Failed to delete existing realm"
            return 1
        fi
        echo "Existing realm deleted"
    fi

    # Create realm
    echo "Creating realm ${KEYCLOAK_REALM}..."
    if ! curl -sk \
        -X POST \
        -H "Authorization: Bearer ${ADMIN_TOKEN}" \
        -H "Content-Type: application/json" \
        --resolve "${KEYCLOAK_HOST}:443:${INGRESS_IP}" \
        -H "Host: ${KEYCLOAK_HOST}" \
        -d "{\"realm\":\"${KEYCLOAK_REALM}\",\"enabled\":true}" \
        "https://${KEYCLOAK_HOST}/admin/realms"; then
        echo "ERROR: Failed to create realm"
        return 1
    fi

    # Create client
    echo "Creating ${CLIENT_ID} client..."
    if ! curl -sk \
        -X POST \
        -H "Authorization: Bearer ${ADMIN_TOKEN}" \
        -H "Content-Type: application/json" \
        --resolve "${KEYCLOAK_HOST}:443:${INGRESS_IP}" \
        -H "Host: ${KEYCLOAK_HOST}" \
        -d "{
            \"clientId\": \"${CLIENT_ID}\",
            \"enabled\": true,
            \"protocol\": \"openid-connect\",
            \"publicClient\": false,
            \"standardFlowEnabled\": true,
            \"redirectUris\": [\"${REDIRECT_URI}\"]
        }" \
        "https://${KEYCLOAK_HOST}/admin/realms/${KEYCLOAK_REALM}/clients"; then
        echo "ERROR: Failed to create client"
        return 1
    fi

    # Get client UUID
    local CLIENT_UUID=$(curl -sk \
        -H "Authorization: Bearer ${ADMIN_TOKEN}" \
        --resolve "${KEYCLOAK_HOST}:443:${INGRESS_IP}" \
        -H "Host: ${KEYCLOAK_HOST}" \
        "https://${KEYCLOAK_HOST}/admin/realms/${KEYCLOAK_REALM}/clients" | \
        jq -r ".[] | select(.clientId==\"${CLIENT_ID}\") | .id")

    if [[ -z "$CLIENT_UUID" ]]; then
        echo "ERROR: Failed to get client UUID"
        return 1
    fi

    # Configure audience mapper
    echo "Configuring audience mapper..."
    if ! curl -sk \
        -X POST \
        -H "Authorization: Bearer ${ADMIN_TOKEN}" \
        -H "Content-Type: application/json" \
        --resolve "${KEYCLOAK_HOST}:443:${INGRESS_IP}" \
        -H "Host: ${KEYCLOAK_HOST}" \
        -d "{
            \"name\": \"${MAPPER_NAME}\",
            \"protocol\": \"openid-connect\",
            \"protocolMapper\": \"oidc-audience-mapper\",
            \"config\": {
                \"included.client.audience\": \"${MAPPER_AUDIENCE}\",
                \"id.token.claim\": \"true\",
                \"access.token.claim\": \"true\"
            }
        }" \
        "https://${KEYCLOAK_HOST}/admin/realms/${KEYCLOAK_REALM}/clients/${CLIENT_UUID}/protocol-mappers/models"; then
        echo "ERROR: Failed to create audience mapper"
        return 1
    fi

    # Create test user
    echo "Creating test user..."
    if ! curl -sk \
        -X POST \
        -H "Authorization: Bearer ${ADMIN_TOKEN}" \
        -H "Content-Type: application/json" \
        --resolve "${KEYCLOAK_HOST}:443:${INGRESS_IP}" \
        -H "Host: ${KEYCLOAK_HOST}" \
        -d "{
            \"username\": \"${TEST_USER}\",
            \"enabled\": true,
            \"credentials\": [{
                \"type\": \"password\",
                \"value\": \"${TEST_PASS}\",
                \"temporary\": false
            }]
        }" \
        "https://${KEYCLOAK_HOST}/admin/realms/${KEYCLOAK_REALM}/users"; then
        echo "ERROR: Failed to create test user"
        return 1
    fi

    echo "Keycloak configuration completed successfully"
    echo "Test user created: ${TEST_USER}/${TEST_PASS}"
    return 0
}
