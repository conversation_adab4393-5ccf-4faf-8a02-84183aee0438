#!/bin/bash

# Function to create Ceph object store and bucket
create_ceph_bucket() {
    echo "Setting up Ceph Object Store and Bucket..."

    # Function to check if resource exists
    check_resource() {
        local kind=$1
        local name=$2
        local namespace=${3:-rook-ceph}
        kubectl get $kind $name -n $namespace >/dev/null 2>&1
    }

    # Function to cleanup existing resources
    cleanup_existing() {
        echo "Checking and cleaning up any existing resources..."
        
        # Delete NodePort Service
        if check_resource service rook-ceph-rgw-store-x-nodeport; then
            echo "Removing existing NodePort service..."
            kubectl delete service rook-ceph-rgw-store-x-nodeport -n rook-ceph --ignore-not-found
            sleep 10
        fi
        
        # Delete ObjectBucketClaim
        if check_resource objectbucketclaim ceph-bucket-x; then
            echo "Removing existing ObjectBucketClaim..."
            # First remove finalizer
            kubectl patch objectbucketclaim ceph-bucket-x -n rook-ceph --type=merge \
                -p '{"metadata":{"finalizers":[]}}' || true
            sleep 5
            
            # Delete the OBC
            kubectl delete objectbucketclaim ceph-bucket-x -n rook-ceph --ignore-not-found
            
            # Wait for deletion with timeout
            for i in {1..30}; do
                if ! check_resource objectbucketclaim ceph-bucket-x; then
                    echo "ObjectBucketClaim deleted successfully"
                    break
                fi
                if [ $i -eq 30 ]; then
                    echo "Warning: Failed to delete ObjectBucketClaim, continuing anyway..."
                    break
                fi
                echo "Waiting for ObjectBucketClaim deletion... ($i/30)"
                sleep 2
            done
        fi
        
        # Delete StorageClass
        if check_resource storageclass rook-ceph-bucket-x; then
            echo "Removing existing StorageClass..."
            kubectl delete storageclass rook-ceph-bucket-x --ignore-not-found
            sleep 5
        fi
        
        # Delete CephObjectStore
        if check_resource cephobjectstore store-x; then
            echo "Removing existing CephObjectStore..."
            
            # Check if already in deleting state
            if kubectl get cephobjectstore store-x -n rook-ceph -o jsonpath='{.status.phase}' | grep -q "Deleting"; then
                echo "CephObjectStore is already in deleting state, removing finalizers..."
                kubectl patch cephobjectstore store-x -n rook-ceph --type=json \
                    -p='[{"op": "remove", "path": "/metadata/finalizers"}]' || true
            else
                # Clean up pools only if not in deleting state
                echo "Cleaning up Ceph pools..."
                kubectl -n rook-ceph exec -it $(kubectl -n rook-ceph get pod -l app=rook-ceph-operator -o jsonpath='{.items[0].metadata.name}') -- bash -c \
                    'ceph osd pool ls | grep store-x | xargs -I {} ceph osd pool delete {} {} --yes-i-really-really-mean-it' || true
                
                # Then delete the object store
                echo "Deleting CephObjectStore..."
                kubectl delete cephobjectstore store-x -n rook-ceph --force --grace-period=0
            fi
            
            # Wait for deletion
            for i in {1..30}; do
                if ! check_resource cephobjectstore store-x; then
                    echo "CephObjectStore deleted successfully"
                    break
                fi
                if [ $i -eq 30 ]; then
                    echo "Warning: Failed to delete CephObjectStore, attempting emergency cleanup..."
                    kubectl patch cephobjectstore store-x -n rook-ceph -p '{"metadata":{"finalizers":null}}' --type=merge || true
                    break
                fi
                echo "Still waiting for CephObjectStore cleanup... ($i/30)"
                sleep 5
            done
            
            # Clean up related resources
            echo "Cleaning up related resources..."
            for resource in deployment service secret; do
                kubectl delete $resource -l rgw=store-x -n rook-ceph --force --grace-period=0 --ignore-not-found || true
            done
        fi
        
        # Additional cleanup for any stuck resources
        kubectl get crd | grep -i 'objectbucket.io' | awk '{print $1}' | \
            xargs -I {} kubectl get {} -n rook-ceph -o name | \
            xargs -I {} kubectl patch {} -n rook-ceph --type=merge -p '{"metadata":{"finalizers":[]}}' || true
        
        sleep 10
        echo "Cleanup completed"
    }

    # Function to verify object store is working
    verify_object_store() {
        echo "Verifying object store functionality..."
        
        # Wait for the object store to be ready
        echo "Waiting for CephObjectStore to be ready..."
        for i in {1..30}; do
            if kubectl get cephobjectstore store-x -n rook-ceph -o jsonpath='{.status.phase}' | grep -q "Ready"; then
                echo "CephObjectStore is ready"
                break
            fi
            if [ $i -eq 30 ]; then
                echo "ERROR: Timeout waiting for CephObjectStore to be ready"
                return 1
            fi
            echo "Waiting for CephObjectStore... ($i/30)"
            sleep 10
        done

        # Verify ObjectBucketClaim
        echo "Verifying ObjectBucketClaim..."
        for i in {1..30}; do
            if kubectl get objectbucketclaim ceph-bucket-x -n rook-ceph -o jsonpath='{.status.phase}' | grep -q "Bound"; then
                echo "ObjectBucketClaim is bound"
                break
            fi
            if [ $i -eq 30 ]; then
                echo "ERROR: Timeout waiting for ObjectBucketClaim to be bound"
                return 1
            fi
            echo "Waiting for ObjectBucketClaim to be bound... ($i/30)"
            sleep 10
        done

        # Create test pod to verify access
        echo "Creating test pod to verify object store access..."
        cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: Pod
metadata:
  name: ceph-test-pod
  namespace: rook-ceph
spec:
  containers:
  - name: ceph-test
    image: amazon/aws-cli
    command: ["/bin/sh", "-c"]
    args:
    - |
      # Wait for credentials to be available
      sleep 10
      
      # Print environment for debugging
      echo "Environment variables:"
      env | grep -E 'AWS_|BUCKET_'
      
      # Configure AWS CLI with debug mode
      aws configure set aws_access_key_id "\${AWS_ACCESS_KEY_ID}"
      aws configure set aws_secret_access_key "\${AWS_SECRET_ACCESS_KEY}"
      aws configure set default.region us-east-1
      
      # Ensure BUCKET_HOST has http:// prefix
      ENDPOINT="http://\${BUCKET_HOST}"
      echo "Using endpoint: \${ENDPOINT}"
      
      # Test endpoint connectivity
      echo "Testing endpoint connectivity..."
      curl -v "\${ENDPOINT}"
      
      # Create and upload test file with debug
      echo "Creating test file..."
      echo "test content" > test.txt
      
      echo "Attempting to upload file..."
      aws --debug --endpoint-url "\${ENDPOINT}" s3 cp test.txt s3://\${BUCKET_NAME}/test.txt
      
      if [ $? -eq 0 ]; then
          echo "Upload successful, attempting download..."
          aws --debug --endpoint-url "\${ENDPOINT}" s3 cp s3://\${BUCKET_NAME}/test.txt downloaded.txt
          
          if [ $? -eq 0 ]; then
              echo "Download successful, verifying content..."
              cat downloaded.txt
              
              echo "Cleaning up test file..."
              aws --endpoint-url "\${ENDPOINT}" s3 rm s3://\${BUCKET_NAME}/test.txt
              echo "Test completed successfully"
          else
              echo "ERROR: Failed to download file"
              exit 1
          fi
      else
          echo "ERROR: Failed to upload file"
          exit 1
      fi
    env:
    - name: AWS_ACCESS_KEY_ID
      valueFrom:
        secretKeyRef:
          name: ceph-bucket-x
          key: AWS_ACCESS_KEY_ID
    - name: AWS_SECRET_ACCESS_KEY
      valueFrom:
        secretKeyRef:
          name: ceph-bucket-x
          key: AWS_SECRET_ACCESS_KEY
    - name: BUCKET_NAME
      valueFrom:
        configMapKeyRef:
          name: ceph-bucket-x
          key: BUCKET_NAME
    - name: BUCKET_HOST
      valueFrom:
        configMapKeyRef:
          name: ceph-bucket-x
          key: BUCKET_HOST
EOF

        # Wait for test pod to complete
        echo "Waiting for test pod to complete..."
        if ! kubectl wait --for=condition=ready pod/ceph-test-pod -n rook-ceph --timeout=120s; then
            echo "ERROR: Test pod failed to start"
            kubectl describe pod ceph-test-pod -n rook-ceph
            kubectl logs ceph-test-pod -n rook-ceph
            kubectl delete pod ceph-test-pod -n rook-ceph
            return 1
        fi

        # Stream logs in real time
        kubectl logs -f ceph-test-pod -n rook-ceph

        # Check final status
        if ! kubectl logs ceph-test-pod -n rook-ceph | grep -q "Test completed successfully"; then
            echo "ERROR: Object store verification failed"
            echo "Pod description:"
            kubectl describe pod ceph-test-pod -n rook-ceph
            echo "Full logs:"
            kubectl logs ceph-test-pod -n rook-ceph
            kubectl delete pod ceph-test-pod -n rook-ceph
            return 1
        fi

        # Cleanup test pod
        kubectl delete pod ceph-test-pod -n rook-ceph

        echo "Object store verification completed successfully"
        return 0
    }

    # Start with cleanup
    cleanup_existing

    echo "Creating CephObjectStore..."
    cat <<EOF | kubectl apply -f -
apiVersion: ceph.rook.io/v1
kind: CephObjectStore
metadata:
  name: store-x
  namespace: rook-ceph
spec:
  metadataPool:
    failureDomain: osd
    replicated:
      size: 3
  dataPool:
    failureDomain: osd
    erasureCoded:
      dataChunks: 4
      codingChunks: 2
  preservePoolsOnDelete: true
  gateway:
    port: 80
    instances: 1
    # Add resource limits
    resources:
      limits:
        cpu: "2"
        memory: "2Gi"
      requests:
        cpu: "1"
        memory: "1Gi"
EOF
    check_command "Create Ceph Object Store"
    
    echo "Creating StorageClass..."
    cat <<EOF | kubectl apply -f -
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: rook-ceph-bucket-x
provisioner: rook-ceph.ceph.rook.io/bucket
reclaimPolicy: Delete
parameters:
  objectStoreName: store-x
  objectStoreNamespace: rook-ceph
EOF
    check_command "Create Ceph StorageClass"

    echo "Creating ObjectBucketClaim..."
    cat <<EOF | kubectl apply -f -
apiVersion: objectbucket.io/v1alpha1
kind: ObjectBucketClaim
metadata:
  name: ceph-bucket-x
  namespace: rook-ceph
spec:
  generateBucketName: ceph-bkt-x
  storageClassName: rook-ceph-bucket-x
EOF
    check_command "Create ObjectBucketClaim"
    
    echo "Creating NodePort Service..."
    cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: Service
metadata:
  name: rook-ceph-rgw-store-x-nodeport
  namespace: rook-ceph
spec:
  selector:
    app: rook-ceph-rgw
    ceph_daemon_id: store-x
    rgw: store-x
    rook_cluster: rook-ceph
    rook_object_store: store-x
  type: NodePort
  ports:
    - name: http
      port: 80
      targetPort: 8080
      protocol: TCP
      nodePort: 30480
EOF
    check_command "Create Ceph Object Store NodePort Service"

    # Verify the installation
    if ! verify_object_store; then
        echo "ERROR: Object store verification failed"
        return 1
    fi

    # Print usage information
    echo
    echo "Ceph Object Store has been successfully created and verified!"
    echo
    echo "To use the object store, get the credentials with:"
    echo "Access Key: kubectl get secret -n rook-ceph ceph-bucket-x -o jsonpath='{.data.AWS_ACCESS_KEY_ID}' | base64 -d"
    echo "Secret Key: kubectl get secret -n rook-ceph ceph-bucket-x -o jsonpath='{.data.AWS_SECRET_ACCESS_KEY}' | base64 -d"
    echo "Bucket Name: kubectl get cm -n rook-ceph ceph-bucket-x -o jsonpath='{.data.BUCKET_NAME}'"
    echo
    echo "Internal endpoint: http://rook-ceph-rgw-store-x.rook-ceph"
    echo "External endpoint: http://<node-ip>:30480"
    echo
}
