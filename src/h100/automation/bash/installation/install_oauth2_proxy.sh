#!/bin/bash

# Function to install OAuth2 Proxy
install_oauth2_proxy() {
    echo "Installing OAuth2 Proxy..."
    
    # Get configuration values
    local OAUTH2_PROXY_HOST=$(read_yaml_value '.oauth2-proxy.hostname')
    local KEYCLOAK_HOST=$(read_yaml_value '.oauth2-proxy.keycloak.host')
    local KEYCLOAK_REALM=$(read_yaml_value '.oauth2-proxy.keycloak.realm')
    local CLIENT_ID=$(read_yaml_value '.oauth2-proxy.keycloak.clientId')
    local INGRESS_CLASS=$(read_yaml_value '.oauth2-proxy.ingress.className')

    # Validate required values
    [[ -z "$OAUTH2_PROXY_HOST" ]] && echo "ERROR: OAuth2 Proxy hostname not configured" && return 1
    [[ -z "$KEYCLOAK_HOST" ]] && echo "ERROR: Keycloak host not configured" && return 1
    [[ -z "$KEYCLOAK_REALM" ]] && echo "ERROR: Keycloak realm not configured" && return 1
    [[ -z "$CLIENT_ID" ]] && echo "ERROR: Client ID not configured" && return 1
    [[ -z "$INGRESS_CLASS" ]] && echo "ERROR: Ingress class not configured" && return 1

    # Get Ingress IP
    local INGRESS_IP=$(kubectl get ingress -n keycloak keycloak -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
    if [[ -z "$INGRESS_IP" ]]; then
        echo "ERROR: Could not get Keycloak Ingress IP"
        return 1
    fi

    # Get admin token
    local ADMIN_TOKEN=$(get_keycloak_admin_token "${KEYCLOAK_HOST}" "${INGRESS_IP}")
    [[ $? -ne 0 ]] && return 1

    # Get client UUID
    local CLIENT_UUID=$(get_keycloak_client_uuid "${ADMIN_TOKEN}" "${KEYCLOAK_HOST}" "${INGRESS_IP}" "${KEYCLOAK_REALM}" "${CLIENT_ID}")
    [[ $? -ne 0 ]] && return 1

    # Get client secret
    local CLIENT_SECRET=$(curl -sk \
        -H "Authorization: Bearer ${ADMIN_TOKEN}" \
        --resolve "${KEYCLOAK_HOST}:443:${INGRESS_IP}" \
        -H "Host: ${KEYCLOAK_HOST}" \
        "https://${KEYCLOAK_HOST}/admin/realms/${KEYCLOAK_REALM}/clients/${CLIENT_UUID}/client-secret" | \
        jq -r '.value')

    [[ -z "$CLIENT_SECRET" || "$CLIENT_SECRET" == "null" ]] && echo "ERROR: Failed to get client secret" && return 1

    # Generate cookie secret
    # TODO: For better user experience, consider storing this in a Kubernetes secret
    # and reusing it across reinstalls to avoid forcing users to re-login
    local COOKIE_SECRET=$(openssl rand -base64 32 | head -c 32 | base64)

    # Check if namespace exists and clean up if needed
    if kubectl get namespace oauth2-proxy >/dev/null 2>&1; then
        echo "Found existing oauth2-proxy namespace, cleaning up..."
        helm uninstall oauth2-proxy -n oauth2-proxy
        kubectl delete namespace oauth2-proxy
    fi

    # Create OAuth2 Proxy values file
    cat > oauth2-proxy-values.yml <<EOF
config:
  clientID: "${CLIENT_ID}"
  clientSecret: "${CLIENT_SECRET}"
  cookieSecret: "${COOKIE_SECRET}"
  configFile: |-
    # Provider config
    provider="keycloak-oidc"
    provider_display_name="Keycloak"
    redirect_url="https://${OAUTH2_PROXY_HOST}/oauth2/callback"
    oidc_issuer_url="https://${KEYCLOAK_HOST}/realms/${KEYCLOAK_REALM}"
    code_challenge_method="S256"
    ssl_insecure_skip_verify=true
    # Upstream config
    http_address="0.0.0.0:4180"
    upstreams="file:///dev/null"
    email_domains=["*"]
    cookie_domains=["projectx.eypoc.com"]
    cookie_secure=false
    scope="openid"
    whitelist_domains=[".projectx.eypoc.com"]
    insecure_oidc_allow_unverified_email="true"
    # Header config
    pass_user_headers=true
    set_xauthrequest=true
    set_authorization_header=true
    pass_access_token=true
    pass_authorization_header=true
    reverse_proxy=true

# Add hostAliases configuration to enable OAuth2 Proxy to resolve Keycloak hostname
# This is a temporary solution until proper DNS is configured
# Similar to adding an entry in /etc/hosts file:
# <INGRESS_IP> <KEYCLOAK_HOST>
hostAliases:
  - ip: "${INGRESS_IP}"        # Keycloak ingress IP address
    hostnames:                  # List of hostnames to be resolved to this IP
      - "${KEYCLOAK_HOST}"     # Keycloak hostname (e.g., sso.projectx.eypoc.com)

sessionStorage:
  type: redis
redis:
  enabled: true
  architecture: standalone

ingress:
  enabled: true
  className: "${INGRESS_CLASS}"
  pathType: Prefix
  path: /oauth2
  annotations:
    nginx.ingress.kubernetes.io/proxy-buffer-size: "16k"
  hosts:
    - ${OAUTH2_PROXY_HOST}
  tls:
    - hosts:
        - ${OAUTH2_PROXY_HOST}
EOF

    # Install OAuth2 Proxy
    echo "Installing OAuth2 Proxy..."
    helm repo add oauth2-proxy https://oauth2-proxy.github.io/manifests
    check_command "Add OAuth2 Proxy repository"
    
    helm repo update
    check_command "Update Helm repositories"
    
    kubectl create namespace oauth2-proxy
    check_command "Create OAuth2 Proxy namespace"
    
    helm install oauth2-proxy oauth2-proxy/oauth2-proxy \
        -f oauth2-proxy-values.yml \
        --namespace oauth2-proxy \
        --wait --timeout 5m
    check_command "Install OAuth2 Proxy"

    # Verify installation
    echo "Verifying OAuth2 Proxy installation..."
    
    # Check pod status
    echo "Checking pod status..."
    if ! kubectl wait --namespace oauth2-proxy \
        --for=condition=ready pod \
        --selector=app.kubernetes.io/name=oauth2-proxy \
        --timeout=300s; then
        echo "ERROR: OAuth2 Proxy pods not ready"
        return 1
    fi

    # Check Redis status if enabled
    echo "Checking Redis status..."
    if ! kubectl wait --namespace oauth2-proxy \
        --for=condition=ready pod \
        --selector=app.kubernetes.io/name=redis \
        --timeout=300s; then
        echo "ERROR: Redis pods not ready"
        return 1
    fi

    # Check ingress configuration
    echo "Checking ingress configuration..."
    if ! kubectl get ingress -n oauth2-proxy oauth2-proxy >/dev/null 2>&1; then
        echo "ERROR: Ingress not created"
        return 1
    fi

    # Verify OAuth2 Proxy sign-in endpoint
    echo "Verifying OAuth2 Proxy sign-in endpoint..."
    local OAUTH2_PROXY_IP=$(kubectl get ingress -n oauth2-proxy oauth2-proxy -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
    if ! curl -sk \
        --resolve "${OAUTH2_PROXY_HOST}:443:${OAUTH2_PROXY_IP}" \
        "https://${OAUTH2_PROXY_HOST}/oauth2/sign_in" | \
        grep -q "Sign in with Keycloak"; then
        echo "ERROR: OAuth2 Proxy sign-in page not accessible"
        return 1
    fi

    echo "OAuth2 Proxy installation and verification completed successfully"
    return 0
}


# Function to get Keycloak admin token
get_keycloak_admin_token() {
    local KEYCLOAK_HOST="$1"
    local INGRESS_IP="$2"

    # Get admin credentials
    local ADMIN_USER=$(read_yaml_value '.keycloak.adminUser')
    if [[ -z "$ADMIN_USER" ]]; then
        echo "ERROR: Could not get Keycloak admin user from config" >&2
        return 1
    fi

    local ADMIN_PASSWORD=$(kubectl get secret keycloak -o jsonpath='{.data.admin-password}' -n keycloak | base64 -d)
    if [[ -z "$ADMIN_PASSWORD" ]]; then
        echo "ERROR: Could not retrieve Keycloak admin password" >&2
        return 1
    fi

    local TOKEN=$(curl -sk \
        -d "client_id=admin-cli" \
        -d "username=${ADMIN_USER}" \
        -d "password=${ADMIN_PASSWORD}" \
        -d "grant_type=password" \
        --resolve "${KEYCLOAK_HOST}:443:${INGRESS_IP}" \
        -H "Host: ${KEYCLOAK_HOST}" \
        "https://${KEYCLOAK_HOST}/realms/master/protocol/openid-connect/token" | jq -r '.access_token')

    if [[ -z "$TOKEN" || "$TOKEN" == "null" ]]; then
        echo "ERROR: Failed to get admin token" >&2
        return 1
    fi
    echo "$TOKEN"
}

# Function to get Keycloak client UUID
get_keycloak_client_uuid() {
    local ADMIN_TOKEN="$1"
    local KEYCLOAK_HOST="$2"
    local INGRESS_IP="$3"
    local REALM="$4"
    local CLIENT_ID="$5"

    local UUID=$(curl -sk \
        -H "Authorization: Bearer ${ADMIN_TOKEN}" \
        --resolve "${KEYCLOAK_HOST}:443:${INGRESS_IP}" \
        -H "Host: ${KEYCLOAK_HOST}" \
        "https://${KEYCLOAK_HOST}/admin/realms/${REALM}/clients" | \
        jq -r ".[] | select(.clientId==\"${CLIENT_ID}\") | .id")

    if [[ -z "$UUID" || "$UUID" == "null" ]]; then
        echo "ERROR: Failed to get client UUID" >&2
        return 1
    fi
    echo "$UUID"
}