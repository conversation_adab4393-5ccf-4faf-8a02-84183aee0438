#!/bin/bash

# Function to install Ingress-Nginx
install_ingress_nginx() {
    echo "Installing Ingress-Nginx..."

    # Cleanup function for Ingress-Nginx resources
    cleanup_ingress_nginx() {
        echo "Cleaning up existing Ingress-Nginx installation..."
        
        # Remove finalizers from resources
        for resource in $(kubectl get -n ingress-nginx $(kubectl api-resources --namespaced=true --output=name | grep networking.k8s.io) -o name 2>/dev/null); do
            kubectl patch -n ingress-nginx $resource -p '{"metadata":{"finalizers":[]}}' --type=merge 2>/dev/null || true
        done
        
        # Delete namespace if exists
        if kubectl get namespace ingress-nginx &>/dev/null; then
            echo "Removing ingress-nginx namespace..."
            
            # Normal non-blocking delete
            kubectl delete namespace ingress-nginx &
            
            # Wait for namespace deletion with timeout
            echo "Waiting for namespace deletion..."
            for i in {1..15}; do
                if ! kubectl get namespace ingress-nginx &>/dev/null; then
                    echo "Namespace deleted successfully"
                    break
                fi
                
                if [ $i -eq 15 ]; then
                    echo "ERROR: Failed to delete ingress-nginx namespace after 5 minutes"
                    return 1
                fi
                
                echo "Waiting for namespace deletion... ($i/15)"
                sleep 20
            done
        fi
        
        # Remove cluster-wide resources
        echo "Removing cluster-wide resources..."
        kubectl delete clusterrole -l app.kubernetes.io/name=ingress-nginx --ignore-not-found
        kubectl delete clusterrolebinding -l app.kubernetes.io/name=ingress-nginx --ignore-not-found
        
        # Remove ValidatingWebhookConfiguration if exists
        kubectl delete ValidatingWebhookConfiguration ingress-nginx-admission --ignore-not-found
        
        # Remove Helm release if exists
        if helm list -n ingress-nginx 2>/dev/null | grep -q "ingress-nginx"; then
            echo "Removing Helm release..."
            helm uninstall ingress-nginx -n ingress-nginx 2>/dev/null || true
        fi
        
        echo "Cleanup completed"
        sleep 5
    }

    # Run cleanup first
    cleanup_ingress_nginx

    # Install Ingress-Nginx
    echo "Installing Ingress-Nginx controller..."
    helm upgrade --install ingress-nginx ingress-nginx \
        --repo https://kubernetes.github.io/ingress-nginx \
        --namespace ingress-nginx \
        --create-namespace \
        --wait \
        --timeout 300s
    check_command "Install Ingress-Nginx"

    # Verify installation
    echo "Verifying Ingress-Nginx installation..."
    
    # Wait for controller deployment
    echo "Waiting for controller deployment..."
    if ! kubectl wait --namespace ingress-nginx \
        --for=condition=ready pod \
        --selector=app.kubernetes.io/component=controller \
        --timeout=300s; then
        echo "ERROR: Timeout waiting for Ingress-Nginx controller pods"
        return 1
    fi

    # Verify admission webhook
    echo "Verifying admission webhook..."
    if ! kubectl get validatingwebhookconfiguration ingress-nginx-admission &>/dev/null; then
        echo "ERROR: Admission webhook configuration not found"
        return 1
    fi

    # Check if service is created and has IP/hostname
    echo "Checking Ingress-Nginx service..."
    for i in {1..30}; do
        if kubectl get service ingress-nginx-controller -n ingress-nginx -o jsonpath='{.status.loadBalancer.ingress[0]}' 2>/dev/null | grep -E 'ip|hostname' >/dev/null; then
            echo "Ingress-Nginx service is ready"
            break
        fi
        if [ $i -eq 30 ]; then
            echo "ERROR: Timeout waiting for Ingress-Nginx service"
            return 1
        fi
        echo "Waiting for service... ($i/30)"
        sleep 5
    done

    # Test the controller with a simple ingress
    echo "Testing Ingress-Nginx controller with a test application..."
    
    # Create test deployment and service
    kubectl create deployment test-nginx --image=nginx -n ingress-nginx
    kubectl expose deployment test-nginx --port=80 -n ingress-nginx
    
    # Create test ingress
    cat <<EOF | kubectl apply -f -
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: test-ingress
  namespace: ingress-nginx
spec:
  ingressClassName: nginx
  rules:
  - http:
      paths:
      - path: /test
        pathType: Prefix
        backend:
          service:
            name: test-nginx
            port:
              number: 80
EOF

    # Wait for ingress to get address
    echo "Waiting for test ingress to get address..."
    for i in {1..12}; do
        if kubectl get ing test-ingress -n ingress-nginx -o jsonpath='{.status.loadBalancer.ingress[0]}' 2>/dev/null | grep -E 'ip|hostname' >/dev/null; then
            echo "Test ingress is ready"
            break
        fi
        if [ $i -eq 12 ]; then
            echo "ERROR: Timeout waiting for test ingress"
            kubectl delete ing test-ingress -n ingress-nginx
            kubectl delete svc test-nginx -n ingress-nginx
            kubectl delete deployment test-nginx -n ingress-nginx
            return 1
        fi
        echo "Waiting for ingress address... ($i/12)"
        sleep 10
    done

    # Cleanup test resources
    kubectl delete ing test-ingress -n ingress-nginx
    kubectl delete svc test-nginx -n ingress-nginx
    kubectl delete deployment test-nginx -n ingress-nginx

    echo "Ingress-Nginx installation and verification completed successfully"
    return 0
}
