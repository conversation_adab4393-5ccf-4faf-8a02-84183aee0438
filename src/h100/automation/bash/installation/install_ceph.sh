#!/bin/bash

# Function to cleanup Ceph resources
cleanup_ceph() {
    echo "Cleaning up Ceph resources..."
    
    # Store the initial directory
    INITIAL_DIR=$(pwd)

    # First cleanup object store related resources
    echo "Cleaning up object store resources..."
    
    # Remove finalizers from ObjectBucketClaim
    kubectl -n rook-ceph get obc -o name | \
        xargs -I {} kubectl patch -n rook-ceph {} --type merge -p '{"metadata":{"finalizers":[]}}' 2>/dev/null || true
    
    # Delete ObjectBucketClaims
    kubectl -n rook-ceph delete obc --all --force --grace-period=0 2>/dev/null || true
    
    # Remove finalizers from CephObjectStore
    kubectl -n rook-ceph get cephobjectstore -o name | \
        xargs -I {} kubectl patch -n rook-ceph {} --type merge -p '{"metadata":{"finalizers":[]}}' 2>/dev/null || true
    
    # Delete CephObjectStore
    kubectl -n rook-ceph delete cephobjectstore --all --force --grace-period=0 2>/dev/null || true
    
    # Clean up RGW resources
    echo "Cleaning up RGW resources..."
    for resource in deployment service secret; do
        kubectl delete $resource -l rgw=store-x -n rook-ceph --force --grace-period=0 --ignore-not-found || true
    done
    
    # Clean up any stuck object bucket resources
    echo "Cleaning up stuck object bucket resources..."
    kubectl get crd | grep -i 'objectbucket.io' | awk '{print $1}' | \
        xargs -I {} kubectl get {} -n rook-ceph -o name | \
        xargs -I {} kubectl patch {} -n rook-ceph --type=merge -p '{"metadata":{"finalizers":[]}}' || true
    
    # Wait for object store resources to be cleaned up
    echo "Waiting for object store resources to be cleaned up..."
    sleep 10
    
    # Now proceed with general Ceph cleanup
    echo "Proceeding with general Ceph cleanup..."
    
    # If we're in the examples directory, go back to rook root
    if [[ "$INITIAL_DIR" == */rook/deploy/examples ]]; then
        cd ../../..
        INITIAL_DIR=$(pwd)
    fi

    # Remove finalizers from CephCluster
    echo "Removing finalizers from CephCluster..."
    kubectl -n rook-ceph patch cephcluster rook-ceph --type merge \
        -p '{"metadata":{"finalizers":[]}}' 2>/dev/null || true
        
    # Remove finalizers from filesystem resources    
    kubectl -n rook-ceph get cephfilesystem -o name | \
        xargs -I {} kubectl patch -n rook-ceph {} --type merge -p '{"metadata":{"finalizers":[]}}' 2>/dev/null || true
    
    kubectl -n rook-ceph get cephfilesystemsubvolumegroup -o name | \
        xargs -I {} kubectl patch -n rook-ceph {} --type merge -p '{"metadata":{"finalizers":[]}}' 2>/dev/null || true

    # Remove finalizers from configmaps and secrets
    for resource in configmap secret; do
        kubectl get $resource -n rook-ceph -o name | \
            xargs -I {} kubectl patch -n rook-ceph {} --type merge -p '{"metadata":{"finalizers":[]}}' 2>/dev/null || true
    done

    # Delete Ceph resources in correct order
    echo "Deleting Ceph resources..."
    if [ -d "$INITIAL_DIR/rook/deploy/examples" ]; then
        cd "$INITIAL_DIR/rook/deploy/examples"
        
        # Delete in reverse order of creation
        kubectl delete -f csi/cephfs/storageclass.yaml --ignore-not-found 2>/dev/null || true
        kubectl delete -f filesystem.yaml --ignore-not-found 2>/dev/null || true
        kubectl delete -f cluster.yaml --ignore-not-found 2>/dev/null || true
        kubectl delete -f operator.yaml --ignore-not-found 2>/dev/null || true
        kubectl delete -f crds.yaml --ignore-not-found 2>/dev/null || true
        kubectl delete -f common.yaml --ignore-not-found 2>/dev/null || true
    fi

    # Clean up remaining CRDs
    echo "Cleaning up remaining Ceph CRDs..."
    kubectl get crd | grep -i 'ceph\|rook' | awk '{print $1}' | xargs -r kubectl delete crd --timeout=30s
    
    # Clean up /var/lib/rook directory on all nodes
    echo "Cleaning up /var/lib/rook directory on all nodes..."
    
    # Create cleanup DaemonSet
    cat <<EOF | kubectl apply -f -
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: rook-cleanup
  namespace: default
spec:
  selector:
    matchLabels:
      app: rook-cleanup
  template:
    metadata:
      labels:
        app: rook-cleanup
    spec:
      hostPID: true
      hostNetwork: true
      containers:
      - name: cleanup
        image: ubuntu:latest
        securityContext:
          privileged: true
        command: ["/bin/bash", "-c"]
        args:
        - |
          echo "Cleaning up /var/lib/rook on node \$(hostname)..."
          rm -rf /var/lib/rook
          echo "Cleanup completed on node \$(hostname)"
          sleep 5
          exit 0
        volumeMounts:
        - name: host-root
          mountPath: /var/lib/rook
          mountPropagation: Bidirectional
      volumes:
      - name: host-root
        hostPath:
          path: /var/lib/rook
      tolerations:
      - operator: Exists
EOF

    # Wait for cleanup DaemonSet to complete
    echo "Waiting for Rook cleanup DaemonSet to complete..."
    sleep 10
    
    # Monitor the progress
    while true; do
        TOTAL_NODES=$(kubectl get nodes --no-headers | wc -l)
        COMPLETED_PODS=$(kubectl get pods -l app=rook-cleanup --no-headers | grep -c "Completed" || true)
        FAILED_PODS=$(kubectl get pods -l app=rook-cleanup --no-headers | grep -c "Error\|Failed" || true)
        RUNNING_PODS=$(kubectl get pods -l app=rook-cleanup --no-headers | grep -c "Running" || true)
        
        echo "Progress: $COMPLETED_PODS/$TOTAL_NODES nodes completed, $RUNNING_PODS running, $FAILED_PODS failed"
        
        if [ $FAILED_PODS -gt 0 ]; then
            echo "ERROR: Some pods failed during Rook cleanup"
            kubectl delete ds rook-cleanup
            return 1
        fi
        
        if [ $COMPLETED_PODS -eq $TOTAL_NODES ]; then
            echo "Rook cleanup completed successfully on all nodes"
            break
        fi
        
        sleep 5
    done
    
    # Cleanup the DaemonSet
    kubectl delete ds rook-cleanup
    
    # Remove Rook directory
    rm -rf "$INITIAL_DIR/rook"
    
    # Return to initial directory
    cd "$INITIAL_DIR"
    
    echo "Cleanup completed"
}

# Function to install Ceph
install_ceph() {
    # Store the initial directory
    INITIAL_DIR=$(pwd)
    
    echo "Starting Ceph installation..."

    # Check if Ceph is already installed
    if kubectl get namespace rook-ceph &>/dev/null; then
        echo "Detected existing Ceph installation"
        read -p "Do you want to remove existing installation and continue? (y/N) " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            cleanup_ceph
        else
            echo "Installation cancelled"
            return 1
        fi
    fi

    # Clone Rook repository
    echo "Cloning Rook repository..."
    if [ -d "rook" ]; then
        rm -rf rook
    fi
    if ! git clone --depth 1 --single-branch --branch release-1.15 https://github.com/rook/rook.git; then
        echo "Failed to clone Rook repository"
        return 1
    fi
    check_command "Clone Rook repository"
    
    cd rook/deploy/examples || {
        echo "Failed to change directory to rook/deploy/examples"
        cd "$INITIAL_DIR"  # Return to initial directory before exit
        return 1
    }
    
    # Install CRDs and operator
    echo "Installing Ceph CRDs, Common Resources, and Operator..."
    if ! kubectl create -f crds.yaml -f common.yaml -f operator.yaml; then
        echo "Failed to create Ceph CRDs and operator"
        cleanup_ceph
        return 1
    fi
    check_command "Create Ceph CRDs, Common Resources, and Operator"
    
    # Wait for operator with increased timeout
    echo "Waiting for Ceph operator to be ready..."
    if ! kubectl wait --for=condition=ready pod -l app=rook-ceph-operator -n rook-ceph --timeout=300s; then
        echo "Timeout waiting for Ceph operator"
        cleanup_ceph
        return 1
    fi
    check_command "Wait for Ceph operator"
    
    # Create Ceph cluster with increased timeout
    echo "Creating Ceph Cluster..."
    if ! kubectl create -f cluster.yaml; then
        echo "Failed to create Ceph cluster"
        cleanup_ceph
        return 1
    fi
    check_command "Create Ceph cluster"
    
    # Wait for Ceph cluster to be ready with interactive retry
    echo "Waiting for Ceph cluster to be ready..."
    i=1
    max_attempts=60
    while true; do
        if kubectl -n rook-ceph get cephcluster -o jsonpath='{.items[0].status.phase}' | grep -q "Ready"; then
            echo "Ceph cluster is ready"
            break
        fi
        
        echo "Waiting for Ceph cluster... ($i/$max_attempts)"
        
        if [ $i -eq $max_attempts ]; then
            echo "Current Ceph cluster status:"
            kubectl -n rook-ceph get cephcluster -o yaml
            echo "Ceph operator logs:"
            kubectl -n rook-ceph logs -l app=rook-ceph-operator
            
            read -p "Timeout waiting for Ceph cluster. Continue waiting? (Y/n) " choice
            choice=${choice:-y}
            if [[ ! "$choice" =~ ^[Yy]$ ]]; then
                echo "Aborting installation..."
                cleanup_ceph
                return 1
            fi
            
            echo "Resetting wait counter..."
            i=0
        fi
        
        i=$((i + 1))
        sleep 20
    done

    # Create filesystem
    echo "Creating Ceph filesystem..."
    if ! kubectl apply -f filesystem.yaml; then
        echo "Failed to create Ceph filesystem"
        cleanup_ceph
        return 1
    fi
    check_command "Apply Ceph filesystem"
    
    # Wait for filesystem to be ready
    echo "Waiting for Ceph filesystem to be ready..."
    for i in {1..30}; do
        if kubectl -n rook-ceph get cephfilesystem -o jsonpath='{.items[0].status.phase}' | grep -q "Ready"; then
            break
        fi
        if [ $i -eq 30 ]; then
            echo "Timeout waiting for Ceph filesystem to be ready"
            cleanup_ceph
            return 1
        fi
        echo "Waiting for filesystem... ($i/30)"
        sleep 10
    done
    
    # Create storage class
    echo "Creating Ceph storage class..."
    if ! kubectl apply -f csi/cephfs/storageclass.yaml; then
        echo "Failed to create storage class"
        cleanup_ceph
        return 1
    fi
    check_command "Apply Ceph CSI storage class"
    
    # Set as default storage class
    echo "Setting Ceph storage class as default..."
    if ! kubectl patch storageclass rook-cephfs -p '{"metadata": {"annotations":{"storageclass.kubernetes.io/is-default-class":"true"}}}'; then
        echo "Failed to set default storage class"
        cleanup_ceph
        return 1
    fi
    check_command "Patch Ceph storage class"

    # Verify installation
    echo "Verifying Ceph installation..."
    if ! kubectl -n rook-ceph get cephcluster -o jsonpath='{.items[0].status.phase}' | grep -q "Ready"; then
        echo "Ceph cluster is not ready"
        cleanup_ceph
        return 1
    fi

    # Test storage class
    echo "Testing storage class..."
    cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: ceph-test-pvc
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 1Gi
  storageClassName: rook-cephfs
EOF

    # Wait for PVC to be bound
    for i in {1..10}; do
        if kubectl get pvc ceph-test-pvc -o jsonpath='{.status.phase}' | grep -q "Bound"; then
            echo "Storage class test successful"
            kubectl delete pvc ceph-test-pvc
            break
        fi
        if [ $i -eq 10 ]; then
            echo "Storage class test failed"
            kubectl delete pvc ceph-test-pvc
            cleanup_ceph
            return 1
        fi
        echo "Waiting for test PVC... ($i/10)"
        sleep 3
    done

    # Return to the initial directory at the end
    cd "$INITIAL_DIR"
    
    echo "Ceph installation completed successfully"
    return 0
}
