#!/bin/bash

# Function to install Qdrant
install_qdrant() {
    echo "Installing Qdrant..."
    
    # Cleanup existing installation
    helm uninstall qdrant -n qdrant 2>/dev/null || true
    kubectl delete namespace qdrant --wait=false 2>/dev/null || true
    kubectl wait --for=delete namespace/qdrant --timeout=60s 2>/dev/null || true
    
    # Add Qdrant repository
    helm repo add qdrant https://qdrant.to/helm
    check_command "Add Qdrant repository"
    
    # Install Qdrant
    helm install qdrant qdrant/qdrant \
        --namespace qdrant \
        --create-namespace
    check_command "Install Qdrant"
    
    # Verify installation
    echo "Verifying Qdrant installation..."
    if ! kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=qdrant -n qdrant --timeout=300s; then
        echo "ERROR: Qdrant pods not ready"
        return 1
    fi
    
    # Test connection using port-forward
    echo "Testing Qdrant connection..."
    local POD_NAME=$(kubectl get pods -n qdrant -l app.kubernetes.io/name=qdrant -o jsonpath='{.items[0].metadata.name}')
    kubectl port-forward -n qdrant $POD_NAME 6333:6333 &
    local PF_PID=$!
    sleep 5
    
    if ! curl -s http://localhost:6333/health > /dev/null; then
        echo "ERROR: Cannot connect to Qdrant"
        kill $PF_PID
        return 1
    fi
    kill $PF_PID
}
