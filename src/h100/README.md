# Stack

## Hardware
### PowerEdge
BISO Setting
* Disable ACS

    IO virtualization (also known as VT-d or IOMMU) can interfere with GPU Direct by redirecting all PCI point-to-point traffic to the CPU root complex, causing a significant performance reduction or even a hang. 
    
    When Virtualization Technology is disabled, it also deactivates the ACS (Access Control Service) function.
    [Resolving ACS Disable on Dell PowerEdge Platforms](https://infohub.delltechnologies.com/en-us/p/resolving-acs-disable-on-dell-poweredge-platforms/)

    > NOTE: When the Intel Xeon Platinum 8592+ processor is installed, the option to disable Virtualization Technology is not available in the BIOS. To achieve full performance with GPUDirect RDMA, enable X2 APIC in the BIOS and manually disable ACS in the OS.
    >
    > For two processors 64 cores (or above), Virtualization Technology must be Enabled and this option is grayed out.
    
    


### Switch
TODO - configure switch command

## OS
Ubuntu manually installation
### OS configure
* Max file open
    ```bash
    sudo sysctl -w fs.inotify.max_user_watches=2099999999
    sudo sysctl -w fs.inotify.max_user_instances=2099999999
    sudo sysctl -w fs.inotify.max_queued_events=2099999999
    ```
* Disable ACS on pci devices
    ```
    for BDF in `lspci -d "*:*:*" | awk '{print $1}'`; do
        # skip if it doesn't support ACS
        sudo setpci -v -s ${BDF} ECAP_ACS+0x6.w > /dev/null 2>&1
        if [ $? -ne 0 ]; then
            continue
        fi
        sudo setpci -v -s ${BDF} ECAP_ACS+0x6.w=0000
    done
    ```
### Driver
* Fabric Manager for NVIDIA NVSwitch Systems (NVlink)

    Here is the Fabric state output when the GPU is being registered:
    ```
    # nvidia-smi -q -i 0 | grep -i -A 2 Fabric
        Fabric
            State                             : Completed
            Status                            : Success
    ```
    Installation
    ```
    wget https://developer.download.nvidia.com/compute/cuda/repos/$distro/$arch/cuda-keyring_1.1-1_all.deb
    sudo dpkg -i cuda-keyring_1.1-1_all.deb
    sudo apt-get update
    version=$(cat /sys/module/nvidia/version)
    main_version=$(echo $version | awk -F '.' '{print $1}')
    apt-get update
    apt-get -y install nvidia-fabricmanager-${main_version}=${version}-*
    ```
* Mellanox MLNX_OFED
    1. Verify that the system has a Mellanox network adapter (HCA/NIC) installed.
        ```
        # lspci -v | grep Mellanox
        1c:00.0 Ethernet controller: Mellanox Technologies MT2892 Family [ConnectX-6 Dx]
                Subsystem: Mellanox Technologies MT2892 Family [ConnectX-6 Dx]
        1c:00.1 Ethernet controller: Mellanox Technologies MT2892 Family [ConnectX-6 Dx]
                Subsystem: Mellanox Technologies MT2892 Family [ConnectX-6 Dx]
        ```
    2. Download the ISO image to your host.

        https://network.nvidia.com/products/infiniband-drivers/linux/mlnx_ofed/
    3. Install driver
        Unzip tarball and run:
        ```
        ./mlnxofedinstall
        ```

### Disk
* cleanup disk

    A single disk can usually be cleared with some or all of the steps below.
    ```BASH
    DISK="/dev/sdX"

    # Zap the disk to a fresh, usable state (zap-all is important, b/c MBR has to be clean)
    sgdisk --zap-all $DISK

    # Wipe a large portion of the beginning of the disk to remove more LVM metadata that may be present
    dd if=/dev/zero of="$DISK" bs=1M count=100 oflag=direct,dsync

    # SSDs may be better cleaned with blkdiscard instead of dd
    blkdiscard $DISK

    # Inform the OS of partition table changes
    partprobe $DISK
    ```

### NIC configure 
* Mellanox
    ```
    # mlnx_qos -i enp104s0f0np0
    DCBX mode: OS controlled
    Priority trust state: dscp
    dscp2prio mapping:
            prio:0 dscp:07,06,05,04,03,02,01,00,
            prio:1 dscp:15,14,13,12,11,10,09,08,
            prio:2 dscp:23,22,21,20,19,18,17,16,
            prio:3 dscp:31,30,29,28,27,26,25,24,
            prio:4 dscp:39,38,37,36,35,34,33,32,
            prio:5 dscp:47,46,45,44,43,42,41,40,
            prio:6 dscp:55,54,53,52,51,50,49,48,
            prio:7 dscp:63,62,61,60,59,58,57,56,
    default priority:
    Receive buffer size (bytes): 20016,156096,0,0,0,0,0,0,max_buffer_size=1027728
    Cable len: 7
    PFC configuration:
            priority    0   1   2   3   4   5   6   7
            enabled     0   0   0   1   0   0   0   0
            buffer      0   0   0   1   0   0   0   0
    tc: 0 ratelimit: unlimited, tsa: ets, bw: 10%
            priority:  1
    tc: 1 ratelimit: unlimited, tsa: ets, bw: 10%
            priority:  0
    tc: 2 ratelimit: unlimited, tsa: ets, bw: 10%
            priority:  2
    tc: 3 ratelimit: unlimited, tsa: ets, bw: 50%
            priority:  3
    tc: 4 ratelimit: unlimited, tsa: ets, bw: 10%
            priority:  4
    tc: 5 ratelimit: unlimited, tsa: ets, bw: 10%
            priority:  5
    tc: 6 ratelimit: 30.0 Gbps, tsa: strict
            priority:  6
    tc: 7 ratelimit: 20.0 Gbps, tsa: strict
            priority:  7
    ```

## K8s
K8s Manually installation
- metallb
    ```
    helm repo add metallb https://metallb.github.io/metallb
    helm install metallb metallb/metallb
    ```
- ingress nginx
    ```
    helm upgrade --install ingress-nginx ingress-nginx \
        --repo https://kubernetes.github.io/ingress-nginx \
        --namespace ingress-nginx --create-namespace
    ```
- Keycloak
    ```
    helm repo add bitnami https://charts.bitnami.com/bitnami
    helm repo update
    kubectl create namespace keycloak
    helm install keycloak bitnami/keycloak -f keycloak-values.yml --namespace keycloak
    ```
- Oauth proxy
    ```
    helm repo add oauth2-proxy https://oauth2-proxy.github.io/manifests
    helm repo update
    kubectl create namespace oauth2-proxy
    helm install oauth2-proxy oauth2-proxy/oauth2-proxy -f oauth2-proxy-values.yml --namespace oauth2-proxy
    ```
- storage
- gpu driver
    * H100 (support MIG)

        ```
        helm install --wait --generate-name \
            -n gpu-operator --create-namespace \
            nvidia/gpu-operator \
            --version=v24.9.1 \
            --set mig.strategy=single
        ```
    * L40s
        ```
        helm install --wait --generate-name \
            -n nvidia-gpu-operator --create-namespace \
            nvidia/gpu-operator \
            --version=v24.9.1 \
        ```

## Uplayer application
postgresql
```
helm install postgresql oci://registry-1.docker.io/bitnamicharts/postgresql --namespace postgresql --create-namespace --set global.postgresql.auth.postgresPassword=mystic
```
qdrant
```
helm repo add qdrant https://qdrant.to/helm
helm install qdrant qdrant/qdrant --namespace qdrant --create-namespace
```
rabbitmq
```
helm install rabbitmq oci://registry-1.docker.io/bitnamicharts/rabbitmq --namespace rabbitmq --create-namespace --set auth.password=mystic
```

## Stack Caperbility

### object storage

### DB
- svc: postgresql.postgresql.svc.cluster.local:5432
- cred: postgres/mystic 

### MQ
- svc: rabbitmq.rabbitmq.svc.cluster.local:5672
- cred: user:mystic
- ErLang cookie: 6fMyM9xKNslY9EighRtoLqEQUl2zSicu

### vector db
- svc: qdrant.qdrant.svc.cluster.local:6333

### LLM model
* Spec
    - Inference Server

     

        LLMs are served by [vLLM](https://docs.vllm.ai/en/latest/) [v0.6.4.post1](https://github.com/vllm-project/vllm/releases/tag/v0.6.4.post1)
    - Models

        | Name | GPU Uasage | Link | Comments|
        |:--|:--|:--|:--|
        |Llama-3.3-70B-Instruct| 4 * H100 SXM 80GB| [Link](https://huggingface.co/meta-llama/Llama-3.3-70B-Instruct) | Provides similar performance to Llama 3.1 405B, but at a significantely lower cost |
        |Llama-3-Groq-8B-Tool-Use|1 * H100 SXM 80GB |[Link](https://huggingface.co/Groq/Llama-3-Groq-8B-Tool-Use)| Designed for advanced tool use and function calling tasks |
        |Llama-3.1-8B-Instruct|1 * H100 SXM 80GB|[Link](https://huggingface.co/meta-llama/Llama-3.1-8B-Instruct)| Optimized for multilingual dialogue use cases|
        |bge-m3|1 * H100 SXM 80GB|[Link](https://huggingface.co/BAAI/bge-m3)| For embedding tasks |
        |bge-reranker-v2-m3 |1 * H100 SXM 80GB|[Link](https://huggingface.co/BAAI/bge-reranker-v2-m3)| For reranking tasks |
        
    
        Comparison of performance between models:

        | Model	| Flexible Match Accuracy | Exact Match Accuracy|
        |:--|:--|:--|
        |Llama-3.1-8B-Instruct | 77.71% | 20.39%|
        |Llama-3.3-70B-Instruct| 88.78% | 9.63% |


        Flexible Match: The 70B model performs better, indicating stronger reasoning abilities.

        Exact Match: Surprisingly, the 70B model performs worse, potentially due to more complex or liberal generated answers.

        Overall, the 70B model is suitable for tasks requiring strong reasoning abilities, but further optimization is needed for format-sensitive tasks.

        

* Access:
    - Internal
        * http://llama-3-3-70b-instruct-service.default.svc.cluster.local:8000
        * http://llama-3-groq.default.svc.cluster.local:8000
        * http://llama3-1.svc.cluster.local:8000
    - External
        * llama-3-3-70b-instruct-service: http://192.168.201.118:30121
        * llama-3-groq: http://192.168.201.118:30123
        * llama3-1: http://192.168.201.118:30122

### Keycloak
* Console:

    https://sso.projectx.eric.com admin:yvQx5ikdR8

### Grafana
http://192.168.201.116:32322 admin:prom-operator


### functional as a service

### Auth
* ProjectX

    smaple: https://web.projectx.eric.com test:test


### Monitoring
Ceph: [Cluster Stats](http://192.168.201.116:32322/d/tbO9LAiZK/ceph-cluster?var-interval=$__auto&orgId=1&from=now-6h&to=now&var-DS_PROMETHEUS=prometheus&refresh=1m)

Postgres: [CloudNativePG](http://192.168.201.116:32322/d/cloudnative-pg/cloudnativepg?orgId=1&from=now-7d&to=now&var-DS_PROMETHEUS=prometheus&var-operatorNamespace=gpu-operator&var-namespace=database&var-cluster=database-cluster&var-instances=$__all&refresh=30s)

vLLM: [vLLM](http://192.168.201.116:32322/d/b281712d-8bff-41ef-9f3f-71ad43c05e9b/vllm?orgId=1&from=now-5m&to=now&var-model_name=meta-llama%2FLlama-3.1-8B-Instruct)

GPU: [NVIDIA DCGM Exporter Dashboard](http://192.168.201.116:32322/d/Oxed_c6Wz/nvidia-dcgm-exporter-dashboard?orgId=1&from=now-15m&to=now&var-instance=10.233.70.245:9400&var-gpu=$__all)

