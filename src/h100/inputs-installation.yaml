# Uncomment following line to config kubeconfig 
# kubeconfig: l40s-kubeconfig

# nvidia-gpu-operator:
#   version: v24.9.1

# ceph:
#   version: release-1.15

metallb:
  ingress-ip-start: *************
  ingress-ip-end: *************
  egress-interface: eno12399.1010

keycloak:
  host: sso.projectx.eypoc.com
  adminUser: admin
  realm: projectx
  client:
    id: oauth2-proxy
    redirectUris:
      - https://oauth2-proxy.projectx.eypoc.com/oauth2/callback
    audienceMapper:
      name: aud-mapper-oauth2-proxy
      audience: oauth2-proxy
  testUser:
    username: test
    password: test

oauth2-proxy:
  hostname: oauth2-proxy.projectx.eypoc.com
  keycloak:
    host: sso.projectx.eypoc.com
    realm: projectx
    clientId: oauth2-proxy
  ingress:
    className: nginx

postgresql:
  password: mystic

# qdrant:

rabbitmq:
  password: mystic

hfApiToken: <hugging face token>