# Prerequisites
* If you have only one node in your cluster, you need at least 6 CPUs, 6 GB of memory, and 30 GB of disk storage.
* If you have multiple nodes in your cluster, for each node you need at least 2 CPUs, 4 GB of memory, and 20 GB of disk storage.
* You have a cluster that uses Kubernetes v1.28 or newer.
* You have installed the kubectl CLI.
* Your Kubernetes cluster must have access to the internet, because Kubernetes needs to be able to fetch images.

# Installation
* Step1: Install the latest stable Operator release
    
    Run the command:
    ```
    kubectl apply -f https://github.com/knative/operator/releases/download/knative-v1.16.0/operator.yaml
    ```
* Step2: Verify Knative Operator installation

    If the Operator is installed correctly, the deployment shows a `Ready` status
    ```
    # kubectl get deployment knative-operator -n  knative-operator
    NAME               READY   UP-TO-DATE   AVAILABLE   AGE
    knative-operator   1/1     1            1           106s
    ```
* Step3: Install Knative Serving
    
    Create following yaml file:
    ```
    apiVersion: v1
    kind: Namespace
    metadata:
      name: knative-serving
    ---
    apiVersion: operator.knative.dev/v1beta1
    kind: KnativeServing
    metadata:
      name: knative-serving
      namespace: knative-serving
    ```
    Apply the YAML file by running the command:
    ```
    kubectl apply -f <filename>.yaml
    ```
* Step4: Install the networking layer

    Create following yaml file:
    ```
    apiVersion: operator.knative.dev/v1beta1
    kind: KnativeServing
    metadata:
      name: knative-serving
      namespace: knative-serving
    spec:
      ingress:
        kourier:
          enabled: true
      config:
        network:
          ingress-class: "kourier.ingress.networking.knative.dev"
    ```
    Apply the YAML file for your Serving CR by running the command:
    ```
    kubectl apply -f <filename>.yaml
    ```
    Fetch the External IP or CNAME by running the command:
    ```
    kubectl --namespace knative-serving get service kourier
    ```
    Example output:
    ```
    # kubectl --namespace knative-serving get service kourier
    NAME      TYPE           CLUSTER-IP      EXTERNAL-IP       PORT(S)                      AGE
    kourier   LoadBalancer   *************   ***************   80:30827/TCP,443:31296/TCP   29s
    ```
* Step5: Verify the Knative Serving deployment
    
    Monitor the Knative deployments:
    ```
    kubectl get deployment -n knative-serving
    ```
    If Knative Serving has been successfully deployed, all deployments of the Knative Serving will show READY status. Here is a sample output:
    ```
    # kubectl get deployment -n knative-serving
    NAME                     READY   UP-TO-DATE   AVAILABLE   AGE
    3scale-kourier-gateway   1/1     1            1           3m1s
    activator                1/1     1            1           6m13s
    autoscaler               1/1     1            1           6m13s
    autoscaler-hpa           1/1     1            1           6m11s
    controller               1/1     1            1           6m12s
    net-kourier-controller   1/1     1            1           3m2s
    webhook                  1/1     1            1           6m12s
    ```
    Check the status of Knative Serving Custom Resource:
    ```
    kubectl get KnativeServing knative-serving -n knative-serving
    ```
    If Knative Serving is successfully installed, you should see:
    ```
    # kubectl get KnativeServing knative-serving -n knative-serving
    NAME              VERSION   READY   REASON
    knative-serving   1.16.0    True
    ```
* Step6: Config DNS for the knative ingress
    Once your DNS provider has been configured, add `spec.config.domain` into your existing Serving CR, and apply it:
    ```
    # Replace knative.example.com with your domain suffix
    apiVersion: operator.knative.dev/v1alpha1
    kind: KnativeServing
    metadata:
      name: knative-serving
      namespace: knative-serving
    spec:
    # ...
      config:
        domain:
          "knative.example.com": ""
    ...
    ```
    You can config `KnativeServing` CR by using command below:
    ```
    kubectl edit KnativeServing knative-serving -n knative-serving
    ```
# Deploy Function
* Step1: Creating function

    Create a function project by using the kn func plugin:
    ```
    kn func create -l <language> <function-name>

    ```
    example:
    ```
    kn func create -l python hello
    ```
* Step2: building function

    Enter the function folder
    ```
    cd hello
    ```
    Build the function
    ```
    kn func build
    ```
    Example:
    ```
    # kn func build
    Building function image
    Still building
    🙌 Function built: ************:9443/hello:latest
    ```
* Step3: Run function locally
    ```
    kn func run
    ```
    Example:
    ```
    # kn func run
    function up-to-date. Force rebuild with --build
    Running on host port 8080
    ```
    From other terminal:
    ```
    # curl -vvv http://localhost:8080
    * Host localhost:8080 was resolved.
    * IPv6: ::1
    * IPv4: 127.0.0.1
    *   Trying [::1]:8080...
    * connect to ::1 port 8080 from ::1 port 46318 failed: Connection refused
    *   Trying 127.0.0.1:8080...
    * Connected to localhost (127.0.0.1) port 8080
    > GET / HTTP/1.1
    > Host: localhost:8080
    > User-Agent: curl/8.5.0
    > Accept: */*
    >
    < HTTP/1.1 200 OK
    < Content-Length: 2
    < Content-Type: text/html; charset=utf-8
    < Date: Tue, 17 Dec 2024 07:48:18 GMT
    < Server: waitress
    <
    * Connection #0 to host localhost left intact
    {}
    ```
    The following output can be saw in the first terminal:
    ```
    # kn func run
    function up-to-date. Force rebuild with --build
    Running on host port 8080
    Received request
    GET http://localhost:8080/ localhost:8080
      Host: localhost:8080
      User-Agent: curl/8.5.0
      Accept: */*
    URL Query String:
      {}
    ```
* Step4: Deploy function locally

    Deploy the function by running the command inside the project directory:
    ```
    kn func deploy --namespace <namespace>
    ```
    Example:
    ```
    # kn func deploy --namespace default
    function up-to-date. Force rebuild with --build
    Pushing function image to the registry "************:9443" using the "admin" user credentials
    🎯 Creating Triggers on the cluster
    ✅ Function deployed in namespace "default" and exposed at URL:
        http://hello.default.knative.projectx.eric.com
    ```
    Verify deployed function
    ```
    # curl http://hello.default.knative.projectx.eric.com
    {}
    ```
    Check pods status under `default` namespace, we can see a pod with `hello` prefix.
    ```
    # kubectl get pods -n default
    NAME                                      READY   STATUS    RESTARTS       AGE
    hello-00001-deployment-5799c546f7-bpncd   2/2     Running   0              17s
    ```
