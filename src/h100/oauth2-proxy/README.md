# Installation
* Step 1: Add Helm repository:
    ```
    helm repo add oauth2-proxy https://oauth2-proxy.github.io/manifests
    ```
* Step 2: Fetch the latest charts from the repository:

    ```
    helm repo update
    ```
* Step 3: Create namespace

    ```
    k<PERSON><PERSON>l create namespace oauth2-proxy
    ```
* Step 4: Create file `values.yml`

Copy `Client Secret` from keycloak.
![client](./assets/client4.png)

Replace `<client-secret>` in the secret with `Client Secret`

```yaml
config:
  # Add config annotations
  annotations: {}
  # OAuth client ID
  # Follow instructions to configure Keycloak client
  # https://oauth2-proxy.github.io/oauth2-proxy/configuration/providers/keycloak_oidc

  # Oauth2 client configuration. From Keycloak configuration
  clientID: "oauth2-proxy"
  clientSecret: "<client-secret>"

  # Cookie secret
  # Create a new secret with the following command
  # openssl rand -base64 32 | head -c 32 | base64
  cookieSecret: "bG5pRDBvL0VaWis3dksrZ05vYnJLclRFb2VNcVZJYkg="
  # The name of the cookie that oauth2-proxy will create
  # If left empty, it will default to the release name
  cookieName: "oauth2-proxy"

  # Config file
  configFile: |-
    # Provider config
    provider="keycloak-oidc"
    provider_display_name="Keycloak"
    redirect_url="https://oauth2-proxy.projectx.eypoc.com/oauth2/callback"
    oidc_issuer_url="https://sso.projectx.eypoc.com/realms/projectx"
    code_challenge_method="S256"
    ssl_insecure_skip_verify=true
    # Upstream config
    http_address="0.0.0.0:4180"
    upstreams="file:///dev/null"
    email_domains=["*"]
    cookie_domains=["projectx.eypoc.com"]
    cookie_secure=false
    scope="openid"
    whitelist_domains=[".projectx.eypoc.com"]
    insecure_oidc_allow_unverified_email="true"
    # Header config
    pass_user_headers=true
    set_xauthrequest=true
    set_authorization_header=true
    pass_access_token=true
    pass_authorization_header=true
    #prefer_email_to_user=true
    reverse_proxy=true

sessionStorage:
  # Can be one of the supported session storage cookie|redis
  type: redis
# Enabling redis backend installation
redis:
  enabled: true
  # standalone redis. No cluster
  architecture: standalone

ingress:
  enabled: true
  className: "nginx"
  pathType: Prefix
  path: /oauth2
  annotations:
    # Enable cert-manager to create automatically the SSL certificate and store in Secret
    # Possible Cluster-Issuer values:
    #   * 'letsencrypt-issuer' (valid TLS certificate using IONOS API)
    #   * 'ca-issuer' (CA-signed certificate, not valid)
    nginx.ingress.kubernetes.io/proxy-buffer-size: "16k"
  hosts:
    - oauth2-proxy.projectx.eypoc.com
  tls:
    - hosts:
        - oauth2-proxy.projectx.eypoc.com

```
* Step 5: Install helm chart
    ```
    helm install oauth2-proxy oauth2-proxy/oauth2-proxy -f values.yml --namespace oauth2-proxy
    ```
* Step 6: Check status oauth2-proxy PODs
    ```
    kubectl --namespace=oauth2-proxy get pods -l "app=oauth2-proxy"
    ```