# Configuration

## On the Jumphost
```
echo "************* sso.projectx.eypoc.com oauth2-proxy.projectx.eypoc.com chat.projectx.eypoc.com" >> /etc/hosts
```

## On K8s


```
# kubectl get cm -n kube-system coredns -o yaml
apiVersion: v1
data:
  Corefile: |
    .:53 {
        errors {
        }
        health {
            lameduck 5s
        }
        ready
        kubernetes cluster.local in-addr.arpa ip6.arpa {
          pods insecure
          fallthrough in-addr.arpa ip6.arpa
        }
        hosts custom.hosts sso.projectx.eric.com {
          *************** sso.projectx.eric.com
          *************** master01.projectx.eric.com
          *************** knative.projectx.eric.com
          fallthrough
        }

        prometheus :9153
        forward . *********** {
          prefer_udp
          max_concurrent 1000
        }
        cache 30

        loop
        reload
        loadbalance
    }
kind: ConfigMap
metadata:
  name: coredns
  namespace: kube-system
```

```
# kubectl get cm -n kube-system nodelocaldns -o yaml
root@node1:~# kubectl get cm -n kube-system nodelocaldns -o yaml
apiVersion: v1
data:
  Corefile: |
    cluster.local:53 {
        errors
        cache {
            success 9984 30
            denial 9984 5
        }
        reload
        loop
        bind *************
        forward . ********** {
            force_tcp
        }
        prometheus :9253
        health *************:9254
    }
    in-addr.arpa:53 {
        errors
        cache 30
        reload
        loop
        bind *************
        forward . ********** {
            force_tcp
        }
        prometheus :9253
    }
    ip6.arpa:53 {
        errors
        cache 30
        reload
        loop
        bind *************
        forward . ********** {
            force_tcp
        }
        prometheus :9253
    }
    .:53 {
        errors
        cache 30
        reload
        loop
        bind *************
        hosts custom.hosts sso.projectx.eypoc.com {
          ************* sso.projectx.eypoc.com
          ************* oauth2-proxy.projectx.eypoc.com
          ************* node1.projectx.eypoc.com
          ************* knative.projectx.eypoc.com
          fallthrough
        }
        forward . /etc/resolv.conf
        prometheus :9253
    }
kind: ConfigMap
metadata:
  name: nodelocaldns
  namespace: kube-system
```