# Environment
After following the INSTALLATION.md, we will get below environment.

## LLM Model Serving
|||
|-|-|
|API base|http://llama3-1.default.svc.cluster.local:8000/v1|
|Model|meta-llama/Llama-3.1-8B-Instruct|

## Embedding Model Serving
|||
|-|-|
|API|http://bge-m3.default.svc.cluster.local:8000/v1/embeddings|
|Model|BAAI/bge-m3|

## PostgreSQL
|||
|-|-|
|Host|postgresql.postgresql.svc.cluster.local|
|Port|5432|
|User|postgres|
|Password|mystic|

## Qdrant
|||
|-|-|
|Host|qdrant.qdrant.svc.cluster.local|
|Port|6333|



# Deplohy sample RAG application
## (Optional) Build application docker image
Modify the registry host and image name/tag according to you need
```bash
## get the source code and jump into that folder
docker build -t eypoc .
docker tag eypoc ************:9443/dell/eypoc:0.0.27
docker push ************:9443/dell/eypoc:0.0.27
```

## Create db and table
kubectl exec to postgres container
```
kubectl exec -it -n postgresql postgresql-0 -- bash
# in container bash
psql -U postgres
# type password: mystic
```
Then paste and execute below sql script
```sql
CREATE DATABASE knowledge_base;

\c knowledge_base;

CREATE TABLE documents (
    id SERIAL PRIMARY KEY,
    knowledge_base VARCHAR(255),
    doc_id TEXT,
    filename VARCHAR(255)
);
```
## Deploy ns/deployment/svc
eypoc.yaml
```yaml
apiVersion: v1
kind: Namespace
metadata:
  name: eypoc

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: eypoc
  namespace: eypoc
spec:
  replicas: 1
  selector:
    matchLabels:
      app: eypoc
  template:
    metadata:
      labels:
        app: eypoc
    spec:
      containers:
      - name: eypoc
        image: ************:9443/dell/eypoc:0.0.27
        ports:
        - containerPort: 5000
        env:
        - name: QDRANT_HOST
          value: 'qdrant.qdrant.svc.cluster.local'
        - name: QDRANT_PORT
          value: '6333'
        - name: POSTGRES_DBNAME
          value: 'knowledge_base'
        - name: POSTGRES_USER
          value: 'postgres'
        - name: POSTGRES_PASSWORD
          value: 'mystic'
        - name: POSTGRES_HOST
          value: 'postgresql.postgresql.svc.cluster.local'
        - name: POSTGRES_PORT
          value: '5432'
        - name: LLM_CONFIGS
          value: '{"llm1":{"api":"http://llama3-1.default.svc.cluster.local:8000/v1","model":"meta-llama/Llama-3.1-8B-Instruct"}}'
        - name: EMBEDDING_API_URL
          value: 'http://bge-m3.default.svc.cluster.local:8000/v1/embeddings'
        - name: EMBEDDING_MODEL
          value: 'BAAI/bge-m3'
      imagePullSecrets:
      - name: nexus-registry-secret
---
apiVersion: v1
kind: Service
metadata:
  name: eypoc
  namespace: eypoc
spec:
  selector:
    app: eypoc
  ports:
    - protocol: TCP
      port: 80
      targetPort: 5000
      nodePort: 30888
  type: NodePort

```
Modify the registry host/port and image name/tag accordingly. Then execute"

```
kubectl apply -f eypoc.yaml
```

## Ingress configuration
ingress-chat.yaml
```yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: chat-ingress
  namespace: eypoc
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/auth-signin: https://oauth2-proxy.projectx.eypoc.com/oauth2/start?rd=https://$host$request_uri
    nginx.ingress.kubernetes.io/auth-url: http://oauth2-proxy.oauth2-proxy.svc.cluster.local/oauth2/auth
    nginx.ingress.kubernetes.io/proxy-body-size: "0"
    nginx.ingress.kubernetes.io/proxy-request-buffering: "off"
    nginx.ingress.kubernetes.io/auth-response-headers: "Authorization,X-Auth-Request-User,X-Auth-Request-Email,X-Auth-Request-Groups, X-Auth-Request-Preferred-Username"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - chat.projectx.eypoc.com
  rules:
  - host: chat.projectx.eypoc.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: eypoc
            port:
              number: 5000

```
```
kubectl apply -f ingress-chat.yaml
```

## DNS config
Add dns record in /etc/hosts in jumphost, it should be like:
```
************* sso.projectx.eypoc.com oauth2-proxy.projectx.eypoc.com chat.projectx.eypoc.com
``` 

# Try it
Visit http://chat.projectx.eypoc.com.

Upload a pdf file to create a knowledge base, and then using this knowledge to chat.