#!/bin/bash

# WARNING WARNING WARNING
# This script is generated by AI from INSTALLATION.md
# This script is not validated yet.
# This script is not validated yet.
# This script is not validated yet.
# Please do not run this script directly.
# Please do not run this script directly.
# Please do not run this script directly.
# WARNING WARNING WARNING


set -e

# Function to check the command result and capture errors
check_command() {
    if [ $? -ne 0 ]; then
        echo "ERROR: $1 failed."
        exit 1
    else
        echo "$1 succeeded."
    fi
}

# Function to check kubernetes cluster status
check_k8s_cluster() {
    # Check if kubectl is installed
    if ! command -v kubectl &> /dev/null; then
        echo "kubectl not found, installing..."
        curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
        chmod +x kubectl
        sudo mv kubectl /usr/local/bin/
        check_command "kubectl installation"
    else
        echo "kubectl is installed, version: $(kubectl version --client --short)"
    fi

    # Use custom kubeconfig if specified in the config file
    KUBECONFIG=$(read_yaml_value '.kubeconfig')
    if [ $? -eq 0 ] && [ ! -z "$KUBECONFIG" ] && [ "$KUBECONFIG" != "null" ]; then
        if [ ! -f "${KUBECONFIG}" ]; then
            echo "Error: specified kubeconfig file does not exist: ${KUBECONFIG}"
            exit 1
        fi
        echo "Using kubeconfig from config file: ${KUBECONFIG}"
        export KUBECONFIG="${KUBECONFIG}"
        # Ensure helm uses the same kubeconfig
        export HELM_KUBECONFIG="${KUBECONFIG}"
    fi

    echo "Checking Kubernetes cluster status..."
    kubectl get node
    check_command "Kubernetes node status check"
    kubectl get pod -A
    check_command "Kubernetes pod status check"
}

# Function to install Helm
install_helm() {
    # First check if helm is already installed
    if command -v helm &> /dev/null; then
        echo "Helm is already installed, version: $(helm version --short)"
        # Verify helm is working
        helm list &> /dev/null
        check_command "Helm availability check"
        return 0
    fi

    echo "Installing Helm..."
    curl -fsSL -o get_helm.sh https://raw.githubusercontent.com/helm/helm/master/scripts/get-helm-3
    check_command "Download Helm script"
    chmod 700 get_helm.sh
    ./get_helm.sh
    check_command "Helm installation"

    # Verify helm is working after installation
    helm list &> /dev/null
    check_command "Post-installation helm availability check"
}

# Function to install NVIDIA Driver
install_nvidia_driver() {
    echo "Starting NVIDIA Driver installation process..."
    
    # Ensure required dependencies
    if ! ensure_dependencies jq; then
        echo "ERROR: Failed to install required dependencies"
        return 1
    fi
    
    # Function to verify GPU setup
    verify_gpu_setup() {
        echo "Verifying GPU Operator status..."
        # Wait longer for the validator pods to be created
        sleep 30
        
        # Check if validator pods exist before waiting
        if ! kubectl get pod -l app=nvidia-operator-validator -n nvidia-gpu-operator &> /dev/null; then
            echo "ERROR: GPU Operator validator pods not found"
            return 1
        fi

        if ! kubectl wait --for=condition=ready pod -l app=nvidia-operator-validator -n nvidia-gpu-operator --timeout=300s; then
            echo "ERROR: GPU Operator validator is not ready"
            return 1
        fi

        echo "Verifying node GPU resources..."
        if ! kubectl get nodes -o json | jq -r '.items[].status.allocatable."nvidia.com/gpu"' | grep -q "[0-9]"; then
            echo "ERROR: No available GPU resources detected"
            return 1
        fi

        echo "Running nvidia-smi test..."
        if ! kubectl run nvidia-smi --rm -i --tty --restart=Never \
            --image=nvidia/cuda:11.6.2-base-ubuntu20.04 \
            --command -- nvidia-smi; then
            echo "ERROR: nvidia-smi test failed"
            return 1
        fi

        echo "GPU verification completed: All tests passed"
        return 0
    }

    # Function to cleanup GPU operator
    cleanup_gpu_operator() {
        echo "Cleaning up GPU operator installation..."
        
        # Delete helm release if exists
        if helm list -n nvidia-gpu-operator | grep -q "gpu-operator"; then
            echo "Removing existing Helm release..."
            helm uninstall -n nvidia-gpu-operator $(helm list -n nvidia-gpu-operator -q)
            sleep 10
        fi
        
        # Delete namespace if exists
        if kubectl get namespace nvidia-gpu-operator &> /dev/null; then
            echo "Removing nvidia-gpu-operator namespace..."
            kubectl delete namespace nvidia-gpu-operator --timeout=60s
            # Wait for namespace deletion
            for i in {1..30}; do
                if ! kubectl get namespace nvidia-gpu-operator &> /dev/null; then
                    break
                fi
                echo "Waiting for namespace deletion... ($i/30)"
                sleep 2
            done
        fi
        
        # Clean up any remaining CRDs
        echo "Cleaning up NVIDIA CRDs..."
        kubectl get crd | grep -i nvidia | awk '{print $1}' | xargs -r kubectl delete crd
        
        # Clean up cluster-wide resources
        echo "Cleaning up cluster-wide resources..."
        kubectl delete clusterrole gpu-operator --ignore-not-found
        kubectl delete clusterrolebinding gpu-operator --ignore-not-found
        
        # Clean up any remaining resources with the gpu-operator label
        echo "Cleaning up resources with gpu-operator label..."
        kubectl delete clusterrole -l app.kubernetes.io/instance=gpu-operator --ignore-not-found
        kubectl delete clusterrolebinding -l app.kubernetes.io/instance=gpu-operator --ignore-not-found
        kubectl delete serviceaccount -l app.kubernetes.io/instance=gpu-operator -n nvidia-gpu-operator --ignore-not-found
        
        sleep 10
        echo "Cleanup completed"
    }

    # Check if GPU operator is already installed
    if kubectl get pods -n nvidia-gpu-operator 2>/dev/null | grep -q "gpu-operator"; then
        echo "Detected existing NVIDIA GPU Operator installation, verifying status..."
        
        if verify_gpu_setup; then
            echo "Existing GPU Operator is functioning correctly, skipping installation"
            return 0
        else
            echo "Existing GPU Operator validation failed, preparing for reinstallation..."
            cleanup_gpu_operator
        fi
    fi

    # Add and update Helm repository
    echo "Adding NVIDIA Helm repository..."
    helm repo add nvidia https://helm.ngc.nvidia.com/nvidia
    check_command "Add NVIDIA Helm repository"
    
    echo "Updating Helm repositories..."
    helm repo update
    check_command "Update Helm repositories"

    # Install GPU operator
    echo "Installing NVIDIA GPU Operator..."
    cleanup_gpu_operator  # Ensure clean state before installation
    
    helm install --wait --generate-name nvidia/gpu-operator \
        --namespace nvidia-gpu-operator \
        --create-namespace \
        --version=v24.9.1 \
        --set driver.enabled=true \
        --timeout 15m
    check_command "Install NVIDIA GPU Operator"

    # Post-installation verification with retry
    echo "Performing post-installation verification..."
    for i in {1..3}; do
        echo "Verification attempt $i/3..."
        sleep 30  # Wait between attempts
        if verify_gpu_setup; then
            echo "NVIDIA Driver and GPU Operator installation and verification completed successfully"
            return 0
        fi
        echo "Verification attempt $i failed, retrying..."
    done

    echo "ERROR: Post-installation GPU verification failed after 3 attempts"
    return 1
}

# Function to safely clear all non-system disks on kubernetes nodes
clear_disks() {
    # Show warning and get confirmation
    echo "WARNING: This operation will PERMANENTLY ERASE ALL DATA on non-system disks!"
    echo "This includes:"
    echo "  - All partitions and partition tables"
    echo "  - All LVM configurations"
    echo "  - All data on these disks"

    if [ "$INTERACTIVE_MODE" = true ]; then
        read -p "Are you sure you want to continue? (y/N) " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo "Operation cancelled by user."
            return 1
        fi
    else
        echo "INTERACTIVE_MODE is false, proceeding with disk cleanup without confirmation."
    fi

    echo "Starting disk cleanup process..."
    
    # Create DaemonSet for disk cleanup
    cat <<EOF | kubectl apply -f -
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: disk-cleanup
  namespace: default
spec:
  selector:
    matchLabels:
      app: disk-cleanup
  template:
    metadata:
      labels:
        app: disk-cleanup
    spec:
      hostPID: true
      hostNetwork: true
      containers:
      - name: disk-cleanup
        image: ubuntu:latest
        securityContext:
          privileged: true
        command: ["/bin/bash", "-c"]
        args:
        - |
          # Install required tools (without systemd)
          DEBIAN_FRONTEND=noninteractive apt-get update -qq
          DEBIAN_FRONTEND=noninteractive apt-get install -y -qq --no-install-recommends gdisk util-linux lvm2 parted
          
          echo "[1/4] Analyzing current disk layout..."
          lsblk
          
          # Identify system disk to protect it
          echo "[2/4] Identifying system disk..."
          ROOT_DEVICE=\$(chroot /host findmnt -n -o SOURCE / 2>/dev/null || echo "/dev/\$(lsblk -no pkname \$(findmnt -n -o SOURCE /host))")
          echo "Root device: \$ROOT_DEVICE"
          
          # Handle both LVM and non-LVM configurations
          if [[ \$ROOT_DEVICE == *"/dev/mapper"* ]]; then
              echo "Detected LVM configuration"
              PART_NAME=\$(chroot /host dmsetup deps -o blkdevname \$ROOT_DEVICE | sed -E "s/.*\((.*)\)/\1/")
              SYSTEM_DISKS=\$(chroot /host lsblk -no pkname -p "/dev/\$PART_NAME" | grep "/dev/" | tail -n1)
          else
              SYSTEM_DISKS=\$(chroot /host lsblk -no pkname -p \$ROOT_DEVICE | grep "/dev/")
          fi
          
          if [ -z "\$SYSTEM_DISKS" ]; then
              echo "ERROR: Failed to identify system disk - aborting for safety"
              exit 1
          fi
          echo "Protected system disk: \$SYSTEM_DISKS"
          
          # Get list of non-system disks to process
          echo "[3/4] Identifying disks to process..."
          DISKS_TO_PROCESS=\$(lsblk -nd -o NAME,TYPE,SIZE | grep "disk" | \
              while read name type size; do
                  if [ "/dev/\$name" != "\$SYSTEM_DISKS" ] && [[ ! "\$name" =~ ^nbd ]]; then
                      echo "\$name"
                  fi
              done)
          
          DISK_COUNT=\$(echo "\$DISKS_TO_PROCESS" | wc -l)
          
          if [ \$DISK_COUNT -eq 0 ]; then
              echo "No non-system disks found to process"
              exit 0
          fi
          
          echo "Found \$DISK_COUNT disk(s) to process:"
          echo "\$DISKS_TO_PROCESS" | while read disk; do
              echo "  - /dev/\$disk \$(lsblk -dn -o SIZE /dev/\$disk)"
          done
          
          echo "[4/4] Processing non-system disks..."
          CURRENT_DISK=0
          
          for device in \$DISKS_TO_PROCESS; do 
              CURRENT_DISK=\$((CURRENT_DISK + 1))
              FULL_PATH="/dev/\$device"
              
              echo "Processing disk \$CURRENT_DISK/\$DISK_COUNT: \$FULL_PATH"
              
              if [ ! -e "\$FULL_PATH" ]; then
                  echo "Warning: Device \$FULL_PATH not found, skipping"
                  continue
              fi
              
              echo "Clearing disk: \$FULL_PATH"
              
              # Step 1: Clear partition table and signatures
              echo "  - Wiping partition table..."
              sgdisk --zap-all "\$FULL_PATH"
              
              # Step 2: Discard blocks if supported
              echo "  - Discarding blocks (if supported)..."
              blkdiscard "\$FULL_PATH" 2>/dev/null || true
              
              # Step 3: Update kernel partition table
              echo "  - Updating kernel partition table..."
              partprobe "\$FULL_PATH"
              
              if [ \$? -ne 0 ]; then 
                  echo "ERROR: Failed to clear \$FULL_PATH"
                  exit 1
              fi
              echo "Successfully cleared \$FULL_PATH"
          done
          
          echo "Disk cleanup completed successfully on node \$(hostname)"
          # Sleep briefly to ensure logs are captured
          sleep 5
          exit 0
        volumeMounts:
        - name: host
          mountPath: /host
        - name: dev
          mountPath: /dev
        - name: run
          mountPath: /run
      volumes:
      - name: host
        hostPath:
          path: /
      - name: dev
        hostPath:
          path: /dev
      - name: run
        hostPath:
          path: /run
      tolerations:
      - operator: Exists
EOF

    echo "Waiting for disk cleanup DaemonSet to complete..."
    
    # Wait for pods to be created
    sleep 10
    
    # Monitor the progress
    while true; do
        TOTAL_NODES=$(kubectl get nodes --no-headers | wc -l)
        COMPLETED_PODS=$(kubectl get pods -l app=disk-cleanup --no-headers | grep -c "Completed" || true)
        FAILED_PODS=$(kubectl get pods -l app=disk-cleanup --no-headers | grep -c "Error\|Failed" || true)
        RUNNING_PODS=$(kubectl get pods -l app=disk-cleanup --no-headers | grep -c "Running" || true)
        
        echo "Progress: $COMPLETED_PODS/$TOTAL_NODES nodes completed, $RUNNING_PODS running, $FAILED_PODS failed"
        
        if [ $FAILED_PODS -gt 0 ]; then
            echo "ERROR: Some pods failed during disk cleanup"
            echo "----------------------------------------"
            for pod in $(kubectl get pods -l app=disk-cleanup -o name); do
                node=$(kubectl get $pod -o jsonpath='{.spec.nodeName}')
                status=$(kubectl get $pod -o jsonpath='{.status.phase}')
                echo "Node: $node (Status: $status)"
                echo "----------------------------------------"
                kubectl logs $pod
                echo "----------------------------------------"
            done
            kubectl delete ds disk-cleanup
            return 1
        fi
        
        if [ $COMPLETED_PODS -eq $TOTAL_NODES ]; then
            echo "All nodes completed disk cleanup successfully"
            break
        fi
        
        sleep 5
    done
    
    # Show logs from all pods
    echo "Disk cleanup logs:"
    echo "----------------------------------------"
    for pod in $(kubectl get pods -l app=disk-cleanup -o name); do
        node=$(kubectl get $pod -o jsonpath='{.spec.nodeName}')
        echo "Logs from node: $node"
        echo "----------------------------------------"
        kubectl logs $pod
        echo "----------------------------------------"
    done
    
    # Cleanup
    kubectl delete ds disk-cleanup
    
    echo "Disk cleanup completed successfully on all nodes"
}

# Function to cleanup Ceph resources
cleanup_ceph() {
    echo "Cleaning up Ceph resources..."
    
    # Store the initial directory
    INITIAL_DIR=$(pwd)

    # First cleanup object store related resources
    echo "Cleaning up object store resources..."
    
    # Remove finalizers from ObjectBucketClaim
    kubectl -n rook-ceph get obc -o name | \
        xargs -I {} kubectl patch -n rook-ceph {} --type merge -p '{"metadata":{"finalizers":[]}}' 2>/dev/null || true
    
    # Delete ObjectBucketClaims
    kubectl -n rook-ceph delete obc --all --force --grace-period=0 2>/dev/null || true
    
    # Remove finalizers from CephObjectStore
    kubectl -n rook-ceph get cephobjectstore -o name | \
        xargs -I {} kubectl patch -n rook-ceph {} --type merge -p '{"metadata":{"finalizers":[]}}' 2>/dev/null || true
    
    # Delete CephObjectStore
    kubectl -n rook-ceph delete cephobjectstore --all --force --grace-period=0 2>/dev/null || true
    
    # Clean up RGW resources
    echo "Cleaning up RGW resources..."
    for resource in deployment service secret; do
        kubectl delete $resource -l rgw=store-x -n rook-ceph --force --grace-period=0 --ignore-not-found || true
    done
    
    # Clean up any stuck object bucket resources
    echo "Cleaning up stuck object bucket resources..."
    kubectl get crd | grep -i 'objectbucket.io' | awk '{print $1}' | \
        xargs -I {} kubectl get {} -n rook-ceph -o name | \
        xargs -I {} kubectl patch {} -n rook-ceph --type=merge -p '{"metadata":{"finalizers":[]}}' || true
    
    # Wait for object store resources to be cleaned up
    echo "Waiting for object store resources to be cleaned up..."
    sleep 10
    
    # Now proceed with general Ceph cleanup
    echo "Proceeding with general Ceph cleanup..."
    
    # If we're in the examples directory, go back to rook root
    if [[ "$INITIAL_DIR" == */rook/deploy/examples ]]; then
        cd ../../..
        INITIAL_DIR=$(pwd)
    fi

    # Remove finalizers from CephCluster
    echo "Removing finalizers from CephCluster..."
    kubectl -n rook-ceph patch cephcluster rook-ceph --type merge \
        -p '{"metadata":{"finalizers":[]}}' 2>/dev/null || true
        
    # Remove finalizers from filesystem resources    
    kubectl -n rook-ceph get cephfilesystem -o name | \
        xargs -I {} kubectl patch -n rook-ceph {} --type merge -p '{"metadata":{"finalizers":[]}}' 2>/dev/null || true
    
    kubectl -n rook-ceph get cephfilesystemsubvolumegroup -o name | \
        xargs -I {} kubectl patch -n rook-ceph {} --type merge -p '{"metadata":{"finalizers":[]}}' 2>/dev/null || true

    # Remove finalizers from configmaps and secrets
    for resource in configmap secret; do
        kubectl get $resource -n rook-ceph -o name | \
            xargs -I {} kubectl patch -n rook-ceph {} --type merge -p '{"metadata":{"finalizers":[]}}' 2>/dev/null || true
    done

    # Delete Ceph resources in correct order
    echo "Deleting Ceph resources..."
    if [ -d "$INITIAL_DIR/rook/deploy/examples" ]; then
        cd "$INITIAL_DIR/rook/deploy/examples"
        
        # Delete in reverse order of creation
        kubectl delete -f csi/cephfs/storageclass.yaml --ignore-not-found 2>/dev/null || true
        kubectl delete -f filesystem.yaml --ignore-not-found 2>/dev/null || true
        kubectl delete -f cluster.yaml --ignore-not-found 2>/dev/null || true
        kubectl delete -f operator.yaml --ignore-not-found 2>/dev/null || true
        kubectl delete -f crds.yaml --ignore-not-found 2>/dev/null || true
        kubectl delete -f common.yaml --ignore-not-found 2>/dev/null || true
    fi

    # Clean up remaining CRDs
    echo "Cleaning up remaining Ceph CRDs..."
    kubectl get crd | grep -i 'ceph\|rook' | awk '{print $1}' | xargs -r kubectl delete crd --timeout=30s
    
    # Clean up /var/lib/rook directory on all nodes
    echo "Cleaning up /var/lib/rook directory on all nodes..."
    
    # Create cleanup DaemonSet
    cat <<EOF | kubectl apply -f -
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: rook-cleanup
  namespace: default
spec:
  selector:
    matchLabels:
      app: rook-cleanup
  template:
    metadata:
      labels:
        app: rook-cleanup
    spec:
      hostPID: true
      hostNetwork: true
      containers:
      - name: cleanup
        image: ubuntu:latest
        securityContext:
          privileged: true
        command: ["/bin/bash", "-c"]
        args:
        - |
          echo "Cleaning up /var/lib/rook on node \$(hostname)..."
          rm -rf /var/lib/rook
          echo "Cleanup completed on node \$(hostname)"
          sleep 5
          exit 0
        volumeMounts:
        - name: host-root
          mountPath: /var/lib/rook
          mountPropagation: Bidirectional
      volumes:
      - name: host-root
        hostPath:
          path: /var/lib/rook
      tolerations:
      - operator: Exists
EOF

    # Wait for cleanup DaemonSet to complete
    echo "Waiting for Rook cleanup DaemonSet to complete..."
    sleep 10
    
    # Monitor the progress
    while true; do
        TOTAL_NODES=$(kubectl get nodes --no-headers | wc -l)
        COMPLETED_PODS=$(kubectl get pods -l app=rook-cleanup --no-headers | grep -c "Completed" || true)
        FAILED_PODS=$(kubectl get pods -l app=rook-cleanup --no-headers | grep -c "Error\|Failed" || true)
        RUNNING_PODS=$(kubectl get pods -l app=rook-cleanup --no-headers | grep -c "Running" || true)
        
        echo "Progress: $COMPLETED_PODS/$TOTAL_NODES nodes completed, $RUNNING_PODS running, $FAILED_PODS failed"
        
        if [ $FAILED_PODS -gt 0 ]; then
            echo "ERROR: Some pods failed during Rook cleanup"
            kubectl delete ds rook-cleanup
            return 1
        fi
        
        if [ $COMPLETED_PODS -eq $TOTAL_NODES ]; then
            echo "Rook cleanup completed successfully on all nodes"
            break
        fi
        
        sleep 5
    done
    
    # Cleanup the DaemonSet
    kubectl delete ds rook-cleanup
    
    # Remove Rook directory
    rm -rf "$INITIAL_DIR/rook"
    
    # Return to initial directory
    cd "$INITIAL_DIR"
    
    echo "Cleanup completed"
}

# Function to install Ceph
install_ceph() {
    # Store the initial directory
    INITIAL_DIR=$(pwd)
    
    echo "Starting Ceph installation..."

    # Check if Ceph is already installed
    if kubectl get namespace rook-ceph &>/dev/null; then
        echo "Detected existing Ceph installation"
        read -p "Do you want to remove existing installation and continue? (y/N) " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            cleanup_ceph
        else
            echo "Installation cancelled"
            return 1
        fi
    fi

    # Clone Rook repository
    echo "Cloning Rook repository..."
    if [ -d "rook" ]; then
        rm -rf rook
    fi
    if ! git clone --depth 1 --single-branch --branch release-1.15 https://github.com/rook/rook.git; then
        echo "Failed to clone Rook repository"
        return 1
    fi
    check_command "Clone Rook repository"
    
    cd rook/deploy/examples || {
        echo "Failed to change directory to rook/deploy/examples"
        cd "$INITIAL_DIR"  # Return to initial directory before exit
        return 1
    }
    
    # Install CRDs and operator
    echo "Installing Ceph CRDs, Common Resources, and Operator..."
    if ! kubectl create -f crds.yaml -f common.yaml -f operator.yaml; then
        echo "Failed to create Ceph CRDs and operator"
        cleanup_ceph
        return 1
    fi
    check_command "Create Ceph CRDs, Common Resources, and Operator"
    
    # Wait for operator with increased timeout
    echo "Waiting for Ceph operator to be ready..."
    if ! kubectl wait --for=condition=ready pod -l app=rook-ceph-operator -n rook-ceph --timeout=300s; then
        echo "Timeout waiting for Ceph operator"
        cleanup_ceph
        return 1
    fi
    check_command "Wait for Ceph operator"
    
    # Create Ceph cluster with increased timeout
    echo "Creating Ceph Cluster..."
    if ! kubectl create -f cluster.yaml; then
        echo "Failed to create Ceph cluster"
        cleanup_ceph
        return 1
    fi
    check_command "Create Ceph cluster"
    
    # Wait for Ceph cluster to be ready with interactive retry
    echo "Waiting for Ceph cluster to be ready..."
    i=1
    max_attempts=60
    while true; do
        if kubectl -n rook-ceph get cephcluster -o jsonpath='{.items[0].status.phase}' | grep -q "Ready"; then
            echo "Ceph cluster is ready"
            break
        fi
        
        echo "Waiting for Ceph cluster... ($i/$max_attempts)"
        
        if [ $i -eq $max_attempts ]; then
            echo "Current Ceph cluster status:"
            kubectl -n rook-ceph get cephcluster -o yaml
            echo "Ceph operator logs:"
            kubectl -n rook-ceph logs -l app=rook-ceph-operator
            
            read -p "Timeout waiting for Ceph cluster. Continue waiting? (Y/n) " choice
            choice=${choice:-y}
            if [[ ! "$choice" =~ ^[Yy]$ ]]; then
                echo "Aborting installation..."
                cleanup_ceph
                return 1
            fi
            
            echo "Resetting wait counter..."
            i=0
        fi
        
        i=$((i + 1))
        sleep 20
    done

    # Create filesystem
    echo "Creating Ceph filesystem..."
    if ! kubectl apply -f filesystem.yaml; then
        echo "Failed to create Ceph filesystem"
        cleanup_ceph
        return 1
    fi
    check_command "Apply Ceph filesystem"
    
    # Wait for filesystem to be ready
    echo "Waiting for Ceph filesystem to be ready..."
    for i in {1..30}; do
        if kubectl -n rook-ceph get cephfilesystem -o jsonpath='{.items[0].status.phase}' | grep -q "Ready"; then
            break
        fi
        if [ $i -eq 30 ]; then
            echo "Timeout waiting for Ceph filesystem to be ready"
            cleanup_ceph
            return 1
        fi
        echo "Waiting for filesystem... ($i/30)"
        sleep 10
    done
    
    # Create storage class
    echo "Creating Ceph storage class..."
    if ! kubectl apply -f csi/cephfs/storageclass.yaml; then
        echo "Failed to create storage class"
        cleanup_ceph
        return 1
    fi
    check_command "Apply Ceph CSI storage class"
    
    # Set as default storage class
    echo "Setting Ceph storage class as default..."
    if ! kubectl patch storageclass rook-cephfs -p '{"metadata": {"annotations":{"storageclass.kubernetes.io/is-default-class":"true"}}}'; then
        echo "Failed to set default storage class"
        cleanup_ceph
        return 1
    fi
    check_command "Patch Ceph storage class"

    # Verify installation
    echo "Verifying Ceph installation..."
    if ! kubectl -n rook-ceph get cephcluster -o jsonpath='{.items[0].status.phase}' | grep -q "Ready"; then
        echo "Ceph cluster is not ready"
        cleanup_ceph
        return 1
    fi

    # Test storage class
    echo "Testing storage class..."
    cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: ceph-test-pvc
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 1Gi
  storageClassName: rook-cephfs
EOF

    # Wait for PVC to be bound
    for i in {1..10}; do
        if kubectl get pvc ceph-test-pvc -o jsonpath='{.status.phase}' | grep -q "Bound"; then
            echo "Storage class test successful"
            kubectl delete pvc ceph-test-pvc
            break
        fi
        if [ $i -eq 10 ]; then
            echo "Storage class test failed"
            kubectl delete pvc ceph-test-pvc
            cleanup_ceph
            return 1
        fi
        echo "Waiting for test PVC... ($i/10)"
        sleep 3
    done

    # Return to the initial directory at the end
    cd "$INITIAL_DIR"
    
    echo "Ceph installation completed successfully"
    return 0
}

# Function to create Ceph object store and bucket
create_ceph_bucket() {
    echo "Setting up Ceph Object Store and Bucket..."

    # Function to check if resource exists
    check_resource() {
        local kind=$1
        local name=$2
        local namespace=${3:-rook-ceph}
        kubectl get $kind $name -n $namespace >/dev/null 2>&1
    }

    # Function to cleanup existing resources
    cleanup_existing() {
        echo "Checking and cleaning up any existing resources..."
        
        # Delete NodePort Service
        if check_resource service rook-ceph-rgw-store-x-nodeport; then
            echo "Removing existing NodePort service..."
            kubectl delete service rook-ceph-rgw-store-x-nodeport -n rook-ceph --ignore-not-found
            sleep 10
        fi
        
        # Delete ObjectBucketClaim
        if check_resource objectbucketclaim ceph-bucket-x; then
            echo "Removing existing ObjectBucketClaim..."
            # First remove finalizer
            kubectl patch objectbucketclaim ceph-bucket-x -n rook-ceph --type=merge \
                -p '{"metadata":{"finalizers":[]}}' || true
            sleep 5
            
            # Delete the OBC
            kubectl delete objectbucketclaim ceph-bucket-x -n rook-ceph --ignore-not-found
            
            # Wait for deletion with timeout
            for i in {1..30}; do
                if ! check_resource objectbucketclaim ceph-bucket-x; then
                    echo "ObjectBucketClaim deleted successfully"
                    break
                fi
                if [ $i -eq 30 ]; then
                    echo "Warning: Failed to delete ObjectBucketClaim, continuing anyway..."
                    break
                fi
                echo "Waiting for ObjectBucketClaim deletion... ($i/30)"
                sleep 2
            done
        fi
        
        # Delete StorageClass
        if check_resource storageclass rook-ceph-bucket-x; then
            echo "Removing existing StorageClass..."
            kubectl delete storageclass rook-ceph-bucket-x --ignore-not-found
            sleep 5
        fi
        
        # Delete CephObjectStore
        if check_resource cephobjectstore store-x; then
            echo "Removing existing CephObjectStore..."
            
            # Check if already in deleting state
            if kubectl get cephobjectstore store-x -n rook-ceph -o jsonpath='{.status.phase}' | grep -q "Deleting"; then
                echo "CephObjectStore is already in deleting state, removing finalizers..."
                kubectl patch cephobjectstore store-x -n rook-ceph --type=json \
                    -p='[{"op": "remove", "path": "/metadata/finalizers"}]' || true
            else
                # Clean up pools only if not in deleting state
                echo "Cleaning up Ceph pools..."
                kubectl -n rook-ceph exec -it $(kubectl -n rook-ceph get pod -l app=rook-ceph-operator -o jsonpath='{.items[0].metadata.name}') -- bash -c \
                    'ceph osd pool ls | grep store-x | xargs -I {} ceph osd pool delete {} {} --yes-i-really-really-mean-it' || true
                
                # Then delete the object store
                echo "Deleting CephObjectStore..."
                kubectl delete cephobjectstore store-x -n rook-ceph --force --grace-period=0
            fi
            
            # Wait for deletion
            for i in {1..30}; do
                if ! check_resource cephobjectstore store-x; then
                    echo "CephObjectStore deleted successfully"
                    break
                fi
                if [ $i -eq 30 ]; then
                    echo "Warning: Failed to delete CephObjectStore, attempting emergency cleanup..."
                    kubectl patch cephobjectstore store-x -n rook-ceph -p '{"metadata":{"finalizers":null}}' --type=merge || true
                    break
                fi
                echo "Still waiting for CephObjectStore cleanup... ($i/30)"
                sleep 5
            done
            
            # Clean up related resources
            echo "Cleaning up related resources..."
            for resource in deployment service secret; do
                kubectl delete $resource -l rgw=store-x -n rook-ceph --force --grace-period=0 --ignore-not-found || true
            done
        fi
        
        # Additional cleanup for any stuck resources
        kubectl get crd | grep -i 'objectbucket.io' | awk '{print $1}' | \
            xargs -I {} kubectl get {} -n rook-ceph -o name | \
            xargs -I {} kubectl patch {} -n rook-ceph --type=merge -p '{"metadata":{"finalizers":[]}}' || true
        
        sleep 10
        echo "Cleanup completed"
    }

    # Function to verify object store is working
    verify_object_store() {
        echo "Verifying object store functionality..."
        
        # Wait for the object store to be ready
        echo "Waiting for CephObjectStore to be ready..."
        for i in {1..30}; do
            if kubectl get cephobjectstore store-x -n rook-ceph -o jsonpath='{.status.phase}' | grep -q "Ready"; then
                echo "CephObjectStore is ready"
                break
            fi
            if [ $i -eq 30 ]; then
                echo "ERROR: Timeout waiting for CephObjectStore to be ready"
                return 1
            fi
            echo "Waiting for CephObjectStore... ($i/30)"
            sleep 10
        done

        # Verify ObjectBucketClaim
        echo "Verifying ObjectBucketClaim..."
        for i in {1..30}; do
            if kubectl get objectbucketclaim ceph-bucket-x -n rook-ceph -o jsonpath='{.status.phase}' | grep -q "Bound"; then
                echo "ObjectBucketClaim is bound"
                break
            fi
            if [ $i -eq 30 ]; then
                echo "ERROR: Timeout waiting for ObjectBucketClaim to be bound"
                return 1
            fi
            echo "Waiting for ObjectBucketClaim to be bound... ($i/30)"
            sleep 10
        done

        # Create test pod to verify access
        echo "Creating test pod to verify object store access..."
        cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: Pod
metadata:
  name: ceph-test-pod
  namespace: rook-ceph
spec:
  containers:
  - name: ceph-test
    image: amazon/aws-cli
    command: ["/bin/sh", "-c"]
    args:
    - |
      # Wait for credentials to be available
      sleep 10
      
      # Print environment for debugging
      echo "Environment variables:"
      env | grep -E 'AWS_|BUCKET_'
      
      # Configure AWS CLI with debug mode
      aws configure set aws_access_key_id "\${AWS_ACCESS_KEY_ID}"
      aws configure set aws_secret_access_key "\${AWS_SECRET_ACCESS_KEY}"
      aws configure set default.region us-east-1
      
      # Ensure BUCKET_HOST has http:// prefix
      ENDPOINT="http://\${BUCKET_HOST}"
      echo "Using endpoint: \${ENDPOINT}"
      
      # Test endpoint connectivity
      echo "Testing endpoint connectivity..."
      curl -v "\${ENDPOINT}"
      
      # Create and upload test file with debug
      echo "Creating test file..."
      echo "test content" > test.txt
      
      echo "Attempting to upload file..."
      aws --debug --endpoint-url "\${ENDPOINT}" s3 cp test.txt s3://\${BUCKET_NAME}/test.txt
      
      if [ $? -eq 0 ]; then
          echo "Upload successful, attempting download..."
          aws --debug --endpoint-url "\${ENDPOINT}" s3 cp s3://\${BUCKET_NAME}/test.txt downloaded.txt
          
          if [ $? -eq 0 ]; then
              echo "Download successful, verifying content..."
              cat downloaded.txt
              
              echo "Cleaning up test file..."
              aws --endpoint-url "\${ENDPOINT}" s3 rm s3://\${BUCKET_NAME}/test.txt
              echo "Test completed successfully"
          else
              echo "ERROR: Failed to download file"
              exit 1
          fi
      else
          echo "ERROR: Failed to upload file"
          exit 1
      fi
    env:
    - name: AWS_ACCESS_KEY_ID
      valueFrom:
        secretKeyRef:
          name: ceph-bucket-x
          key: AWS_ACCESS_KEY_ID
    - name: AWS_SECRET_ACCESS_KEY
      valueFrom:
        secretKeyRef:
          name: ceph-bucket-x
          key: AWS_SECRET_ACCESS_KEY
    - name: BUCKET_NAME
      valueFrom:
        configMapKeyRef:
          name: ceph-bucket-x
          key: BUCKET_NAME
    - name: BUCKET_HOST
      valueFrom:
        configMapKeyRef:
          name: ceph-bucket-x
          key: BUCKET_HOST
EOF

        # Wait for test pod to complete
        echo "Waiting for test pod to complete..."
        if ! kubectl wait --for=condition=ready pod/ceph-test-pod -n rook-ceph --timeout=120s; then
            echo "ERROR: Test pod failed to start"
            kubectl describe pod ceph-test-pod -n rook-ceph
            kubectl logs ceph-test-pod -n rook-ceph
            kubectl delete pod ceph-test-pod -n rook-ceph
            return 1
        fi

        # Stream logs in real time
        kubectl logs -f ceph-test-pod -n rook-ceph

        # Check final status
        if ! kubectl logs ceph-test-pod -n rook-ceph | grep -q "Test completed successfully"; then
            echo "ERROR: Object store verification failed"
            echo "Pod description:"
            kubectl describe pod ceph-test-pod -n rook-ceph
            echo "Full logs:"
            kubectl logs ceph-test-pod -n rook-ceph
            kubectl delete pod ceph-test-pod -n rook-ceph
            return 1
        fi

        # Cleanup test pod
        kubectl delete pod ceph-test-pod -n rook-ceph

        echo "Object store verification completed successfully"
        return 0
    }

    # Start with cleanup
    cleanup_existing

    echo "Creating CephObjectStore..."
    cat <<EOF | kubectl apply -f -
apiVersion: ceph.rook.io/v1
kind: CephObjectStore
metadata:
  name: store-x
  namespace: rook-ceph
spec:
  metadataPool:
    failureDomain: osd
    replicated:
      size: 3
  dataPool:
    failureDomain: osd
    erasureCoded:
      dataChunks: 4
      codingChunks: 2
  preservePoolsOnDelete: true
  gateway:
    port: 80
    instances: 1
    # Add resource limits
    resources:
      limits:
        cpu: "2"
        memory: "2Gi"
      requests:
        cpu: "1"
        memory: "1Gi"
EOF
    check_command "Create Ceph Object Store"
    
    echo "Creating StorageClass..."
    cat <<EOF | kubectl apply -f -
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: rook-ceph-bucket-x
provisioner: rook-ceph.ceph.rook.io/bucket
reclaimPolicy: Delete
parameters:
  objectStoreName: store-x
  objectStoreNamespace: rook-ceph
EOF
    check_command "Create Ceph StorageClass"

    echo "Creating ObjectBucketClaim..."
    cat <<EOF | kubectl apply -f -
apiVersion: objectbucket.io/v1alpha1
kind: ObjectBucketClaim
metadata:
  name: ceph-bucket-x
  namespace: rook-ceph
spec:
  generateBucketName: ceph-bkt-x
  storageClassName: rook-ceph-bucket-x
EOF
    check_command "Create ObjectBucketClaim"
    
    echo "Creating NodePort Service..."
    cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: Service
metadata:
  name: rook-ceph-rgw-store-x-nodeport
  namespace: rook-ceph
spec:
  selector:
    app: rook-ceph-rgw
    ceph_daemon_id: store-x
    rgw: store-x
    rook_cluster: rook-ceph
    rook_object_store: store-x
  type: NodePort
  ports:
    - name: http
      port: 80
      targetPort: 8080
      protocol: TCP
      nodePort: 30480
EOF
    check_command "Create Ceph Object Store NodePort Service"

    # Verify the installation
    if ! verify_object_store; then
        echo "ERROR: Object store verification failed"
        return 1
    fi

    # Print usage information
    echo
    echo "Ceph Object Store has been successfully created and verified!"
    echo
    echo "To use the object store, get the credentials with:"
    echo "Access Key: kubectl get secret -n rook-ceph ceph-bucket-x -o jsonpath='{.data.AWS_ACCESS_KEY_ID}' | base64 -d"
    echo "Secret Key: kubectl get secret -n rook-ceph ceph-bucket-x -o jsonpath='{.data.AWS_SECRET_ACCESS_KEY}' | base64 -d"
    echo "Bucket Name: kubectl get cm -n rook-ceph ceph-bucket-x -o jsonpath='{.data.BUCKET_NAME}'"
    echo
    echo "Internal endpoint: http://rook-ceph-rgw-store-x.rook-ceph"
    echo "External endpoint: http://<node-ip>:30480"
    echo
}

# Function to install MetalLB
install_metallb() {
    echo "Installing MetalLB..."

    # Cleanup function for MetalLB resources
    cleanup_metallb() {
        echo "Cleaning up existing MetalLB installation..."
        
        # Remove finalizers from resources
        for resource in $(kubectl get -n metallb-system $(kubectl api-resources --namespaced=true --output=name | grep metallb.io) -o name 2>/dev/null); do
            kubectl patch -n metallb-system $resource -p '{"metadata":{"finalizers":[]}}' --type=merge 2>/dev/null || true
        done
        
        # Delete MetalLB namespace if exists
        if kubectl get namespace metallb-system &>/dev/null; then
            echo "Removing metallb-system namespace..."
            kubectl delete namespace metallb-system --timeout=60s
            # Wait for namespace deletion
            for i in {1..30}; do
                if ! kubectl get namespace metallb-system &>/dev/null; then
                    break
                fi
                echo "Waiting for namespace deletion... ($i/30)"
                sleep 2
            done
        fi
        
        # Remove CRDs
        echo "Removing MetalLB CRDs..."
        kubectl get crd -o name | grep metallb.io | xargs -r kubectl delete --timeout=60s 2>/dev/null || true
        
        # Remove cluster-wide resources
        echo "Removing cluster-wide resources..."
        kubectl delete clusterrole -l app.kubernetes.io/name=metallb --ignore-not-found
        kubectl delete clusterrolebinding -l app.kubernetes.io/name=metallb --ignore-not-found
        
        # Remove Helm release if exists
        if helm list -n metallb-system 2>/dev/null | grep -q "metallb"; then
            echo "Removing Helm release..."
            helm uninstall metallb -n metallb-system 2>/dev/null || true
        fi
        
        echo "Cleanup completed"
        sleep 5
    }

    # Run cleanup first
    cleanup_metallb

    # Add MetalLB Helm repository
    echo "Adding MetalLB Helm repository..."
    helm repo add metallb https://metallb.github.io/metallb
    check_command "Add MetalLB repository"
    
    echo "Updating Helm repositories..."
    helm repo update
    check_command "Update Helm repositories"

    # Create namespace
    echo "Creating metallb-system namespace..."
    kubectl create namespace metallb-system
    check_command "Create MetalLB namespace"

    # Install MetalLB using Helm
    echo "Installing MetalLB via Helm..."
    helm install metallb metallb/metallb \
        --namespace metallb-system \
        --wait \
        --timeout 300s
    check_command "Install MetalLB"

    # Wait for MetalLB pods with increased timeout
    echo "Waiting for MetalLB pods to be ready..."
    for i in {1..10}; do
        if kubectl wait --namespace metallb-system \
            --for=condition=ready pod \
            --selector=app.kubernetes.io/name=metallb \
            --timeout=60s; then
            break
        fi
        if [ $i -eq 10 ]; then
            echo "ERROR: Timeout waiting for MetalLB pods"
            return 1
        fi
        echo "Retry $i/10: Waiting for pods..."
        sleep 30
    done
    check_command "Wait for MetalLB pods"

    # Verify installation
    echo "Verifying MetalLB installation..."
    
    # Check pods status
    if ! kubectl get pods -n metallb-system | grep -q "Running"; then
        echo "ERROR: MetalLB pods are not running properly"
        kubectl get pods -n metallb-system
        return 1
    fi

    # Check CRDs
    required_crds=("addresspools.metallb.io" "bfdprofiles.metallb.io" "bgppeers.metallb.io" "bgpadvertisements.metallb.io" "ipaddresspools.metallb.io" "l2advertisements.metallb.io")
    for crd in "${required_crds[@]}"; do
        if ! kubectl get crd | grep -q "$crd"; then
            echo "ERROR: Required CRD $crd is missing"
            return 1
        fi
    done

    # Check controller and speaker deployments
    if ! kubectl get deployment -n metallb-system metallb-controller &>/dev/null; then
        echo "ERROR: MetalLB controller deployment not found"
        return 1
    fi
    
    if ! kubectl get daemonset -n metallb-system metallb-speaker &>/dev/null; then
        echo "ERROR: MetalLB speaker daemonset not found"
        return 1
    fi

    echo "MetalLB installation and verification completed successfully"
    return 0
}

# Function to configure MetalLB
configure_metallb() {
    echo "Configuring MetalLB..."
    
    # Read configuration values
    echo "Reading configuration values..."
    INGRESS_IP_START=$(read_yaml_value '.metallb.ingress-ip-start')
    if [ $? -ne 0 ] || [ -z "$INGRESS_IP_START" ]; then
        echo "Failed to read ingress-ip-start from config"
        return 1
    fi
    
    INGRESS_IP_END=$(read_yaml_value '.metallb.ingress-ip-end')
    if [ $? -ne 0 ] || [ -z "$INGRESS_IP_END" ]; then
        echo "Failed to read ingress-ip-end from config"
        return 1
    fi
    
    EGRESS_INTERFACE=$(read_yaml_value '.metallb.egress-interface')
    if [ $? -ne 0 ] || [ -z "$EGRESS_INTERFACE" ]; then
        echo "Failed to read egress-interface from config"
        return 1
    fi
    
    echo "Using configuration:"
    echo "  Ingress IP range: $INGRESS_IP_START - $INGRESS_IP_END"
    echo "  Egress interface: $EGRESS_INTERFACE"
    
    # Validate interface on all nodes
    echo "Checking interface $EGRESS_INTERFACE on all nodes..."
    cat <<'EOF' | kubectl apply -f -
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: interface-check
  namespace: metallb-system
spec:
  selector:
    matchLabels:
      app: interface-check
  template:
    metadata:
      labels:
        app: interface-check
    spec:
      hostNetwork: true
      containers:
      - name: interface-check
        image: busybox
        command:
        - /bin/sh
        - -c
        - |
          if ! ip link show $EGRESS_INTERFACE > /dev/null 2>&1; then
            echo "Interface $EGRESS_INTERFACE not found on node $(hostname)"
            exit 1
          fi
          if ! ip link show $EGRESS_INTERFACE | grep -q "UP"; then
            echo "Interface $EGRESS_INTERFACE is not UP on node $(hostname)"
            exit 1
          fi
          echo "Interface check passed on node $(hostname)"
      terminationGracePeriodSeconds: 0
EOF
    
    # Wait for interface check to complete
    sleep 5
    local failed_pods=$(kubectl get pods -n metallb-system -l app=interface-check --field-selector status.phase=Failed -o name)
    if [ ! -z "$failed_pods" ]; then
        echo "ERROR: Interface check failed on some nodes"
        kubectl logs -n metallb-system -l app=interface-check
        kubectl delete ds interface-check -n metallb-system
        return 1
    fi
    
    kubectl delete ds interface-check -n metallb-system
    
    # Check IP availability
    echo "Checking IP range availability on all nodes..."
    cat <<EOF | kubectl apply -f -
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: ip-check
  namespace: metallb-system
spec:
  selector:
    matchLabels:
      app: ip-check
  template:
    metadata:
      labels:
        app: ip-check
    spec:
      hostNetwork: true
      containers:
      - name: ip-check
        image: busybox
        command:
        - /bin/sh
        - -c
        - |
          for ip in \$(seq -f "$INGRESS_IP_START" 1 $INGRESS_IP_END); do
            if arping -c 1 -w 1 -I $EGRESS_INTERFACE \$ip > /dev/null 2>&1; then
              echo "WARNING: IP \$ip is in use on node \$(hostname)"
              exit 1
            fi
          done
          echo "All IPs are available on node \$(hostname)"
      terminationGracePeriodSeconds: 0
EOF
    
    # Wait for IP check to complete
    sleep 5
    failed_pods=$(kubectl get pods -n metallb-system -l app=ip-check --field-selector status.phase=Failed -o name)
    if [ ! -z "$failed_pods" ]; then
        echo "ERROR: IP conflict detected on some nodes"
        kubectl logs -n metallb-system -l app=ip-check
        kubectl delete ds ip-check -n metallb-system
        return 1
    fi
    
    kubectl delete ds ip-check -n metallb-system
    
    # Apply MetalLB configuration
    echo "Applying MetalLB configuration..."
    cat <<EOF | kubectl apply -f -
apiVersion: metallb.io/v1beta1
kind: IPAddressPool
metadata:
    name: ingress-public-ip
    namespace: metallb-system
spec:
    addresses:
    - ${INGRESS_IP_START}-${INGRESS_IP_END}
---
apiVersion: metallb.io/v1beta1
kind: L2Advertisement
metadata:
    name: ingress-public-ip
    namespace: metallb-system
spec:
    interfaces:
    - ${EGRESS_INTERFACE}
    ipAddressPools:
    - ingress-public-ip
EOF
    
    if [ $? -ne 0 ]; then
        echo "ERROR: Failed to apply MetalLB configuration"
        return 1
    fi
    
    echo "MetalLB configuration completed successfully"
    return 0
}

# Function to install Ingress-Nginx
install_ingress_nginx() {
    echo "Installing Ingress-Nginx..."

    # Cleanup function for Ingress-Nginx resources
    cleanup_ingress_nginx() {
        echo "Cleaning up existing Ingress-Nginx installation..."
        
        # Remove finalizers from resources
        for resource in $(kubectl get -n ingress-nginx $(kubectl api-resources --namespaced=true --output=name | grep networking.k8s.io) -o name 2>/dev/null); do
            kubectl patch -n ingress-nginx $resource -p '{"metadata":{"finalizers":[]}}' --type=merge 2>/dev/null || true
        done
        
        # Delete namespace if exists
        if kubectl get namespace ingress-nginx &>/dev/null; then
            echo "Removing ingress-nginx namespace..."
            
            # Normal non-blocking delete
            kubectl delete namespace ingress-nginx &
            
            # Wait for namespace deletion with timeout
            echo "Waiting for namespace deletion..."
            for i in {1..15}; do
                if ! kubectl get namespace ingress-nginx &>/dev/null; then
                    echo "Namespace deleted successfully"
                    break
                fi
                
                if [ $i -eq 15 ]; then
                    echo "ERROR: Failed to delete ingress-nginx namespace after 5 minutes"
                    return 1
                fi
                
                echo "Waiting for namespace deletion... ($i/15)"
                sleep 20
            done
        fi
        
        # Remove cluster-wide resources
        echo "Removing cluster-wide resources..."
        kubectl delete clusterrole -l app.kubernetes.io/name=ingress-nginx --ignore-not-found
        kubectl delete clusterrolebinding -l app.kubernetes.io/name=ingress-nginx --ignore-not-found
        
        # Remove ValidatingWebhookConfiguration if exists
        kubectl delete ValidatingWebhookConfiguration ingress-nginx-admission --ignore-not-found
        
        # Remove Helm release if exists
        if helm list -n ingress-nginx 2>/dev/null | grep -q "ingress-nginx"; then
            echo "Removing Helm release..."
            helm uninstall ingress-nginx -n ingress-nginx 2>/dev/null || true
        fi
        
        echo "Cleanup completed"
        sleep 5
    }

    # Run cleanup first
    cleanup_ingress_nginx

    # Install Ingress-Nginx
    echo "Installing Ingress-Nginx controller..."
    helm upgrade --install ingress-nginx ingress-nginx \
        --repo https://kubernetes.github.io/ingress-nginx \
        --namespace ingress-nginx \
        --create-namespace \
        --wait \
        --timeout 300s
    check_command "Install Ingress-Nginx"

    # Verify installation
    echo "Verifying Ingress-Nginx installation..."
    
    # Wait for controller deployment
    echo "Waiting for controller deployment..."
    if ! kubectl wait --namespace ingress-nginx \
        --for=condition=ready pod \
        --selector=app.kubernetes.io/component=controller \
        --timeout=300s; then
        echo "ERROR: Timeout waiting for Ingress-Nginx controller pods"
        return 1
    fi

    # Verify admission webhook
    echo "Verifying admission webhook..."
    if ! kubectl get validatingwebhookconfiguration ingress-nginx-admission &>/dev/null; then
        echo "ERROR: Admission webhook configuration not found"
        return 1
    fi

    # Check if service is created and has IP/hostname
    echo "Checking Ingress-Nginx service..."
    for i in {1..30}; do
        if kubectl get service ingress-nginx-controller -n ingress-nginx -o jsonpath='{.status.loadBalancer.ingress[0]}' 2>/dev/null | grep -E 'ip|hostname' >/dev/null; then
            echo "Ingress-Nginx service is ready"
            break
        fi
        if [ $i -eq 30 ]; then
            echo "ERROR: Timeout waiting for Ingress-Nginx service"
            return 1
        fi
        echo "Waiting for service... ($i/30)"
        sleep 5
    done

    # Test the controller with a simple ingress
    echo "Testing Ingress-Nginx controller with a test application..."
    
    # Create test deployment and service
    kubectl create deployment test-nginx --image=nginx -n ingress-nginx
    kubectl expose deployment test-nginx --port=80 -n ingress-nginx
    
    # Create test ingress
    cat <<EOF | kubectl apply -f -
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: test-ingress
  namespace: ingress-nginx
spec:
  ingressClassName: nginx
  rules:
  - http:
      paths:
      - path: /test
        pathType: Prefix
        backend:
          service:
            name: test-nginx
            port:
              number: 80
EOF

    # Wait for ingress to get address
    echo "Waiting for test ingress to get address..."
    for i in {1..12}; do
        if kubectl get ing test-ingress -n ingress-nginx -o jsonpath='{.status.loadBalancer.ingress[0]}' 2>/dev/null | grep -E 'ip|hostname' >/dev/null; then
            echo "Test ingress is ready"
            break
        fi
        if [ $i -eq 12 ]; then
            echo "ERROR: Timeout waiting for test ingress"
            kubectl delete ing test-ingress -n ingress-nginx
            kubectl delete svc test-nginx -n ingress-nginx
            kubectl delete deployment test-nginx -n ingress-nginx
            return 1
        fi
        echo "Waiting for ingress address... ($i/12)"
        sleep 10
    done

    # Cleanup test resources
    kubectl delete ing test-ingress -n ingress-nginx
    kubectl delete svc test-nginx -n ingress-nginx
    kubectl delete deployment test-nginx -n ingress-nginx

    echo "Ingress-Nginx installation and verification completed successfully"
    return 0
}

# Function to configure DNS on jumphost and CoreDNS
configure_dns() {
    echo "Configuring DNS..."

    # Update /etc/hosts on the jumphost
    echo "Updating /etc/hosts on the jumphost..."
    sudo bash -c 'echo "************* sso.projectx.eypoc.com oauth2-proxy.projectx.eypoc.com" >> /etc/hosts'
    check_command "Update jumphost /etc/hosts"
    
    # Update CoreDNS ConfigMap
    echo "Updating CoreDNS ConfigMap..."
    kubectl get cm -n kube-system coredns -o yaml | sed '/hosts custom.hosts/a\          sso.projectx.eypoc.com oauth2-proxy.projectx.eypoc.com' | kubectl apply -f -
    check_command "Update CoreDNS ConfigMap"

    kubectl get cm -n kube-system nodelocaldns -o yaml | sed '/hosts custom.hosts/a\          sso.projectx.eypoc.com oauth2-proxy.projectx.eypoc.com' | kubectl apply -f -
    check_command "Update NodeLocalDNS ConfigMap"

    # Restart CoreDNS related pods
    echo "Restarting CoreDNS and NodeLocalDNS pods..."
    kubectl get pod -A | grep -E 'coredns|localdns' | awk '{print $2}' | xargs kubectl delete pod -n kube-system
    check_command "Restart CoreDNS and NodeLocalDNS pods"
}

# Function to install Keycloak
install_keycloak() {
    echo "Installing Keycloak..."
    
    # Read configuration values
    local KEYCLOAK_HOST=$(read_yaml_value '.keycloak.host')
    local KEYCLOAK_ADMIN_USER=$(read_yaml_value '.keycloak.adminUser')
    
    if [[ -z "$KEYCLOAK_HOST" ]]; then
        echo "ERROR: Keycloak host not configured in inputs-installation.yaml"
        return 1
    fi
    
    if [[ -z "$KEYCLOAK_ADMIN_USER" ]]; then
        echo "ERROR: Keycloak admin user not configured in inputs-installation.yaml"
        return 1
    fi
    
    # Check if namespace exists and clean up if needed
    if kubectl get namespace keycloak >/dev/null 2>&1; then
        echo "Found existing keycloak namespace, cleaning up..."
        kubectl delete ingress -n keycloak --all
        kubectl delete service -n keycloak --all
        helm uninstall keycloak -n keycloak
        kubectl delete namespace keycloak
    fi

    # Create namespace
    kubectl create namespace keycloak

    # Prepare values file
    cat > keycloak-values.yml <<EOF
# Run in production mode behind NGINX proxy
production: true
proxyHeaders: xforwarded

# Admin user
auth:
  adminUser: ${KEYCLOAK_ADMIN_USER}

# Ingress config
ingress:
  enabled: true
  ingressClassName: "nginx"
  pathType: Prefix
  annotations:
    nginx.ingress.kubernetes.io/proxy-buffers-number: "4"
    nginx.ingress.kubernetes.io/proxy-buffer-size: "16k"
  hostname: ${KEYCLOAK_HOST}
  tls: true
  extraTls:
    - hosts:
        - ${KEYCLOAK_HOST}
EOF

    # Add Bitnami repo if not exists
    if ! helm repo list | grep -q "bitnami"; then
        helm repo add bitnami https://charts.bitnami.com/bitnami
        helm repo update
    fi

    # Install Keycloak
    if ! helm install keycloak bitnami/keycloak \
        -f keycloak-values.yml \
        --namespace keycloak \
        --wait --timeout 10m; then
        echo "ERROR: Keycloak installation failed"
        return 1
    fi

    # Verify installation
    verify_keycloak_installation "${KEYCLOAK_HOST}"
}

# Function to verify Keycloak installation
verify_keycloak_installation() {
    local KEYCLOAK_HOST="$1"
    echo "Verifying Keycloak installation..."

    # Verify TLS configuration
    if ! kubectl get ingress -n keycloak keycloak -o jsonpath='{.spec.tls[0].hosts[0]}' | grep -q "${KEYCLOAK_HOST}"; then
        echo "ERROR: TLS configuration verification failed"
        echo "Current ingress configuration:"
        kubectl get ingress -n keycloak keycloak -o yaml
        return 1
    fi

    # Get admin password
    local ADMIN_PASSWORD=$(kubectl get secret keycloak -o jsonpath='{.data.admin-password}' -n keycloak | base64 -d)
    if [[ -z "$ADMIN_PASSWORD" ]]; then
        echo "ERROR: Could not retrieve admin password"
        return 1
    fi

    # Get Keycloak Ingress IP
    local INGRESS_IP=$(kubectl get ingress -n keycloak keycloak -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
    if [[ -z "$INGRESS_IP" ]]; then
        echo "ERROR: Could not get Keycloak Ingress IP"
        return 1
    fi

    # Wait for Keycloak to be accessible
    echo "Checking Keycloak availability..."
    for i in {1..30}; do
        if curl -sk -o /dev/null -w "%{http_code}" "https://${INGRESS_IP}/realms/master/.well-known/openid-configuration" \
            --resolve "${KEYCLOAK_HOST}:443:${INGRESS_IP}" \
            -H "Host: ${KEYCLOAK_HOST}" | grep -q "200"; then
            echo "Keycloak is accessible and responding"
            break
        fi
        if [ $i -eq 30 ]; then
            echo "ERROR: Keycloak is not responding"
            return 1
        fi
        echo "Waiting for Keycloak to be accessible... ($i/30)"
        sleep 10
    done

    echo "Keycloak installation verified successfully"
    echo "Admin password: $ADMIN_PASSWORD"
    echo "Ingress IP: $INGRESS_IP"
    return 0
}

configure_keycloak() {
    echo "Configuring Keycloak..."
    
    # Ensure required dependencies
    if ! ensure_dependencies jq; then
        echo "ERROR: Failed to install required dependencies"
        return 1
    fi
    
    # Get configuration values
    local KEYCLOAK_HOST=$(read_yaml_value '.keycloak.host')
    local KEYCLOAK_ADMIN_USER=$(read_yaml_value '.keycloak.adminUser')
    local KEYCLOAK_REALM=$(read_yaml_value '.keycloak.realm')
    local CLIENT_ID=$(read_yaml_value '.keycloak.client.id')
    local REDIRECT_URI=$(read_yaml_value '.keycloak.client.redirectUris[0]')
    local MAPPER_NAME=$(read_yaml_value '.keycloak.client.audienceMapper.name')
    local MAPPER_AUDIENCE=$(read_yaml_value '.keycloak.client.audienceMapper.audience')
    local TEST_USER=$(read_yaml_value '.keycloak.testUser.username')
    local TEST_PASS=$(read_yaml_value '.keycloak.testUser.password')

    # Validate required values
    [[ -z "$KEYCLOAK_HOST" ]] && echo "ERROR: Keycloak host not configured" && return 1
    [[ -z "$KEYCLOAK_ADMIN_USER" ]] && echo "ERROR: Keycloak admin user not configured" && return 1
    [[ -z "$KEYCLOAK_REALM" ]] && echo "ERROR: Keycloak realm not configured" && return 1
    [[ -z "$CLIENT_ID" ]] && echo "ERROR: Client ID not configured" && return 1
    [[ -z "$REDIRECT_URI" ]] && echo "ERROR: Redirect URI not configured" && return 1
    [[ -z "$MAPPER_NAME" ]] && echo "ERROR: Audience mapper name not configured" && return 1
    [[ -z "$MAPPER_AUDIENCE" ]] && echo "ERROR: Audience mapper audience not configured" && return 1
    [[ -z "$TEST_USER" ]] && echo "ERROR: Test username not configured" && return 1
    [[ -z "$TEST_PASS" ]] && echo "ERROR: Test password not configured" && return 1

    # Get admin password
    local ADMIN_PASSWORD=$(kubectl get secret keycloak -o jsonpath='{.data.admin-password}' -n keycloak | base64 -d)
    if [[ -z "$ADMIN_PASSWORD" ]]; then
        echo "ERROR: Could not retrieve admin password"
        return 1
    fi

    # Get Ingress IP
    local INGRESS_IP=$(kubectl get ingress -n keycloak keycloak -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
    if [[ -z "$INGRESS_IP" ]]; then
        echo "ERROR: Could not get Keycloak Ingress IP"
        return 1
    fi
    
    # Get admin token
    local ADMIN_TOKEN=$(curl -sk \
        -d "client_id=admin-cli" \
        -d "username=${KEYCLOAK_ADMIN_USER}" \
        -d "password=${ADMIN_PASSWORD}" \
        -d "grant_type=password" \
        --resolve "${KEYCLOAK_HOST}:443:${INGRESS_IP}" \
        -H "Host: ${KEYCLOAK_HOST}" \
        "https://${KEYCLOAK_HOST}/realms/master/protocol/openid-connect/token" | jq -r '.access_token')

    if [[ -z "$ADMIN_TOKEN" || "$ADMIN_TOKEN" == "null" ]]; then
        echo "ERROR: Failed to get admin token"
        return 1
    fi

    # Check and delete existing realm
    echo "Checking for existing realm..."
    if curl -sk \
        -H "Authorization: Bearer ${ADMIN_TOKEN}" \
        --resolve "${KEYCLOAK_HOST}:443:${INGRESS_IP}" \
        -H "Host: ${KEYCLOAK_HOST}" \
        "https://${KEYCLOAK_HOST}/admin/realms/${KEYCLOAK_REALM}" | grep -q "realm"; then
        echo "Found existing realm, deleting..."
        if ! curl -sk \
            -X DELETE \
            -H "Authorization: Bearer ${ADMIN_TOKEN}" \
            --resolve "${KEYCLOAK_HOST}:443:${INGRESS_IP}" \
            -H "Host: ${KEYCLOAK_HOST}" \
            "https://${KEYCLOAK_HOST}/admin/realms/${KEYCLOAK_REALM}"; then
            echo "ERROR: Failed to delete existing realm"
            return 1
        fi
        echo "Existing realm deleted"
    fi

    # Create realm
    echo "Creating realm ${KEYCLOAK_REALM}..."
    if ! curl -sk \
        -X POST \
        -H "Authorization: Bearer ${ADMIN_TOKEN}" \
        -H "Content-Type: application/json" \
        --resolve "${KEYCLOAK_HOST}:443:${INGRESS_IP}" \
        -H "Host: ${KEYCLOAK_HOST}" \
        -d "{\"realm\":\"${KEYCLOAK_REALM}\",\"enabled\":true}" \
        "https://${KEYCLOAK_HOST}/admin/realms"; then
        echo "ERROR: Failed to create realm"
        return 1
    fi

    # Create client
    echo "Creating ${CLIENT_ID} client..."
    if ! curl -sk \
        -X POST \
        -H "Authorization: Bearer ${ADMIN_TOKEN}" \
        -H "Content-Type: application/json" \
        --resolve "${KEYCLOAK_HOST}:443:${INGRESS_IP}" \
        -H "Host: ${KEYCLOAK_HOST}" \
        -d "{
            \"clientId\": \"${CLIENT_ID}\",
            \"enabled\": true,
            \"protocol\": \"openid-connect\",
            \"publicClient\": false,
            \"standardFlowEnabled\": true,
            \"redirectUris\": [\"${REDIRECT_URI}\"]
        }" \
        "https://${KEYCLOAK_HOST}/admin/realms/${KEYCLOAK_REALM}/clients"; then
        echo "ERROR: Failed to create client"
        return 1
    fi

    # Get client UUID
    local CLIENT_UUID=$(curl -sk \
        -H "Authorization: Bearer ${ADMIN_TOKEN}" \
        --resolve "${KEYCLOAK_HOST}:443:${INGRESS_IP}" \
        -H "Host: ${KEYCLOAK_HOST}" \
        "https://${KEYCLOAK_HOST}/admin/realms/${KEYCLOAK_REALM}/clients" | \
        jq -r ".[] | select(.clientId==\"${CLIENT_ID}\") | .id")

    if [[ -z "$CLIENT_UUID" ]]; then
        echo "ERROR: Failed to get client UUID"
        return 1
    fi

    # Configure audience mapper
    echo "Configuring audience mapper..."
    if ! curl -sk \
        -X POST \
        -H "Authorization: Bearer ${ADMIN_TOKEN}" \
        -H "Content-Type: application/json" \
        --resolve "${KEYCLOAK_HOST}:443:${INGRESS_IP}" \
        -H "Host: ${KEYCLOAK_HOST}" \
        -d "{
            \"name\": \"${MAPPER_NAME}\",
            \"protocol\": \"openid-connect\",
            \"protocolMapper\": \"oidc-audience-mapper\",
            \"config\": {
                \"included.client.audience\": \"${MAPPER_AUDIENCE}\",
                \"id.token.claim\": \"true\",
                \"access.token.claim\": \"true\"
            }
        }" \
        "https://${KEYCLOAK_HOST}/admin/realms/${KEYCLOAK_REALM}/clients/${CLIENT_UUID}/protocol-mappers/models"; then
        echo "ERROR: Failed to create audience mapper"
        return 1
    fi

    # Create test user
    echo "Creating test user..."
    if ! curl -sk \
        -X POST \
        -H "Authorization: Bearer ${ADMIN_TOKEN}" \
        -H "Content-Type: application/json" \
        --resolve "${KEYCLOAK_HOST}:443:${INGRESS_IP}" \
        -H "Host: ${KEYCLOAK_HOST}" \
        -d "{
            \"username\": \"${TEST_USER}\",
            \"enabled\": true,
            \"credentials\": [{
                \"type\": \"password\",
                \"value\": \"${TEST_PASS}\",
                \"temporary\": false
            }]
        }" \
        "https://${KEYCLOAK_HOST}/admin/realms/${KEYCLOAK_REALM}/users"; then
        echo "ERROR: Failed to create test user"
        return 1
    fi

    echo "Keycloak configuration completed successfully"
    echo "Test user created: ${TEST_USER}/${TEST_PASS}"
    return 0
}

# Function to install OAuth2 Proxy
install_oauth2_proxy() {
    echo "Installing OAuth2 Proxy..."
    
    # Get configuration values
    local OAUTH2_PROXY_HOST=$(read_yaml_value '.oauth2-proxy.hostname')
    local KEYCLOAK_HOST=$(read_yaml_value '.oauth2-proxy.keycloak.host')
    local KEYCLOAK_REALM=$(read_yaml_value '.oauth2-proxy.keycloak.realm')
    local CLIENT_ID=$(read_yaml_value '.oauth2-proxy.keycloak.clientId')
    local INGRESS_CLASS=$(read_yaml_value '.oauth2-proxy.ingress.className')

    # Validate required values
    [[ -z "$OAUTH2_PROXY_HOST" ]] && echo "ERROR: OAuth2 Proxy hostname not configured" && return 1
    [[ -z "$KEYCLOAK_HOST" ]] && echo "ERROR: Keycloak host not configured" && return 1
    [[ -z "$KEYCLOAK_REALM" ]] && echo "ERROR: Keycloak realm not configured" && return 1
    [[ -z "$CLIENT_ID" ]] && echo "ERROR: Client ID not configured" && return 1
    [[ -z "$INGRESS_CLASS" ]] && echo "ERROR: Ingress class not configured" && return 1

    # Get Ingress IP
    local INGRESS_IP=$(kubectl get ingress -n keycloak keycloak -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
    if [[ -z "$INGRESS_IP" ]]; then
        echo "ERROR: Could not get Keycloak Ingress IP"
        return 1
    fi

    # Get admin token
    local ADMIN_TOKEN=$(get_keycloak_admin_token "${KEYCLOAK_HOST}" "${INGRESS_IP}")
    [[ $? -ne 0 ]] && return 1

    # Get client UUID
    local CLIENT_UUID=$(get_keycloak_client_uuid "${ADMIN_TOKEN}" "${KEYCLOAK_HOST}" "${INGRESS_IP}" "${KEYCLOAK_REALM}" "${CLIENT_ID}")
    [[ $? -ne 0 ]] && return 1

    # Get client secret
    local CLIENT_SECRET=$(curl -sk \
        -H "Authorization: Bearer ${ADMIN_TOKEN}" \
        --resolve "${KEYCLOAK_HOST}:443:${INGRESS_IP}" \
        -H "Host: ${KEYCLOAK_HOST}" \
        "https://${KEYCLOAK_HOST}/admin/realms/${KEYCLOAK_REALM}/clients/${CLIENT_UUID}/client-secret" | \
        jq -r '.value')

    [[ -z "$CLIENT_SECRET" || "$CLIENT_SECRET" == "null" ]] && echo "ERROR: Failed to get client secret" && return 1

    # Generate cookie secret
    # TODO: For better user experience, consider storing this in a Kubernetes secret
    # and reusing it across reinstalls to avoid forcing users to re-login
    local COOKIE_SECRET=$(openssl rand -base64 32 | head -c 32 | base64)

    # Check if namespace exists and clean up if needed
    if kubectl get namespace oauth2-proxy >/dev/null 2>&1; then
        echo "Found existing oauth2-proxy namespace, cleaning up..."
        helm uninstall oauth2-proxy -n oauth2-proxy
        kubectl delete namespace oauth2-proxy
    fi

    # Create OAuth2 Proxy values file
    cat > oauth2-proxy-values.yml <<EOF
config:
  clientID: "${CLIENT_ID}"
  clientSecret: "${CLIENT_SECRET}"
  cookieSecret: "${COOKIE_SECRET}"
  configFile: |-
    # Provider config
    provider="keycloak-oidc"
    provider_display_name="Keycloak"
    redirect_url="https://${OAUTH2_PROXY_HOST}/oauth2/callback"
    oidc_issuer_url="https://${KEYCLOAK_HOST}/realms/${KEYCLOAK_REALM}"
    code_challenge_method="S256"
    ssl_insecure_skip_verify=true
    # Upstream config
    http_address="0.0.0.0:4180"
    upstreams="file:///dev/null"
    email_domains=["*"]
    cookie_domains=["projectx.eypoc.com"]
    cookie_secure=false
    scope="openid"
    whitelist_domains=[".projectx.eypoc.com"]
    insecure_oidc_allow_unverified_email="true"
    # Header config
    pass_user_headers=true
    set_xauthrequest=true
    set_authorization_header=true
    pass_access_token=true
    pass_authorization_header=true
    reverse_proxy=true

# Add hostAliases configuration to enable OAuth2 Proxy to resolve Keycloak hostname
# This is a temporary solution until proper DNS is configured
# Similar to adding an entry in /etc/hosts file:
# <INGRESS_IP> <KEYCLOAK_HOST>
hostAliases:
  - ip: "${INGRESS_IP}"        # Keycloak ingress IP address
    hostnames:                  # List of hostnames to be resolved to this IP
      - "${KEYCLOAK_HOST}"     # Keycloak hostname (e.g., sso.projectx.eypoc.com)

sessionStorage:
  type: redis
redis:
  enabled: true
  architecture: standalone

ingress:
  enabled: true
  className: "${INGRESS_CLASS}"
  pathType: Prefix
  path: /oauth2
  annotations:
    nginx.ingress.kubernetes.io/proxy-buffer-size: "16k"
  hosts:
    - ${OAUTH2_PROXY_HOST}
  tls:
    - hosts:
        - ${OAUTH2_PROXY_HOST}
EOF

    # Install OAuth2 Proxy
    echo "Installing OAuth2 Proxy..."
    helm repo add oauth2-proxy https://oauth2-proxy.github.io/manifests
    check_command "Add OAuth2 Proxy repository"
    
    helm repo update
    check_command "Update Helm repositories"
    
    kubectl create namespace oauth2-proxy
    check_command "Create OAuth2 Proxy namespace"
    
    helm install oauth2-proxy oauth2-proxy/oauth2-proxy \
        -f oauth2-proxy-values.yml \
        --namespace oauth2-proxy \
        --wait --timeout 5m
    check_command "Install OAuth2 Proxy"

    # Verify installation
    echo "Verifying OAuth2 Proxy installation..."
    
    # Check pod status
    echo "Checking pod status..."
    if ! kubectl wait --namespace oauth2-proxy \
        --for=condition=ready pod \
        --selector=app.kubernetes.io/name=oauth2-proxy \
        --timeout=300s; then
        echo "ERROR: OAuth2 Proxy pods not ready"
        return 1
    fi

    # Check Redis status if enabled
    echo "Checking Redis status..."
    if ! kubectl wait --namespace oauth2-proxy \
        --for=condition=ready pod \
        --selector=app.kubernetes.io/name=redis \
        --timeout=300s; then
        echo "ERROR: Redis pods not ready"
        return 1
    fi

    # Check ingress configuration
    echo "Checking ingress configuration..."
    if ! kubectl get ingress -n oauth2-proxy oauth2-proxy >/dev/null 2>&1; then
        echo "ERROR: Ingress not created"
        return 1
    fi

    # Verify OAuth2 Proxy sign-in endpoint
    echo "Verifying OAuth2 Proxy sign-in endpoint..."
    local OAUTH2_PROXY_IP=$(kubectl get ingress -n oauth2-proxy oauth2-proxy -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
    local MAX_ATTEMPTS=10  # 最大尝试次数
    local SLEEP_INTERVAL=30  # 每次尝试间隔（秒）

    ATTEMPT=0

    while (( ATTEMPT < MAX_ATTEMPTS )); do
        ATTEMPT=$((ATTEMPT + 1))
        echo "Attempt $ATTEMPT/$MAX_ATTEMPTS..."
        if curl -sk \
            --resolve "${OAUTH2_PROXY_HOST}:443:${OAUTH2_PROXY_IP}" \
            "https://${OAUTH2_PROXY_HOST}/oauth2/sign_in" | \
            grep -q "Sign in with Keycloak"; then
            echo "OAuth2 Proxy sign-in page is accessible."
            break
        else
            echo "OAuth2 Proxy sign-in page not accessible, retrying in $SLEEP_INTERVAL seconds..."
            sleep $SLEEP_INTERVAL
        fi
    done

    if (( ATTEMPT == MAX_ATTEMPTS )); then
        echo "ERROR: OAuth2 Proxy sign-in page not accessible after $MAX_ATTEMPTS attempts."
        return 1
    fi

    echo "OAuth2 Proxy installation and verification completed successfully"
    return 0
}

# Function to install PostgreSQL
install_postgresql() {
    echo "Installing PostgreSQL..."
    
    # Read password from configuration file
    local postgres_password=$(read_yaml_value '.postgresql.password')
    
    # Cleanup existing installation
    helm uninstall postgresql -n postgresql 2>/dev/null || true
    kubectl delete namespace postgresql --wait=false 2>/dev/null || true
    kubectl wait --for=delete namespace/postgresql --timeout=60s 2>/dev/null || true
    
    # Install PostgreSQL
    helm install postgresql oci://registry-1.docker.io/bitnamicharts/postgresql \
        --namespace postgresql \
        --create-namespace \
        --set global.postgresql.auth.postgresPassword="$postgres_password"
    check_command "Install PostgreSQL"
    
    # Verify installation
    echo "Verifying PostgreSQL installation..."
    if ! kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=postgresql -n postgresql --timeout=300s; then
        echo "ERROR: PostgreSQL pods not ready"
        return 1
    fi
    
    # Test connection
    local POD_NAME=$(kubectl get pods -n postgresql -l app.kubernetes.io/name=postgresql -o jsonpath='{.items[0].metadata.name}')
    if ! kubectl exec -n postgresql $POD_NAME -- pg_isready -U postgres; then
        echo "ERROR: Cannot connect to PostgreSQL"
        return 1
    fi
}

# Function to install Qdrant
install_qdrant() {
    echo "Installing Qdrant..."
    
    # Cleanup existing installation
    helm uninstall qdrant -n qdrant 2>/dev/null || true
    kubectl delete namespace qdrant --wait=false 2>/dev/null || true
    kubectl wait --for=delete namespace/qdrant --timeout=60s 2>/dev/null || true
    
    # Add Qdrant repository
    helm repo add qdrant https://qdrant.to/helm
    check_command "Add Qdrant repository"
    
    # Install Qdrant
    helm install qdrant qdrant/qdrant \
        --namespace qdrant \
        --create-namespace
    check_command "Install Qdrant"
    
    # Verify installation
    echo "Verifying Qdrant installation..."
    if ! kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=qdrant -n qdrant --timeout=300s; then
        echo "ERROR: Qdrant pods not ready"
        return 1
    fi
    
    # Test connection using port-forward
    echo "Testing Qdrant connection..."
    local POD_NAME=$(kubectl get pods -n qdrant -l app.kubernetes.io/name=qdrant -o jsonpath='{.items[0].metadata.name}')
    kubectl port-forward -n qdrant $POD_NAME 6333:6333 &
    local PF_PID=$!
    sleep 5
    
    if ! curl -s http://localhost:6333/health > /dev/null; then
        echo "ERROR: Cannot connect to Qdrant"
        kill $PF_PID
        return 1
    fi
    kill $PF_PID
}

# Function to install RabbitMQ
install_rabbitmq() {
    echo "Installing RabbitMQ..."
    
    # Read password from configuration file
    local rabbitmq_password=$(read_yaml_value '.rabbitmq.password')
    
    # Cleanup existing installation
    helm uninstall rabbitmq -n rabbitmq 2>/dev/null || true
    kubectl delete namespace rabbitmq --wait=false 2>/dev/null || true
    kubectl wait --for=delete namespace/rabbitmq --timeout=60s 2>/dev/null || true
    
    # Install RabbitMQ
    helm install rabbitmq oci://registry-1.docker.io/bitnamicharts/rabbitmq \
        --namespace rabbitmq \
        --create-namespace \
        --set auth.password="$rabbitmq_password" \
        --set resources.limits.memory=512Mi \
        --set resources.requests.memory=256Mi
    check_command "Install RabbitMQ"
    
    # Verify installation
    echo "Verifying RabbitMQ installation..."
    if ! kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=rabbitmq -n rabbitmq --timeout=300s; then
        echo "ERROR: RabbitMQ pods not ready"
        return 1
    fi
    
    # Test connection
    local POD_NAME=$(kubectl get pods -n rabbitmq -l app.kubernetes.io/name=rabbitmq -o jsonpath='{.items[0].metadata.name}')
    if ! kubectl exec -n rabbitmq $POD_NAME -- rabbitmq-diagnostics check_running; then
        echo "ERROR: Cannot connect to RabbitMQ"
        return 1
    fi
}

# Function to create Hugging Face Secret
create_huggingface_secret() {
    echo "Creating Hugging Face Secret..."
    
    # Read Hugging Face API token from configuration file
    local HF_API_TOKEN=$(read_yaml_value '.hfApiToken')
    
    # Check if the token is empty
    if [ -z "$HF_API_TOKEN" ]; then
        echo "ERROR: Hugging Face API token is not set in the configuration file."
        return 1
    fi
    
    # Delete existing secret if it exists
    kubectl delete secret hf-secret --namespace=default --ignore-not-found
    
    # Create the secret directly
    kubectl create secret generic hf-secret \
        --from-literal=hfApiToken="$HF_API_TOKEN" \
        --namespace=default
    
    check_command "Create Hugging Face Secret"
}

# Function to deploy Llama-3.1-8B
deploy_llama_3_1_8b() {
    echo "Deploying Llama-3.1-8B..."

    # Cleanup existing deployment
    kubectl delete deployment llama3-1 --namespace=default --ignore-not-found
    kubectl delete service llama3-1 --namespace=default --ignore-not-found
    kubectl delete pvc llama-3-1 --namespace=default --ignore-not-found

    # Create PersistentVolumeClaim
    kubectl apply -f - <<EOF
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: llama-3-1
  namespace: default
spec:
  accessModes:
  - ReadWriteMany
  resources:
    requests:
      storage: 50Gi
  volumeMode: Filesystem
EOF

    # Create Deployment
    kubectl apply -f - <<EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: llama3-1
  name: llama3-1
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: llama3-1
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: llama3-1
    spec:
      containers:
      - args:
        - vllm serve meta-llama/Llama-3.1-8B-Instruct --trust-remote-code --enable-chunked-prefill --max_num_batched_tokens 1024
        command:
        - /bin/sh
        - -c
        env:
        - name: HUGGING_FACE_HUB_TOKEN
          valueFrom:
            secretKeyRef:
              key: hfApiToken
              name: hf-secret
        image: vllm/vllm-openai:latest
        imagePullPolicy: Always
        name: llama
        ports:
        - containerPort: 8000
          protocol: TCP
        resources:
          limits:
            cpu: "16"
            nvidia.com/gpu: "1"
          requests:
            cpu: "6"
            memory: "6Gi"
            nvidia.com/gpu: "1"
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /root/.cache/huggingface
          name: cache-volume
        - mountPath: /dev/shm
          name: shm
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
      volumes:
      - name: cache-volume
        persistentVolumeClaim:
          claimName: llama-3-1
      - emptyDir:
          medium: Memory
        name: shm
EOF

    # Create Service
    kubectl apply -f - <<EOF
apiVersion: v1
kind: Service
metadata:
  labels:
    app: llama3-1
  name: llama3-1
  namespace: default
spec:
  ports:
  - name: vllm
    nodePort: 30122
    port: 8000
    protocol: TCP
    targetPort: 8000
  selector:
    app: llama3-1
  type: NodePort
EOF

    # Verify deployment
    echo "Verifying Llama-3.1-8B deployment..."
    if ! kubectl wait --for=condition=available --timeout=300s deployment/llama3-1 -n default; then
        echo "ERROR: Llama-3.1-8B deployment not ready"
        return 1
    fi

    # Check if the model is serving successfully by checking logs
    echo "Waiting for the model to start serving..."
    for i in {1..30}; do
        if kubectl logs deployment/llama3-1 -n default | grep -q "Application startup complete."; then
            echo "Llama-3.1-8B model is serving successfully."
            break
        fi
        if [ $i -eq 30 ]; then
            echo "ERROR: Model did not start serving in time."
            return 1
        fi
        echo "Waiting for model to start... ($i/30)"
        sleep 10
    done

    # Port forward to access the model service
    echo "Setting up port forwarding to access the model service..."
    kubectl port-forward deployment/llama3-1 -n default 0:8000 > port_forward.log 2>&1 &
    PORT_FORWARD_PID=$!

    # Wait for a moment to ensure port forwarding is established
    sleep 5

    # Get the randomly assigned port from the output log
    RANDOM_PORT=$(grep -oP 'Forwarding from 127.0.0.1:\K[0-9]+' port_forward.log | tail -n 1)

    # Test API call to the model
    echo "Testing API call to the model..."
    TEST_DATA='{"model": "meta-llama/Llama-3.1-8B-Instruct", "messages": [{"role": "user", "content": "Hello, how are you?"}], "max_tokens": 5}'  # Example input for the model
    if ! curl -s -X POST -H "Content-Type: application/json" -d "$TEST_DATA" "http://localhost:$RANDOM_PORT/v1/chat/completions" | grep -q "choices"; then
        echo "ERROR: API call to the model failed"
        kill $PORT_FORWARD_PID
        return 1
    fi

    echo "API call to the meta-llama/Llama-3.1-8B-Instruct model succeeded."

    # Cleanup port forwarding
    kill $PORT_FORWARD_PID
}

# Function to deploy bge-m3
deploy_bge_m3() {
    echo "Deploying bge-m3..."

    # Cleanup existing deployment
    kubectl delete deployment bge-m3 --namespace=default --ignore-not-found
    kubectl delete service bge-m3 --namespace=default --ignore-not-found
    kubectl delete pvc bge-m3 --namespace=default --ignore-not-found

    # Create PersistentVolumeClaim
    kubectl apply -f - <<EOF
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: bge-m3
  namespace: default
spec:
  accessModes:
  - ReadWriteMany
  resources:
    requests:
      storage: 5Gi
  volumeMode: Filesystem
EOF

    # Create Deployment
    kubectl apply -f - <<EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: bge-m3
  namespace: default
  labels:
    app: bge-m3
spec:
  replicas: 1
  selector:
    matchLabels:
      app: bge-m3
  template:
    metadata:
      labels:
        app: bge-m3
    spec:
      volumes:
      - name: cache-volume
        persistentVolumeClaim:
          claimName: bge-m3
      - name: shm
        emptyDir:
          medium: Memory
      containers:
      - name: llama
        image: vllm/vllm-openai:latest
        command: ["/bin/sh", "-c"]
        args: ["vllm serve BAAI/bge-m3 --trust-remote-code --task embedding"]
        env:
        - name: HUGGING_FACE_HUB_TOKEN
          valueFrom:
            secretKeyRef:
              name: hf-secret
              key: hfApiToken
        ports:
        - containerPort: 8000
        resources:
          limits:
            cpu: "16"
            nvidia.com/gpu: "1"
          requests:
            cpu: "6"
            memory: "6Gi"
            nvidia.com/gpu: "1"
        volumeMounts:
        - mountPath: /root/.cache/huggingface
          name: cache-volume
        - mountPath: /dev/shm
          name: shm
EOF

    # Create Service for bge-m3
    kubectl apply -f - <<EOF
apiVersion: v1
kind: Service
metadata:
  name: bge-m3
  namespace: default
spec:
  ports:
  - nodePort: 30125
    port: 8000
    protocol: TCP
    targetPort: 8000
  selector:
    app: bge-m3
  type: NodePort
EOF

    # Verify deployment
    echo "Verifying bge-m3 deployment..."
    if ! kubectl wait --for=condition=available --timeout=300s deployment/bge-m3 -n default; then
        echo "ERROR: bge-m3 deployment not ready"
        return 1
    fi

    # Check if the model is serving successfully by checking logs
    echo "Waiting for the bge-m3 model to start serving..."
    for i in {1..30}; do
        if kubectl logs deployment/bge-m3 -n default | grep -q "Application startup complete."; then
            echo "bge-m3 model is serving successfully."
            break
        fi
        if [ $i -eq 30 ]; then
            echo "ERROR: bge-m3 model did not start serving in time."
            return 1
        fi
        echo "Waiting for bge-m3 model to start... ($i/30)"
        sleep 10
    done

    # Port forward to access the model service
    echo "Setting up port forwarding to access the bge-m3 model service..."
    kubectl port-forward deployment/bge-m3 -n default 0:8000 > port_forward_bge.log 2>&1 &
    PORT_FORWARD_PID=$!

    # Wait for a moment to ensure port forwarding is established
    sleep 5

    # Get the randomly assigned port from the output log
    RANDOM_PORT=$(grep -oP 'Forwarding from 127.0.0.1:\K[0-9]+' port_forward_bge.log | tail -n 1)

    # Test API call to the bge-m3 model
    echo "Testing API call to the bge-m3 model..."
    TEST_DATA='{"model": "BAAI/bge-m3", "input": "Hello, how are you?"}'  # Example input for the embedding model
    if ! curl -s -X POST -H "Content-Type: application/json" -d "$TEST_DATA" "http://localhost:$RANDOM_PORT/v1/embeddings" | grep -q "data"; then
        echo "ERROR: API call to the bge-m3 model failed"
        kill $PORT_FORWARD_PID
        return 1
    fi

    echo "API call to the BAAI/bge-m3 model succeeded."

    # Cleanup port forwarding
    kill $PORT_FORWARD_PID
}

# Function to install Prometheus
install_prometheus() {
    echo "Installing Prometheus..."
    kubectl create namespace monitoring
    check_command "Create monitoring namespace"
    helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
    check_command "Add Prometheus repository"
    helm repo update
    check_command "Update Helm repositories"
    helm install prometheus prometheus-community/kube-prometheus-stack --namespace=monitoring
    check_command "Install Prometheus"
}

# Function to install Grafana
install_grafana() {
    echo "Installing Grafana..."
    cat <<EOF > grafana-values.yaml
adminUser: admin
adminPassword: admin
service:
  type: LoadBalancer
EOF
    helm repo add grafana https://grafana.github.io/helm-charts
    check_command "Add Grafana repository"
    helm repo update
    check_command "Update Helm repositories"
    helm install grafana grafana/grafana -f grafana-values.yaml --namespace=monitoring
    check_command "Install Grafana"
}

# Function to cleanup on error
cleanup() {
    echo "Error occurred during installation. Cleaning up..."
    # Add cleanup steps here if needed
    exit 1
}

# Set trap for cleanup
trap cleanup ERR

# Function to show usage
show_usage() {
    echo "Usage: $0 [-a|--auto] [-h|--help]"
    echo "Options:"
    echo "  -a, --auto          Enable automatic mode for component installation"
    echo "  -h, --help          Show this help message"
}

# Function to prompt user for component installation
prompt_component() {
    local component=$1
    local description=$2
    local default=${3:-n}
    
    local prompt_char="y/N"
    if [ "$default" = "y" ]; then
        prompt_char="Y/n"
    fi
    
    read -p "Install $description? [$prompt_char] " choice
    choice=${choice:-$default}
    
    case "$choice" in
        [Yy]*)
            return 0
            ;;
        *)
            return 1
            ;;
    esac
}

# Function to ensure yq is installed and read yaml config
read_yaml_value() {
    local key=$1
    local input_file=${2:-"inputs-installation.yaml"}
    
    # Install yq if needed
    if ! command -v yq &> /dev/null; then
        echo "Installing yq (required for configuration)..." >&2
        if ! sudo wget -q "https://github.com/mikefarah/yq/releases/download/v4.40.5/yq_linux_amd64" -O /usr/local/bin/yq; then
            echo "Failed to download yq" >&2
            return 1
        fi
        if ! sudo chmod +x /usr/local/bin/yq; then
            echo "Failed to make yq executable" >&2
            return 1
        fi
        echo "yq installed successfully" >&2
    fi
    
    # Read and return the value, redirecting stderr to avoid version info output
    yq e "$key" "$input_file" 2>/dev/null
}

# Function to ensure required tools are installed
ensure_dependencies() {
    local deps=("$@")
    local missing=()
    
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            missing+=("$dep")
        fi
    done

    if [[ ${#missing[@]} -eq 0 ]]; then
        return 0
    fi

    echo "Installing missing dependencies: ${missing[*]}"
    if [[ -f /etc/debian_version ]]; then
        sudo apt-get update && sudo apt-get install -y "${missing[@]}"
        check_command "Install dependencies: ${missing[*]}"
    elif [[ -f /etc/redhat-release ]]; then
        sudo yum install -y "${missing[@]}"
        check_command "Install dependencies: ${missing[*]}"
    else
        echo "ERROR: Unsupported OS for package installation"
        return 1
    fi
}

# Function to get Keycloak admin token
get_keycloak_admin_token() {
    local KEYCLOAK_HOST="$1"
    local INGRESS_IP="$2"

    # Get admin credentials
    local ADMIN_USER=$(read_yaml_value '.keycloak.adminUser')
    if [[ -z "$ADMIN_USER" ]]; then
        echo "ERROR: Could not get Keycloak admin user from config" >&2
        return 1
    fi

    local ADMIN_PASSWORD=$(kubectl get secret keycloak -o jsonpath='{.data.admin-password}' -n keycloak | base64 -d)
    if [[ -z "$ADMIN_PASSWORD" ]]; then
        echo "ERROR: Could not retrieve Keycloak admin password" >&2
        return 1
    fi

    local TOKEN=$(curl -sk \
        -d "client_id=admin-cli" \
        -d "username=${ADMIN_USER}" \
        -d "password=${ADMIN_PASSWORD}" \
        -d "grant_type=password" \
        --resolve "${KEYCLOAK_HOST}:443:${INGRESS_IP}" \
        -H "Host: ${KEYCLOAK_HOST}" \
        "https://${KEYCLOAK_HOST}/realms/master/protocol/openid-connect/token" | jq -r '.access_token')

    if [[ -z "$TOKEN" || "$TOKEN" == "null" ]]; then
        echo "ERROR: Failed to get admin token" >&2
        return 1
    fi
    echo "$TOKEN"
}

# Function to get Keycloak client UUID
get_keycloak_client_uuid() {
    local ADMIN_TOKEN="$1"
    local KEYCLOAK_HOST="$2"
    local INGRESS_IP="$3"
    local REALM="$4"
    local CLIENT_ID="$5"

    local UUID=$(curl -sk \
        -H "Authorization: Bearer ${ADMIN_TOKEN}" \
        --resolve "${KEYCLOAK_HOST}:443:${INGRESS_IP}" \
        -H "Host: ${KEYCLOAK_HOST}" \
        "https://${KEYCLOAK_HOST}/admin/realms/${REALM}/clients" | \
        jq -r ".[] | select(.clientId==\"${CLIENT_ID}\") | .id")

    if [[ -z "$UUID" || "$UUID" == "null" ]]; then
        echo "ERROR: Failed to get client UUID" >&2
        return 1
    fi
    echo "$UUID"
}

# Entry point to the script
main() {
    # Parse command line arguments
    INTERACTIVE_MODE=true  # Default to interactive mode
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -a|--auto)  # Short and long option for automatic mode
                INTERACTIVE_MODE=false
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                echo "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done

    echo "This script will install the complete stack. Please make sure you have:"
    echo "1. A working Kubernetes cluster"
    echo "2. Sufficient resources for all components"
    echo "3. Required network access"
    echo

    if [ "$INTERACTIVE_MODE" = true ]; then
        read -p "Continue with installation? (y/N) " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo "Installation cancelled."
            exit 1
        fi
    else
        echo "INTERACTIVE_MODE is false, proceeding with installation without confirmation."
    fi

    echo "Starting installation..."
    
    # These components are required and will always be installed
    echo "Installing required base components..."
    check_k8s_cluster
    install_helm
    
    # Define components in correct order
    components=(
        "install_nvidia_driver:NVIDIA Driver"
        "clear_disks:Disk Cleanup"
        "install_ceph:Ceph Storage System"
        "create_ceph_bucket:Ceph Object Store Bucket"
        "install_metallb:MetalLB Load Balancer"
        "configure_metallb:MetalLB Configuration"
        "install_ingress_nginx:Ingress-Nginx"
        "install_keycloak:Keycloak Authentication"
        "configure_keycloak:Keycloak Configuration"
        "install_oauth2_proxy:OAuth2 Proxy"
        # "configure_dns:DNS Configuration"
        "install_postgresql:PostgreSQL Database"
        "install_qdrant:Qdrant Vector Database"
        "install_rabbitmq:RabbitMQ Message Queue"
        "create_huggingface_secret:Hugging Face Secret"
        "deploy_llama_3_1_8b:Llama-3.1-8B Model"
        "deploy_bge_m3:BGE-M3 Embedding Model"
        # "install_prometheus:Prometheus Monitoring"
        # "install_grafana:Grafana Visualization"
    )
    
    if [ "$INTERACTIVE_MODE" = true ]; then
        echo -e "\nSelect components to install:"
        for component in "${components[@]}"; do
            IFS=':' read -r func desc <<< "$component"
            if prompt_component "$func" "$desc"; then
                echo "Executing $desc installation..."
                $func
            else
                echo "Skipping $desc installation"
            fi
        done
    else
        echo "Running in automatic mode - installing all components"
        for component in "${components[@]}"; do
            IFS=':' read -r func desc <<< "$component"
            echo "Installing $desc..."
            $func
        done
    fi
    
    echo "Installation complete. Follow the GETTING-STARTED.md to deploy an RAG application."
}

main "$@"