# Command line to setup and use message queue
Download command line binary from rabbitmq pod
```
wget http://rabbitmq-headless.rabbitmq.svc.cluster.local:15672/cli/rabbitmqadmin
chmod +x rabbitmqadmin
```

Declare exchange/queue/binding
```
./rabbitmqadmin -H rabbitmq.rabbitmq.svc.cluster.local -P 15672 -u user -p mystic declare exchange name=x_exchange type=direct
./rabbitmqadmin -H rabbitmq.rabbitmq.svc.cluster.local -P 15672 -u user -p mystic declare queue name=x_queue durable=true
./rabbitmqadmin -H rabbitmq.rabbitmq.svc.cluster.local -P 15672 -u user -p mystic declare binding source=x_exchange destination=x_queue routing_key=x_key
```

Public message
```
./rabbitmqadmin -H rabbitmq.rabbitmq.svc.cluster.local -P 15672 -u user -p mystic publish exchange=x_exchange routing_key=x_key payload="Hello, RabbitMQ!"
```

Get message
```
./rabbitmqadmin -H rabbitmq.rabbitmq.svc.cluster.local -P 15672 -u user -p mystic get queue=x_queue
```

Clear queue
```
./rabbitmqadmin -H rabbitmq.rabbitmq.svc.cluster.local -P 15672 -u user -p mystic purge queue name=x_queue
```

**Note**
rabbitmqadmin command line does not support acknowledge and delete single message. Use python lib for more operations.    


# Python code to setup and use message queue
See mqclient.py