import pika


class RabbitMQClient:
    def __init__(self, host='localhost', exchange='my_exchange', queue='my_queue', routing_key='my_routing_key', usrname='user', password='mystic'):
        self.host = host
        self.exchange = exchange
        self.queue = queue
        self.routing_key = routing_key
        self.username = usrname
        self.password = password
        self.connection = None
        self.channel = None

    def connect(self):
        """Connect to RabbitMQ and create a channel."""
        creds = pika.PlainCredentials(self.username, self.password)
        self.connection = pika.BlockingConnection(pika.ConnectionParameters(host=self.host, credentials=creds))
        self.channel = self.connection.channel()
        print("Connected to RabbitMQ.")

    def setup(self):
        """Setup exchange, queue, and bind them."""
        self.connect()
        # Declare exchange of type 'direct'
        self.channel.exchange_declare(exchange=self.exchange, exchange_type='direct')
        print(f"Exchange '{self.exchange}' declared.")

        # Declare queue
        self.channel.queue_declare(queue=self.queue, durable=True)
        print(f"Queue '{self.queue}' declared.")

        # Bind queue to exchange with routing key
        self.channel.queue_bind(exchange=self.exchange, queue=self.queue, routing_key=self.routing_key)
        print(f"Queue '{self.queue}' bound to exchange '{self.exchange}' with routing key '{self.routing_key}'.")
        self.connection.close()
        print("Setup completed.\n")

    def publish(self, message):
        """Publish a message to the exchange."""
        self.connect()
        self.channel.basic_publish(
            exchange=self.exchange,
            routing_key=self.routing_key,
            body=message,
            properties=pika.BasicProperties(delivery_mode=2)  # Make the message persistent
        )
        print(f"Message published: {message}")
        self.connection.close()

    def get_message(self):
        """Get a single message from the queue."""
        message = None
        self.connect()
        method_frame, _, body = self.channel.basic_get(queue=self.queue, auto_ack=False)
        if method_frame:
            message = body.decode()
            print(f"Message received: {message}")
            # Acknowledge the message to remove it from the queue
            self.channel.basic_ack(delivery_tag=method_frame.delivery_tag)
            print("Message acknowledged.")
        else:
            print("No messages available in the queue.")
        self.connection.close()
        return message

    def consume_messages(self, callback_function):
        """Continuously consume messages from the queue."""
        self.connect()

        def wrapper_callback(ch, method, properties, body):
            print(f"Received message: {body.decode()}")
            callback_function(body)
            ch.basic_ack(delivery_tag=method.delivery_tag)

        print("Starting consumer. Press CTRL+C to exit.")
        self.channel.basic_consume(queue=self.queue, on_message_callback=wrapper_callback)
        try:
            self.channel.start_consuming()
        except KeyboardInterrupt:
            print("Consumer stopped.")
        finally:
            self.connection.close()


# Example usage of the RabbitMQClient
def custom_callback(message_body):
    """Custom callback function for message consumption."""
    print(f"Processing message: {message_body.decode()}")


if __name__ == "__main__":
    # Initialize the RabbitMQ client
    rabbitmq_client = RabbitMQClient(
        host='rabbitmq.rabbitmq.svc.cluster.local',
        exchange='x_exchange',
        queue='x_queue',
        routing_key='x_key',
        usrname='user',
        password='mystic'
    )

    # Step 1: Setup exchange, queue, and binding
    rabbitmq_client.setup()

    # Step 2: Publish messages
    rabbitmq_client.publish("Hello, RabbitMQ!")
    rabbitmq_client.publish("Another test message.")

    # Step 3: Get a single message
    msg = rabbitmq_client.get_message()
    print("Get msg: ", msg)

    # Step 4: Start consuming messages
    # print("Starting consumer...")
    # rabbitmq_client.consume_messages(custom_callback)
