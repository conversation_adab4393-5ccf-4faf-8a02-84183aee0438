# Uncomment following line to config kubeconfig 
# kubeconfig: l40s-kubeconfig

llm:
- api_base: http://llama3-1.default.svc.cluster.local:8000/v1
  model: meta-llama/Llama-3.1-8B-Instruct
  name: llm1

embedding:
- api_base: http://bge-m3.default.svc.cluster.local:8000/v1/embeddings
  model: BAAI/bge-m3

postgresql:
  database_name: knowledge_base
  user: postgres
  password: mystic
  host: postgresql.postgresql.svc.cluster.local
  port: 5432

qdrant:
  host: qdrant.qdrant.svc.cluster.local
  port: 6333

app:
  image: ************:9443/dell/eypoc:0.0.39
  hostname: chat.projectx.eypoc.com