#!/bin/bash


# WARNING WARNING WARNING
# This script is generated by AI from GETTING-STARTED.md
# This script is not validated yet.
# This script is not validated yet.
# This script is not validated yet.
# Please do not run this script directly.
# Please do not run this script directly.
# Please do not run this script directly.
# WARNING WARNING WARNING

# Function to check the command result and capture errors
check_command() {
    if [ $? -ne 0 ]; then
        echo "ERROR: $1 failed."
        exit 1
    else
        echo "$1 succeeded."
    fi
}

# Function to clean up existing resources
cleanup_resources() {
    echo "Cleaning up existing resources..."
    kubectl delete namespace eypoc --ignore-not-found
    check_command "Cleanup existing resources"
}

# Function to initialize the database
initialize_database() {
    echo "Initializing database..."

    # Execute commands in the PostgreSQL container
    kubectl exec -it -n postgresql postgresql-0 -- bash -c "
    PGPASSWORD='mystic' psql -U postgres -c \"DROP DATABASE IF EXISTS knowledge_base;\"
    PGPASSWORD='mystic' psql -U postgres -c \"CREATE DATABASE knowledge_base;\"
    PGPASSWORD='mystic' psql -U postgres -d knowledge_base -c \"CREATE TABLE documents (id SERIAL PRIMARY KEY, knowledge_base VARCHAR(255), doc_id TEXT, filename VARCHAR(255));\"
    "
    check_command "Database initialization"
}

# Function to read configuration from YAML file
read_configuration() {
    local config_file="inputs-getting-started.yaml"  # Use current directory file

    # Read configuration fields
    kubeconfig=$(yq eval '.kubeconfig // ""' $config_file)  # Use empty if not set
    llm_name=$(yq eval '.llm[0].name' $config_file)
    llm_api_base=$(yq eval '.llm[0].api_base' $config_file)
    llm_model=$(yq eval '.llm[0].model' $config_file)
    embedding_api_base=$(yq eval '.embedding[0].api_base' $config_file)
    embedding_model=$(yq eval '.embedding[0].model' $config_file)
    postgresql_db_name=$(yq eval '.postgresql.database_name' $config_file)
    postgresql_db_user=$(yq eval '.postgresql.user' $config_file)
    postgresql_db_password=$(yq eval '.postgresql.password' $config_file)
    postgresql_db_host=$(yq eval '.postgresql.host' $config_file)
    postgresql_db_port=$(yq eval '.postgresql.port' $config_file)
    qdrant_db_host=$(yq eval '.qdrant.host' $config_file)
    qdrant_db_port=$(yq eval '.qdrant.port' $config_file)
    app_image=$(yq eval '.app.image' $config_file)
    app_hostname=$(yq eval '.app.hostname' $config_file)

    # Check for required fields
    for field in llm_name llm_api_base llm_model \
                 embedding_api_base embedding_model postgresql_db_name \
                 postgresql_db_user postgresql_db_password postgresql_db_host \
                 postgresql_db_port qdrant_db_host qdrant_db_port \
                 app_image app_hostname; do
        if [ -z "${!field}" ]; then
            echo "ERROR: Required field '${field}' is missing in the configuration file."
            exit 1
        fi
    done

    # Use custom kubeconfig if specified in the config file
    if [ -n "$kubeconfig" ]; then
        if [ ! -f "${kubeconfig}" ]; then
            echo "Error: specified kubeconfig file does not exist: ${kubeconfig}"
            exit 1
        fi
        echo "Using kubeconfig from config file: ${kubeconfig}"
        export KUBECONFIG="${kubeconfig}"
        # Ensure helm uses the same kubeconfig
        export HELM_KUBECONFIG="${KUBECONFIG}"
    else
        echo "No kubeconfig specified, using default."
    fi
}

# Function to deploy the application
deploy_application() {
    echo "Deploying application..."

    # Create namespace for the application
    kubectl create namespace eypoc || true

    # Deploy the application using existing image
    cat <<EOF | kubectl apply -f -
apiVersion: apps/v1
kind: Deployment
metadata:
  name: chat
  namespace: eypoc
spec:
  replicas: 1
  selector:
    matchLabels:
      app: chat
  template:
    metadata:
      labels:
        app: chat
    spec:
      containers:
      - name: chat
        image: ${app_image}
        ports:
        - containerPort: 5000
        env:
        - name: QDRANT_HOST
          value: '${qdrant_db_host}'
        - name: QDRANT_PORT
          value: '${qdrant_db_port}'
        - name: POSTGRES_DBNAME
          value: '${postgresql_db_name}'
        - name: POSTGRES_USER
          value: '${postgresql_db_user}'
        - name: POSTGRES_PASSWORD
          value: '${postgresql_db_password}'
        - name: POSTGRES_HOST
          value: '${postgresql_db_host}'
        - name: POSTGRES_PORT
          value: '${postgresql_db_port}'
        - name: LLM_CONFIGS
          value: "{\"${llm_name}\":{\"api\":\"${llm_api_base}\",\"model\":\"${llm_model}\"}}"
        - name: EMBEDDING_API_URL
          value: '${embedding_api_base}'
        - name: EMBEDDING_MODEL
          value: '${embedding_model}'
      imagePullSecrets:
      - name: nexus-registry-secret
EOF
    check_command "Application deployment"

    # Expose the application
    kubectl expose deployment chat --type=ClusterIP --port=5000 --namespace=eypoc
    check_command "Expose application"

    # Create Ingress resource
    cat <<EOF | kubectl apply -f -
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: chat-ingress
  namespace: eypoc
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/auth-signin: https://oauth2-proxy.projectx.eypoc.com/oauth2/start?rd=https://\$host\$request_uri
    nginx.ingress.kubernetes.io/auth-url: http://oauth2-proxy.oauth2-proxy.svc.cluster.local/oauth2/auth
    nginx.ingress.kubernetes.io/proxy-body-size: "0"
    nginx.ingress.kubernetes.io/proxy-request-buffering: "off"
    nginx.ingress.kubernetes.io/auth-response-headers: "Authorization,X-Auth-Request-User,X-Auth-Request-Email,X-Auth-Request-Groups,X-Auth-Request-Preferred-Username"
spec:
  tls:
  - hosts:
    - ${app_hostname}
  rules:
  - host: ${app_hostname}
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: chat
            port:
              number: 5000
EOF
    check_command "Ingress creation"

    # Verify application deployment
    verify_deployment
}

# Function to verify application deployment
verify_deployment() {
    echo "Verifying application deployment..."
    sleep 10  # Wait for a few seconds to allow the application to start
    kubectl get pods -n eypoc
    check_command "Verify application deployment"
    echo "Application deployed successfully. Access it at http://chat.projectx.eypoc.com"

    # Define Keycloak and OAuth2 Proxy details
    local KEYCLOAK_HOST="sso.projectx.eypoc.com"  # Replace with your Keycloak host
    local OAUTH2_PROXY_HOST="oauth2-proxy.projectx.eypoc.com"  # Replace with your OAuth2 Proxy host
    local APP_URL="http://chat.projectx.eypoc.com"  # Replace with your application URL

    # Obtain Keycloak admin credentials from environment or config
    local ADMIN_USER="your_admin_user"  # Replace with your Keycloak admin user
    local ADMIN_PASSWORD="your_admin_password"  # Replace with your Keycloak admin password

    # Get OAuth2 Proxy token
    echo "Obtaining OAuth2 Proxy token..."
    local TOKEN=$(curl -s -X POST "https://${KEYCLOAK_HOST}/auth/realms/master/protocol/openid-connect/token" \
        -d "client_id=admin-cli" \
        -d "username=${ADMIN_USER}" \
        -d "password=${ADMIN_PASSWORD}" \
        -d "grant_type=password" | jq -r '.access_token')

    if [ -z "$TOKEN" ]; then
        echo "ERROR: Failed to obtain OAuth2 Proxy token"
        exit 1
    fi

    # Access the application with the obtained token
    echo "Accessing the application..."
    local RESPONSE=$(curl -s -H "Authorization: Bearer ${TOKEN}" "${APP_URL}")

    if echo "$RESPONSE" | grep -q "expected_content"; then  # Replace "expected_content" with a string you expect in the response
        echo "Application is accessible and verified successfully."
    else
        echo "ERROR: Application is not accessible or verification failed."
        exit 1
    fi
}

# Entry point to the script
main() {
    echo "Starting application deployment..."
    read_configuration
    cleanup_resources
    initialize_database
    deploy_application
}

main "$@" 