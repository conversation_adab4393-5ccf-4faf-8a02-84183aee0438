# LLM model
## Spec
    - Inference Server

     

        LLMs are served by [vLLM](https://docs.vllm.ai/en/latest/) [v0.6.4.post1](https://github.com/vllm-project/vllm/releases/tag/v0.6.4.post1)
    - Models

        | Name | GPU Uasage | Link | Comments|
        |:--|:--|:--|:--|
        |Llama-3.3-70B-Instruct| 4 * H100 SXM 80GB| [Link](https://huggingface.co/meta-llama/Llama-3.3-70B-Instruct) | Provides similar performance to Llama 3.1 405B, but at a significantely lower cost |
        |Llama-3-Groq-8B-Tool-Use|1 * H100 SXM 80GB |[Link](https://huggingface.co/Groq/Llama-3-Groq-8B-Tool-Use)| Designed for advanced tool use and function calling tasks |
        |Llama-3.1-8B-Instruct|1 * H100 SXM 80GB|[Link](https://huggingface.co/meta-llama/Llama-3.1-8B-Instruct)| Optimized for multilingual dialogue use cases|
        |bge-m3|1 * H100 SXM 80GB|[Link](https://huggingface.co/BAAI/bge-m3)| For embedding tasks |
        |bge-reranker-v2-m3 |1 * H100 SXM 80GB|[Link](https://huggingface.co/BAAI/bge-reranker-v2-m3)| For reranking tasks |
        
    
        Comparison of performance between models:

        | Model	| Flexible Match Accuracy | Exact Match Accuracy|
        |:--|:--|:--|
        |Llama-3.1-8B-Instruct | 77.71% | 20.39%|
        |Llama-3.3-70B-Instruct| 88.78% | 9.63% |


        Flexible Match: The 70B model performs better, indicating stronger reasoning abilities.

        Exact Match: Surprisingly, the 70B model performs worse, potentially due to more complex or liberal generated answers.

        Overall, the 70B model is suitable for tasks requiring strong reasoning abilities, but further optimization is needed for format-sensitive tasks.

## Installation

### L40s
* Step1：Deploy `Llama-3.1-8B-Instruct`
    
    ```
    # cd Llama-3.1-8B-Instruct
    # kubectl apply -f pvc.yaml
    # kubectl apply -f deployment.yaml
    # kubectl apply -f svc.yaml
    ```
    Verification:
    ```
    root@node1:~# kubectl get po,svc,ep
    NAME                           READY   STATUS    RESTARTS   AGE
    pod/llama3-1-76588c6bf-qhmwg   1/1     Running   0          17h

    NAME                 TYPE        CLUSTER-IP      EXTERNAL-IP   PORT(S)          AGE
    service/kubernetes   ClusterIP   **********      <none>        443/TCP          7d16h
    service/llama3-1     NodePort    *************   <none>        8000:30122/TCP   18h

    NAME                   ENDPOINTS            AGE
    endpoints/kubernetes   *************:6443   7d16h
    endpoints/llama3-1     *************:8000   18h
    ```
* Step2: Deploy `bge-m3`

    ```
    # cd bge-m3
    # kubectl apply -f pvc.yaml
    # kubectl apply -f deployment.yaml
    # kubectl apply -f svc.yaml 
    ```
    Verification:
    ```
    root@node1:~# kubectl get po,svc,ep
    NAME                           READY   STATUS    RESTARTS   AGE
    pod/bge-m3-7c79bf568c-x5sxt    1/1     Running   0          17h
    pod/llama3-1-76588c6bf-qhmwg   1/1     Running   0          17h

    NAME                 TYPE        CLUSTER-IP      EXTERNAL-IP   PORT(S)          AGE
    service/bge-m3       NodePort    ***********     <none>        8000:30125/TCP   17h
    service/kubernetes   ClusterIP   **********      <none>        443/TCP          7d16h
    service/llama3-1     NodePort    *************   <none>        8000:30122/TCP   18h

    NAME                   ENDPOINTS            AGE
    endpoints/bge-m3       ************:8000    17h
    endpoints/kubernetes   *************:6443   7d16h
    endpoints/llama3-1     *************:8000   18h
    ```
