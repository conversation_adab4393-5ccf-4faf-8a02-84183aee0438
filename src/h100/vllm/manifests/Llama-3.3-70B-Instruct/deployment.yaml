apiVersion: apps/v1
kind: Deployment
metadata:
  name: llama-3-3-70b-instruct
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: llama-3-3-70b-instruct
  template:
    metadata:
      labels:
        app: llama-3-3-70b-instruct
    spec:
      containers:
      - command:
        - sh
        - -c
        - python3 -m vllm.entrypoints.openai.api_server --enforce-eager --enable-prefix-caching
          --enable-prefix-caching  --max-num-seqs=512 --max-num-batched-token=32768
          --max-model-len=32768 --model=/models/Llama-3.3-70B-Instruct --tensor-parallel-size=4
          --enable-chunked-prefill --max_num_batched_tokens 1024
        env:
        - name: HUGGING_FACE_HUB_TOKEN
          valueFrom:
            secretKeyRef:
              key: hf_api_token
              name: hf-secret
        image: library/vllm-multihost:eric
        imagePullPolicy: IfNotPresent
        name: vllm
        resources:
          limits:
            nvidia.com/gpu: "4"
        securityContext:
          privileged: true
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /models
          name: llama3-405b
      dnsPolicy: ClusterFirst
      hostIPC: true
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
      volumes:
      - name: llama3-405b
        persistentVolumeClaim:
          claimName: hdml-static-pvc2

