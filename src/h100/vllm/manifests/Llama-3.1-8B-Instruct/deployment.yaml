apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: llama3-1
  name: llama3-1
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: llama3-1
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: llama3-1
    spec:
      containers:
      - args:
        - vllm serve meta-llama/Llama-3.1-8B-Instruct --trust-remote-code --enable-chunked-prefill
          --max_num_batched_tokens 1024
        command:
        - /bin/sh
        - -c
        env:
        - name: HUGGING_FACE_HUB_TOKEN
          valueFrom:
            secretKeyRef:
              key: hf_api_token
              name: hf-secret
        image: vllm/vllm-openai:latest
        imagePullPolicy: Always
        name: llama
        ports:
        - containerPort: 8000
          protocol: TCP
        resources:
          limits:
            cpu: "16"
            nvidia.com/gpu: "1"
          requests:
            cpu: "6"
            memory: 6G
            nvidia.com/gpu: "1"
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /root/.cache/huggingface
          name: cache-volume
        - mountPath: /dev/shm
          name: shm
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
      volumes:
      - name: cache-volume
        persistentVolumeClaim:
          claimName: llama-3-1
      - emptyDir:
          medium: Memory
        name: shm