apiVersion: apps/v1
kind: Deployment
metadata:
  name: llama-3-groq
  namespace: default
  labels:
    app: llama-3-groq
spec:
  replicas: 1
  selector:
    matchLabels:
      app: llama-3-groq
  template:
    metadata:
      labels:
        app: llama-3-groq
    spec:
      volumes:
      - name: cache-volume
        persistentVolumeClaim:
          claimName: llama-3-groq
      # vLLM needs to access the host's shared memory for tensor parallel inference.
      - name: shm
        emptyDir:
          medium: Memory
          sizeLimit: "40Gi"
      containers:
      - name: llama
        image: vllm/vllm-openai:latest
        command: ["/bin/sh", "-c"]
        args: [
          "vllm serve Groq/Llama-3-Groq-8B-Tool-Use --trust-remote-code --enable-chunked-prefill --max_num_batched_tokens 1024"
        ]
        env:
        - name: HUGGING_FACE_HUB_TOKEN
          valueFrom:
            secretKeyRef:
              name: hf-secret
              key: hf_api_token
        ports:
        - containerPort: 8000
        resources:
          limits:
            cpu: "16"
            nvidia.com/gpu: "1"
          requests:
            cpu: "6"
            memory: 6G
            nvidia.com/gpu: "1"
