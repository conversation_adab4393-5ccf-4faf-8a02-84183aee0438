apiVersion: apps/v1
kind: Deployment
metadata:
  name: bge-m3
  namespace: default
  labels:
    app: bge-m3
spec:
  replicas: 1
  selector:
    matchLabels:
      app: bge-m3
  template:
    metadata:
      labels:
        app: bge-m3
    spec:
      volumes:
      - name: cache-volume
        persistentVolumeClaim:
          claimName: bge-m3
      # vLLM needs to access the host's shared memory for tensor parallel inference.
      - name: shm
        emptyDir:
          medium: Memory
      containers:
      - name: llama
        image: vllm/vllm-openai:latest
        command: ["/bin/sh", "-c"]
        args: [
          "vllm serve BAAI/bge-m3 --trust-remote-code --task embedding"
        ]
        env:
        - name: HUGGING_FACE_HUB_TOKEN
          valueFrom:
            secretKeyRef:
              name: hf-secret
              key: hf_api_token
        ports:
        - containerPort: 8000
        resources:
          limits:
            cpu: "16"
            nvidia.com/gpu: "1"
          requests:
            cpu: "6"
            memory: 6G
            nvidia.com/gpu: "1"
        volumeMounts:
        - mountPath: /root/.cache/huggingface
          name: cache-volume
        - name: shm
          mountPath: /dev/shm
