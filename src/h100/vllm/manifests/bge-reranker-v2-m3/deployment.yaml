apiVersion: apps/v1
kind: Deployment
metadata:
  name: bge-reranker-v2-m3
  namespace: default
  labels:
    app: bge-reranker-v2-m3
spec:
  replicas: 1
  selector:
    matchLabels:
      app: bge-reranker-v2-m3
  template:
    metadata:
      labels:
        app: bge-reranker-v2-m3
    spec:
      volumes:
      - name: cache-volume
        persistentVolumeClaim:
          claimName: bge-reranker-v2-m3
      # vLLM needs to access the host's shared memory for tensor parallel inference.
      - name: shm
        emptyDir:
          medium: Memory
      containers:
      - name: llama
        image: public.ecr.aws/q9t5s3a7/vllm-ci-postmerge-repo:d5c5154fcf4c5d65551c98e458cbb027e5f4b672
        command: ["/bin/sh", "-c"]
        args: [
          "vllm serve BAAI/bge-reranker-v2-m3 --trust-remote-code --task embedding"
        ]
        env:
        - name: HUGGING_FACE_HUB_TOKEN
          valueFrom:
            secretKeyRef:
              name: hf-secret
              key: hf_api_token
        ports:
        - containerPort: 8000
        resources:
          limits:
            cpu: "16"
            nvidia.com/gpu: "1"
          requests:
            cpu: "6"
            memory: 6G
            nvidia.com/gpu: "1"
        volumeMounts:
        - mountPath: /root/.cache/huggingface
          name: cache-volume
        - name: shm
          mountPath: /dev/shm
